<div class="bg-white">
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="max-w-xl mx-auto text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>
            <h2 class="mt-2 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">Thank you!</h2>
            <p class="mt-2 text-base text-gray-500">Your order has been placed and will be processed shortly.</p>
            <p class="mt-2 text-sm text-gray-500">Transaction ID: {{ $transaction_id }}</p>

            <div class="mt-10">
                <a href="{{ route('home') }}" class="text-base font-medium text-black hover:text-gray-700">Continue Shopping<span aria-hidden="true"> &rarr;</span></a>
            </div>
        </div>

        <div class="mt-16">
            <h3 class="text-lg font-medium text-gray-900">Order Details</h3>
            <div class="mt-6 border-t border-gray-200">
                <dl class="divide-y divide-gray-200">
                    @foreach ($orders as $order)
                        <div class="py-6">
                            <div class="flex justify-between items-center">
                                <h4 class="text-base font-medium text-gray-900">Order #{{ $order->order_number }}</h4>
                                <p class="text-sm text-gray-500">Sold by: {{ $order->vendor->business_name ?? 'N/A' }}</p>
                            </div>

                            <div class="mt-4 flow-root">
                                <ul role="list" class="-my-6 divide-y divide-gray-200">
                                    @foreach ($order->items as $item)
                                        <li class="py-6 flex">
                                            <div class="ml-4 flex-1 flex flex-col">
                                                <div>
                                                    <div class="flex justify-between text-base font-medium text-gray-900">
                                                        <h3>
                                                            {{ optional(json_decode($item->product_data))->name ?? 'Product not found' }}
                                                        </h3>
                                                        <p class="ml-4">₦{{ number_format($item->price, 2) }}</p>
                                                    </div>
                                                    <p class="mt-1 text-sm text-gray-500">Qty: {{ $item->quantity }}</p>
                                                </div>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>

                            <div class="mt-6">
                                <dl class="text-sm font-medium text-gray-500">
                                    <div class="flex justify-between">
                                        <dt>Subtotal</dt>
                                        <dd class="text-gray-900">₦{{ number_format($order->subtotal, 2) }}</dd>
                                    </div>
                                    <div class="flex justify-between mt-2">
                                        <dt>Shipping</dt>
                                        <dd class="text-gray-900">₦{{ number_format($order->shipping_cost, 2) }}</dd>
                                    </div>
                                    <div class="flex justify-between mt-2 pt-2 border-t border-gray-200">
                                        <dt class="text-base text-gray-900">Total</dt>
                                        <dd class="text-base text-gray-900">₦{{ number_format($order->total, 2) }}</dd>
                                    </div>
                                </dl>
                            </div>
                            @if($order->shipping_tracking_url)
                                <div class="mt-6">
                                    <a href="{{ $order->shipping_tracking_url }}" target="_blank" class="w-full bg-black border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Track Order</a>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </dl>
            </div>
        </div>

        @if ($orders->isNotEmpty())
            @php $firstOrder = $orders->first(); @endphp
            <div class="mt-16">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Shipping Information</h3>
                        @php $address = json_decode($firstOrder->shipping_address); @endphp
                        @if ($address)
                            <div class="mt-6 text-sm text-gray-600">
                                <p>{{ $address->first_name }} {{ $address->last_name }}</p>
                                <p>{{ $address->address }}</p>
                                <p>{{ $address->city }}, {{ $address->state }} {{ $address->postal_code }}</p>
                                <p>{{ $address->country }}</p>
                                <p class="mt-4">{{ $address->email }}</p>
                                <p>{{ $address->phone }}</p>
                            </div>
                        @endif
                    </div>
                    <div class="mt-16 md:mt-0">
                        <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
                        <div class="mt-6 text-sm text-gray-600">
                            <p><strong>Payment Method:</strong> {{ str_replace('_', ' ', ucwords($firstOrder->payment_method)) }}</p>
                            <p class="mt-2"><strong>Payment Status:</strong>
                                @if($firstOrder->payment_status === 'paid')
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">{{ ucfirst($firstOrder->payment_status) }}</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
