<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\Gate;
use App\Models\Product;
use App\Policies\ProductPolicy;
use App\Models\SubscriptionPlan;
use App\Policies\SubscriptionPlanPolicy;
use App\Models\Order;
use App\Policies\OrderPolicy;
use App\Policies\VendorPolicy;
use App\Models\Review;
use App\Policies\ReviewPolicy;
use App\Models\VendorSubscription;
use App\Policies\SubscriptionPolicy;
use Illuminate\Support\ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Product::class => ProductPolicy::class,
        SubscriptionPlan::class => SubscriptionPlanPolicy::class,
        Order::class => OrderPolicy::class,
        Vendor::class => VendorPolicy::class,
        Review::class => ReviewPolicy::class,
        VendorSubscription::class => SubscriptionPolicy::class,
    ];
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Gate::define('manage-vendor-finances', function (User $user, Vendor $vendor) {
            // Admins can manage any vendor's finances.
            if ($user->hasRole('admin')) {
                return true;
            }

            // Vendors can only manage their own finances.
            if ($user->hasRole('vendor')) {
                return $user->id === $vendor->user_id;
            }

            return false;
        });

        // SECURITY FIX: Gate for role assignment authorization
        Gate::define('assign-role', function (User $user, int $roleId) {
            // Only admins can assign roles
            if (!$user->hasRole('admin')) {
                return false;
            }

            // Get the role being assigned
            $role = \App\Models\Role::find($roleId);
            if (!$role) {
                return false;
            }

            // Prevent assignment of admin role unless user is super admin
            // For now, all admins can assign any role, but this can be restricted further
            $restrictedRoles = ['admin'];

            // Additional security: Log role assignment attempts
            \Log::info('Role assignment attempt', [
                'admin_user_id' => $user->id,
                'admin_email' => $user->email,
                'target_role_id' => $roleId,
                'target_role_name' => $role->name,
                'timestamp' => now()
            ]);

            return true;
        });

        // SECURITY FIX: Gate for user management authorization
        Gate::define('manage-users', function (User $user) {
            return $user->hasRole('admin');
        });
    }
}
