<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $this->call([
            RoleSeeder::class,
        ]);

        // Create admin user
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $vendorRole = Role::firstOrCreate(['name' => 'vendor']);
        $customerRole = Role::firstOrCreate(['name' => 'customer']);

        $admin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
        ]);

        // Assign role separately to avoid mass assignment protection
        $admin->role_id = $adminRole->id;
        $admin->save();

        // Create vendor user
        $vendorUser = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Vendor User',
            'password' => Hash::make('password'),
        ]);

        // Assign role separately to avoid mass assignment protection
        $vendorUser->role_id = $vendorRole->id;
        $vendorUser->save();

        // Create vendor profile for vendor user
        Vendor::firstOrCreate([
            'user_id' => $vendorUser->id,
        ], [
            'shop_name' => 'Demo Vendor Shop',
            'slug' => 'demo-vendor-shop',
            'business_name' => 'Demo Vendor Shop',
            'business_address' => '123 Demo Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
            'phone' => '+2348000000000',
            'is_approved' => true,
            'has_completed_onboarding' => true, // CRITICAL FIX: Set onboarding as completed
        ]);
        
        // Run the seeders in proper order
        $this->call([
            BrandSeeder::class,
            SubscriptionPlanSeeder::class,
            CategorySeeder::class, // Create categories before products
            ColorSeeder::class,
            SizeSeeder::class,
            VendorSeeder::class,
            ProductSeeder::class,
            ProductVariantSeeder::class, // Create variants after products
            CustomerSeeder::class, // Create customers before orders
            OrderSeeder::class, // Create orders last
        ]);
    }
}
