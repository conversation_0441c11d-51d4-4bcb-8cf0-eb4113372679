<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class VendorOrdersController extends Controller
{
    /**
     * Get orders by state with statistics for the vendor dashboard map
     * This is optimized for performance by using caching and efficient queries
     */
    public function getOrdersByState(Request $request)
    {
        // Get authenticated user's vendor
        $user = auth()->user();
        $vendor = $user->vendor;
        
        if (!$vendor) {
            return response()->json([
                'error' => 'Vendor not found for this user'
            ], 404);
        }
        
        $timeframe = $request->query('timeframe', 30);

        // Create a unique cache key based on vendor and timeframe
        $cacheKey = "vendor_{$vendor->id}_orders_by_state_{$timeframe}";
        
        // Try to get from cache first (10 minute cache)
        return Cache::remember($cacheKey, 600, function() use ($vendor, $timeframe) {
            // Try to get real orders if they exist
            try {
                $query = Order::query()
                    ->join('order_items', 'orders.id', '=', 'order_items.order_id')
                    ->join('products', 'order_items.product_id', '=', 'products.id')
                    ->where('products.vendor_id', $vendor->id)
                    ->where('orders.payment_status', 'paid');
                
                // Apply timeframe filter
                if ($timeframe !== 'all') {
                    $query->where('orders.created_at', '>=', Carbon::now()->subDays($timeframe));
                }
                
                // Check if we have shipping_state column or shipping_address JSON
                if (Schema::hasColumn('orders', 'shipping_state')) {
                    $query->select(
                        'shipping_state as state',
                        DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                        DB::raw('SUM(order_items.price * order_items.quantity) as order_value')
                    )
                    ->whereNotNull('shipping_state')
                    ->groupBy('shipping_state');
                } else {
                    // SECURITY FIX: Use JSON_EXTRACT to prevent SQL injection and properly group by the state field within the JSON object.
                    $stateExpression = DB::raw("JSON_UNQUOTE(JSON_EXTRACT(shipping_address, '$.state'))");

                    $query->select(
                        $stateExpression . ' as state',
                        DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                        DB::raw('SUM(order_items.price * order_items.quantity) as order_value')
                    )
                    ->whereNotNull('shipping_address')
                    ->groupBy($stateExpression);
                }
                
                $ordersByState = $query->get();
                
                // If we have real data, use it
                if (count($ordersByState) > 0) {
                    $stateOrders = [];
                    $statesDetails = [];
                    
                    foreach ($ordersByState as $stateData) {
                        $stateName = $stateData->state;
                        
                        // If it's JSON encoded, try to decode it
                        if (is_string($stateName) && $this->isJson($stateName)) {
                            $decodedState = json_decode($stateName, true);
                            $stateName = is_array($decodedState) && isset($decodedState['state']) ? $decodedState['state'] : $stateName;
                        }
                        
                        // Skip if state name is not valid
                        if (!$stateName || !is_string($stateName)) {
                            continue;
                        }
                        
                        // Normalize state names to match the map widget
                        $normalizedState = $this->normalizeStateName($stateName);
                        
                        // Add to orders by state array for the map
                        $stateOrders[$normalizedState] = (int) $stateData->order_count;
                        
                        // Add detailed data for the table
                        $statesDetails[] = [
                            'state' => $normalizedState,
                            'orders' => (int) $stateData->order_count,
                            'value' => (float) $stateData->order_value,
                        ];
                    }
                    
                    return [
                        'orders_by_state' => $stateOrders,
                        'states_details' => $statesDetails,
                        'timeframe' => $timeframe
                    ];
                }
            } catch (\Exception $e) {
                // FIXED: Log the error and return empty data instead of simulated data
                \Log::error('Error fetching vendor order data', [
                    'vendor_id' => $vendor->id,
                    'error' => $e->getMessage(),
                    'timeframe' => $timeframe
                ]);
            }

            // FIXED: Return empty data instead of simulated data for production
            return [
                'orders_by_state' => [],
                'states_details' => [],
                'timeframe' => $timeframe,
                'message' => 'No order data available for the selected timeframe.'
            ];
        });
    }
    
    /**
     * Check if a string is valid JSON
     */
    private function isJson($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
    
    /**
     * Normalize state names to ensure consistency with the map visualization
     */
    private function normalizeStateName($stateName)
    {
        // Remove any extra spaces and title case the state name
        $normalized = ucwords(trim($stateName));
        
        // Handle common abbreviations and alternate spellings for Nigerian states
        $stateMap = [
            'FCT' => 'FCT',
            'F.C.T' => 'FCT',
            'Federal Capital Territory' => 'FCT',
            'Abuja' => 'FCT',
            'Akwa-Ibom' => 'Akwa Ibom',
            'Akwaibom' => 'Akwa Ibom',
            'Cross-River' => 'Cross River',
            'Crossriver' => 'Cross River'
        ];
        
        return $stateMap[$normalized] ?? $normalized;
    }
}
