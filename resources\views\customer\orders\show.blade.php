@extends('layouts.customer')

@section('customer-content')
<div>
    {{-- Header --}}
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold">Order #{{ $order->order_number }}</h1>
            <p class="text-sm text-gray-500 mt-1">Order placed on {{ $order->created_at->format('M d, Y') }}</p>
        </div>
        <a href="{{ route('orders.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-800 border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Orders
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {{-- Main Content (Left/Top) --}}
        <div class="lg:col-span-2 space-y-8">
            {{-- Order Details --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-bold">Order Details</h3>
                    @php
                        $statusColors = [
                            'pending' => 'bg-yellow-100 text-yellow-800',
                            'processing' => 'bg-blue-100 text-blue-800',
                            'shipped' => 'bg-indigo-100 text-indigo-800',
                            'completed' => 'bg-green-100 text-green-800',
                            'cancelled' => 'bg-red-100 text-red-800',
                            'return_requested' => 'bg-purple-100 text-purple-800',
                            'returned' => 'bg-gray-100 text-gray-800',
                        ];
                    @endphp
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full {{ $statusColors[$order->status] ?? 'bg-gray-100 text-gray-800' }}">
                        {{ str_replace('_', ' ', Str::title($order->status)) }}
                    </span>
                </div>
                <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Shipping Address</h4>
                        @php $shippingAddr = $order->formatted_shipping_address; @endphp
                        <address class="not-italic text-gray-600">
                            @if($shippingAddr['name'])
                                <div class="font-medium text-gray-900">{{ $shippingAddr['name'] }}</div>
                            @endif
                            @if($shippingAddr['address'])
                                <div>{{ $shippingAddr['address'] }}</div>
                            @endif
                            @if($shippingAddr['city'] || $shippingAddr['state'] || $shippingAddr['postal_code'])
                                <div>
                                    {{ $shippingAddr['city'] }}@if($shippingAddr['city'] && ($shippingAddr['state'] || $shippingAddr['postal_code'])),@endif
                                    {{ $shippingAddr['state'] }} {{ $shippingAddr['postal_code'] }}
                                </div>
                            @endif
                            @if($shippingAddr['lga'])
                                <div class="text-sm text-gray-500">LGA: {{ $shippingAddr['lga'] }}</div>
                            @endif
                            @if($shippingAddr['country'])
                                <div>{{ $shippingAddr['country'] }}</div>
                            @endif
                            @if($shippingAddr['phone'])
                                <div class="mt-2">
                                    <i class="fas fa-phone-alt mr-2"></i>{{ $shippingAddr['phone'] }}
                                </div>
                            @endif
                            @if(!$shippingAddr['name'] && !$shippingAddr['address'] && !$shippingAddr['city'])
                                <div class="text-gray-500 italic">No shipping address available</div>
                            @endif
                        </address>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Payment Information</h4>
                        <div class="text-gray-600 space-y-1">
                            <p><strong>Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                            <p><strong>Status:</strong>
                                @if($order->payment_status === 'paid')
                                    <span class="text-green-600 font-semibold">Paid</span>
                                @elseif($order->payment_status === 'pending')
                                    <span class="text-yellow-600 font-semibold">Pending</span>
                                @else
                                    <span class="text-red-600 font-semibold">Failed</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Order Items --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold">Order Items ({{ $order->items->count() }})</h3>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach($order->items as $item)
                        <div class="p-6 flex items-start space-x-4">
                            <img src="{{ $item->product->image_url ?? 'https://via.placeholder.com/100x100?text=' . urlencode($item->product_name) }}" alt="{{ $item->product_name }}" class="w-24 h-24 object-cover rounded-md">
                            <div class="flex-1">
                                <h4 class="font-bold text-gray-800">{{ $item->product_name }}</h4>
                                <p class="text-sm text-gray-500">Vendor: {{ $item->vendor->shop_name ?? 'N/A' }}</p>
                                <p class="text-sm text-gray-500">Qty: {{ $item->quantity }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-lg">₦{{ number_format($item->subtotal, 2) }}</p>
                                <p class="text-sm text-gray-500">₦{{ number_format($item->unit_price, 2) }} each</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        {{-- Sidebar (Right/Bottom) --}}
        <div class="lg:col-span-1 space-y-8">
            {{-- Order Summary --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold">Order Summary</h3>
                </div>
                <div class="p-6 space-y-3 text-gray-600">
                    @php
                        // Calculate subtotal from order items
                        $subtotal = $order->items->sum(function($item) {
                            return $item->price * $item->quantity;
                        });
                        $shippingCost = $order->shipping_cost ?? 0;
                        $total = $order->total;
                    @endphp
                    <div class="flex justify-between"><span>Subtotal</span> <span>₦{{ number_format($subtotal, 2) }}</span></div>
                    <div class="flex justify-between"><span>Shipping</span> <span>₦{{ number_format($shippingCost, 2) }}</span></div>
                    <div class="border-t border-gray-200 pt-4 mt-4">
                        <div class="flex justify-between font-bold text-lg text-gray-800">
                            <span>Total</span>
                            <span>₦{{ number_format($total, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Order Notes --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold">Order Notes</h3>
                </div>
                <div class="p-6 text-gray-600">
                    @if($order->notes)
                        <p>{{ $order->notes }}</p>
                    @else
                        <p class="text-gray-400">No notes for this order.</p>
                    @endif
                </div>
            </div>

            {{-- Order Timeline --}}
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold">Order Timeline</h3>
                </div>
                <div class="p-6">
                    <ol class="relative border-l border-gray-200">
                        <li class="mb-10 ml-4">
                            <div class="absolute w-3 h-3 bg-green-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->created_at->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Order Placed</h3>
                        </li>
                        @if($order->payment_status === 'paid')
                        <li class="mb-10 ml-4">
                            <div class="absolute w-3 h-3 bg-green-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->paid_at ? $order->paid_at->format('M d, Y h:i A') : $order->created_at->addMinutes(5)->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Payment Confirmed</h3>
                        </li>
                        @endif
                        @if(in_array($order->status, ['processing', 'shipped', 'completed']))
                        <li class="mb-10 ml-4">
                            <div class="absolute w-3 h-3 bg-green-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->created_at->addHours(2)->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Order Processing</h3>
                        </li>
                        @endif
                        @if(in_array($order->status, ['shipped', 'completed']))
                        <li class="mb-10 ml-4">
                            <div class="absolute w-3 h-3 bg-green-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->shipped_at ? $order->shipped_at->format('M d, Y h:i A') : $order->created_at->addDays(1)->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Order Shipped</h3>
                        </li>
                        @endif
                        @if($order->status === 'completed')
                        <li class="ml-4">
                            <div class="absolute w-3 h-3 bg-green-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->delivered_at ? $order->delivered_at->format('M d, Y h:i A') : $order->created_at->addDays(3)->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Order Delivered</h3>
                        </li>
                        @endif
                        @if($order->status === 'cancelled')
                        <li class="ml-4">
                            <div class="absolute w-3 h-3 bg-red-500 rounded-full mt-1.5 -left-1.5 border border-white"></div>
                            <time class="mb-1 text-sm font-normal leading-none text-gray-400">{{ $order->cancelled_at ? $order->cancelled_at->format('M d, Y h:i A') : $order->updated_at->format('M d, Y h:i A') }}</time>
                            <h3 class="text-lg font-semibold text-gray-900">Order Cancelled</h3>
                        </li>
                        @endif
                    </ol>
                </div>
            </div>
        </div>
    </div>



    {{-- Request Return Modal --}}
    <div x-show="returnModalOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="returnModalOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="returnModalOpen = false" aria-hidden="true"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div x-show="returnModalOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block w-full max-w-lg p-8 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
                <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                    Request Order Return
                </h3>
                
                <form action="{{ route('orders.return', $order) }}" method="POST" class="mt-4 space-y-4">
                    @csrf
                    <div>
                        <label for="return_reason" class="block text-sm font-medium text-gray-700">Return Reason</label>
                        <textarea id="return_reason" name="return_reason" rows="4" required class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Please explain the reason for returning this order..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button @click="returnModalOpen = false" type="button" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-md hover:bg-yellow-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-yellow-500">
                            Submit Return Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
