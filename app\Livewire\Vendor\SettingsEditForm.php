<?php

namespace App\Livewire\Vendor;

use App\Models\Vendor;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;

class SettingsEditForm extends Component
{
    use WithFileUploads;

    public Vendor $vendor;

    // Vendor settings fields
    public $shop_name;
    public $business_name;
    public $business_address;
    public $city;
    public $state;
    public $country;
    public $phone;
    public $about;
    public $facebook_url;
    public $twitter_url;
    public $instagram_url;
    public $website_url;
    public $logo;
    public $banner;

    // UI state
    public $saving = false;

    public function mount(Vendor $vendor)
    {
        $this->vendor = $vendor;

        // Populate form fields
        $this->shop_name = $vendor->shop_name;
        $this->business_name = $vendor->business_name ?? '';
        $this->business_address = $vendor->business_address ?? '';
        $this->city = $vendor->city ?? '';
        $this->state = $vendor->state ?? '';
        $this->country = $vendor->country ?? '';
        $this->phone = $vendor->phone ?? '';
        $this->about = $vendor->about ?? '';
        $this->facebook_url = $vendor->facebook_url ?? '';
        $this->twitter_url = $vendor->twitter_url ?? '';
        $this->instagram_url = $vendor->instagram_url ?? '';
        $this->website_url = $vendor->website_url ?? '';
    }

    protected function rules()
    {
        return [
            'shop_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'business_address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'phone' => 'nullable|string|regex:/^0[789][01]\d{8}$/',
            'about' => 'nullable|string|max:5000',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'website_url' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'banner' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ];
    }

    protected function messages()
    {
        return [
            'shop_name.required' => 'Shop name is required.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., ***********).',
            'facebook_url.url' => 'Please enter a valid Facebook URL.',
            'twitter_url.url' => 'Please enter a valid Twitter URL.',
            'instagram_url.url' => 'Please enter a valid Instagram URL.',
            'website_url.url' => 'Please enter a valid website URL.',
            'logo.image' => 'Logo must be an image file.',
            'logo.max' => 'Logo file size cannot exceed 2MB.',
            'banner.image' => 'Banner must be an image file.',
            'banner.max' => 'Banner file size cannot exceed 2MB.',
        ];
    }

    public function updatedShopName($value)
    {
        $this->validateOnly('shop_name');
    }

    public function updatedPhone($value)
    {
        $this->validateOnly('phone');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            // Update vendor settings
            $this->vendor->update([
                'shop_name' => $this->shop_name,
                'business_name' => $this->business_name,
                'business_address' => $this->business_address,
                'city' => $this->city,
                'state' => $this->state,
                'country' => $this->country,
                'phone' => $this->phone,
                'about' => $this->about,
                'facebook_url' => $this->facebook_url,
                'twitter_url' => $this->twitter_url,
                'instagram_url' => $this->instagram_url,
                'website_url' => $this->website_url,
            ]);

            // Handle logo upload
            if ($this->logo) {
                $this->vendor->clearMediaCollection('logo');
                $this->vendor->addMedia($this->logo->getRealPath())
                    ->usingName($this->vendor->shop_name . ' Logo')
                    ->toMediaCollection('logo');
            }

            // Handle banner upload
            if ($this->banner) {
                $this->vendor->clearMediaCollection('banner');
                $this->vendor->addMedia($this->banner->getRealPath())
                    ->usingName($this->vendor->shop_name . ' Banner')
                    ->toMediaCollection('banner');
            }

            $this->dispatch('settings-updated');
            session()->flash('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update settings: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.vendor.settings-edit-form');
    }
}
