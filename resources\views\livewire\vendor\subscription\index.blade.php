{{-- MOBILE RESPONSIVENESS FIX: Proper navbar spacing and mobile-first design --}}
<div class="bg-gradient-to-br from-gray-50 to-white">
    {{-- Mobile-First Header with proper spacing --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl shadow-2xl mb-6 sm:mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div class="space-y-2">
                <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Subscription Management
                </h1>
                <p class="text-gray-300 text-base sm:text-lg">Manage your subscription and billing</p>
            </div>
            <div class="flex space-x-2 sm:space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-4 py-3 sm:px-6 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2 min-h-[44px] min-w-[44px] justify-center">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span class="hidden sm:inline">Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-check-circle mr-3"></i>
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-exclamation-circle mr-3"></i>
            {{ session('error') }}
        </div>
    @endif

    @if (session()->has('info'))
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-info-circle mr-3"></i>
            {{ session('info') }}
        </div>
    @endif

    {{-- MOBILE RESPONSIVENESS FIX: Mobile-first subscription status card --}}
    <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-6 sm:mb-8">
        <div class="p-4 sm:p-6 lg:p-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-2 sm:space-y-0">
                <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Current Status</h2>
                <div class="px-3 py-2 sm:px-4 rounded-full text-sm font-semibold self-start sm:self-auto
                    @if($subscriptionStatus['status'] === 'free_trial') bg-blue-100 text-blue-800
                    @elseif($subscriptionStatus['status'] === 'active') bg-green-100 text-green-800
                    @else bg-red-100 text-red-800 @endif">
                    @if($subscriptionStatus['status'] === 'free_trial') Free Trial
                    @elseif($subscriptionStatus['status'] === 'active') Active Subscription
                    @elseif($subscriptionStatus['status'] === 'subscription_required') Subscription Required
                    @elseif($subscriptionStatus['status'] === 'subscription_expired') Subscription Expired
                    @endif
                </div>
            </div>

            {{-- MOBILE RESPONSIVENESS FIX: Mobile-first grid with proper spacing --}}
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {{-- Orders Count --}}
                <div class="text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                    <div class="text-2xl sm:text-3xl font-bold text-blue-600 mb-2">{{ $orderCount }}</div>
                    <div class="text-sm text-gray-600">Total Orders</div>
                    @if($orderCount < 10)
                        <div class="text-xs text-blue-600 mt-1">{{ 10 - $orderCount }} free orders remaining</div>
                    @endif
                </div>

                {{-- Subscription Status --}}
                <div class="text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                    @if($subscription && $subscription->status === 'active')
                        <div class="text-2xl sm:text-3xl font-bold text-green-600 mb-2">₦{{ number_format($subscription->plan->price) }}</div>
                        <div class="text-sm text-gray-600">{{ $subscription->plan->name ?? 'Monthly Plan' }}</div>
                        <div class="text-xs text-green-600 mt-1">{{ $subscriptionStatus['days_remaining'] ?? 0 }} days remaining</div>
                    @else
                        <div class="text-2xl sm:text-3xl font-bold text-gray-400 mb-2">—</div>
                        <div class="text-sm text-gray-600">No Active Plan</div>
                    @endif
                </div>

                {{-- Next Action --}}
                <div class="text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl sm:col-span-2 lg:col-span-1">
                    @if($subscriptionStatus['can_receive_orders'])
                        <div class="text-3xl font-bold text-green-600 mb-2">✓</div>
                        <div class="text-sm text-gray-600">Can Receive Orders</div>
                    @else
                        <div class="text-3xl font-bold text-red-600 mb-2">✗</div>
                        <div class="text-sm text-gray-600">Orders Blocked</div>
                    @endif
                </div>
            </div>

            <div class="mt-6 p-4 bg-gray-50 rounded-xl">
                <p class="text-gray-700 text-center">{{ $subscriptionStatus['message'] }}</p>
            </div>

            @if($subscription && $subscription->status === 'active')
                <div class="mt-6 text-center">
                    <button wire:click="cancel"
                            wire:confirm="Are you sure you want to cancel your subscription? This action cannot be undone."
                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 transform hover:scale-105">
                        Cancel Subscription
                    </button>
                </div>
            @endif
        </div>
    </div>

    {{-- Available Subscription Plans --}}
    <div class="space-y-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Choose Your Plan</h2>
            <p class="text-gray-600 mb-4">Select the perfect plan for your business needs</p>
            <div class="inline-flex items-center bg-green-50 border border-green-200 rounded-lg px-4 py-2">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="text-green-700 text-sm font-medium">Save up to 6% with annual billing</span>
            </div>
        </div>

        {{-- MOBILE RESPONSIVENESS FIX: Mobile-first subscription plans grid --}}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            @foreach($allPlans as $plan)
                <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden {{ $this->isCurrentPlan($plan->id) ? 'ring-2 sm:ring-4 ring-green-500' : '' }} transition-all duration-300 hover:shadow-2xl">
                    {{-- Plan Header --}}
                    <div class="p-4 sm:p-6 {{ $this->isCurrentPlan($plan->id) ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white' : 'bg-gradient-to-r from-gray-50 to-white' }}">
                        <div class="text-center">
                            <h3 class="text-xl sm:text-2xl font-bold {{ $this->isCurrentPlan($plan->id) ? 'text-white' : 'text-gray-900' }} mb-2">
                                {{ $plan->name }}
                            </h3>
                            @if($this->isCurrentPlan($plan->id))
                                <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white text-green-600">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    Current Plan
                                </div>
                            @endif
                        </div>
                    </div>

                    {{-- Plan Pricing - CRITICAL FIX: Correct price display without division --}}
                    <div class="p-6 text-center border-b border-gray-100">
                        @if($plan->interval === 'monthly')
                            <div class="text-5xl font-bold text-black mb-2">₦{{ number_format($plan->price) }}</div>
                            <div class="text-gray-600 text-lg">per month</div>
                        @elseif($plan->interval === 'bi-annually')
                            <div class="text-5xl font-bold text-black mb-2">₦{{ number_format($plan->price) }}</div>
                            <div class="text-gray-600 text-lg">per month</div>
                            <div class="text-sm text-green-600 font-medium mt-1">Billed every 6 months (₦{{ number_format($plan->price * 6) }})</div>
                            <div class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full mt-2">
                                3% savings vs monthly
                            </div>
                        @elseif($plan->interval === 'annually')
                            <div class="text-5xl font-bold text-black mb-2">₦{{ number_format($plan->price) }}</div>
                            <div class="text-gray-600 text-lg">per month</div>
                            <div class="text-sm text-green-600 font-medium mt-1">Billed annually (₦{{ number_format($plan->price * 12) }})</div>
                            <div class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full mt-2">
                                6% savings vs monthly
                            </div>
                        @endif

                        @if($plan->order_limit)
                            <div class="text-sm text-gray-500 mt-3">Up to {{ $plan->order_limit }} orders</div>
                        @else
                            <div class="text-sm text-gray-500 mt-3">Unlimited orders</div>
                        @endif
                    </div>

                    {{-- Plan Features --}}
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">What's included:</h4>
                        <div class="space-y-3">
                            @foreach(json_decode($plan->features, true) as $feature)
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 text-xs"></i>
                                        </div>
                                    </div>
                                    <span class="text-gray-700 text-sm">{{ $feature }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- MOBILE RESPONSIVENESS FIX: Plan Action with proper touch targets --}}
                    <div class="p-4 sm:p-6 bg-gray-50">
                        @if($this->isCurrentPlan($plan->id))
                            <div class="bg-green-100 text-green-800 py-3 px-4 rounded-xl text-center font-medium min-h-[44px] flex items-center justify-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                You're subscribed to this plan
                            </div>
                        @elseif($subscriptionStatus['status'] === 'free_trial' && $plan->price > 0)
                            <button wire:click="subscribeToPlan({{ $plan->id }})"
                                    wire:loading.attr="disabled"
                                    class="w-full bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black text-white font-bold py-3 px-4 sm:px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl min-h-[44px] flex items-center justify-center">
                                <span wire:loading.remove wire:target="subscribeToPlan({{ $plan->id }})" class="flex items-center">
                                    <i class="fas fa-rocket mr-2"></i>
                                    Subscribe Now
                                </span>
                                <span wire:loading wire:target="subscribeToPlan({{ $plan->id }})" class="flex items-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    Processing...
                                </span>
                            </button>
                            <p class="text-sm text-gray-500 mt-2 text-center">You can subscribe anytime during your free trial</p>
                        @elseif($subscriptionStatus['status'] !== 'active' && $plan->price > 0)
                            <button wire:click="subscribeToPlan({{ $plan->id }})"
                                    wire:loading.attr="disabled"
                                    class="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <span wire:loading.remove wire:target="subscribeToPlan({{ $plan->id }})">
                                    <i class="fas fa-unlock mr-2"></i>
                                    Subscribe to Continue
                                </span>
                                <span wire:loading wire:target="subscribeToPlan({{ $plan->id }})">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    Processing...
                                </span>
                            </button>
                            <p class="text-sm text-red-600 mt-2 text-center">Subscribe now to continue receiving orders</p>
                        @elseif($plan->price == 0)
                            <div class="bg-blue-100 text-blue-800 py-3 px-4 rounded-xl text-center font-medium">
                                <i class="fas fa-gift mr-2"></i>
                                Free Plan (First 10 orders)
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
