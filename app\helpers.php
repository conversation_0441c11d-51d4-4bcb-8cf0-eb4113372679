<?php

if (!function_exists('format_currency')) {
    /**
     * Format currency with Nigerian Naira symbol
     *
     * @param float|int|string $amount
     * @param int $decimals
     * @return string
     */
    function format_currency($amount, $decimals = 2)
    {
        $symbol = config('brandify.currency.symbol', '₦');
        return $symbol . number_format(floatval($amount), $decimals, '.', ',');
    }
}

if (!function_exists('format_currency_whole')) {
    /**
     * Format currency with Nigerian Naira symbol (no decimals)
     *
     * @param float|int|string $amount
     * @return string
     */
    function format_currency_whole($amount)
    {
        return format_currency($amount, 0);
    }
}

if (!function_exists('format_price')) {
    /**
     * Alias for format_currency for backward compatibility
     *
     * @param float|int|string $amount
     * @param int $decimals
     * @return string
     */
    function format_price($amount, $decimals = 2)
    {
        return format_currency($amount, $decimals);
    }
}

if (!function_exists('calculate_commission')) {
    /**
     * Calculate commission amount based on the configured rate
     *
     * @param float|int $amount
     * @param float|null $rate
     * @return float
     */
    function calculate_commission($amount, $rate = null)
    {
        $commissionRate = $rate ?? config('brandify.commission_rate', 0.027);
        return floatval($amount) * $commissionRate;
    }
}

if (!function_exists('format_commission')) {
    /**
     * Calculate and format commission with currency symbol
     *
     * @param float|int $amount
     * @param float|null $rate
     * @return string
     */
    function format_commission($amount, $rate = null)
    {
        $commission = calculate_commission($amount, $rate);
        return format_currency($commission);
    }
}

if (!function_exists('get_commission_rate_percentage')) {
    /**
     * Get commission rate as a percentage string
     *
     * @param float|null $rate
     * @return string
     */
    function get_commission_rate_percentage($rate = null)
    {
        $commissionRate = $rate ?? config('brandify.commission_rate', 0.027);
        return number_format($commissionRate * 100, 1) . '%';
    }
}
