<?php

namespace App\Livewire\Vendor\Products;

use App\Livewire\Shared\ProductForm as BaseProductForm;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Color;
use App\Models\Product;
use App\Models\Size;
use App\Models\Vendor;

use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Str;
use Livewire\Attributes\Layout;

#[Layout('layouts.vendor')]
class CreateProduct extends BaseProductForm
{
    // CRITICAL FIX: Individual properties for proper Livewire binding
    public $name = '';
    public $description = '';
    public $price;
    public $discount_price;
    public $stock;
    public $sku = '';
    public $category_id;
    public $brand_id;
    public $is_active = true;
    public $weight;
    public $length;
    public $width;
    public $height;

    // Vendor-specific properties
    public $colors;
    public $sizes;
    public $vendor;
    public $vendorBrand;

    // Dropdown data properties
    public $categories = [];
    public $brands = [];

    // UI state properties
    public $isLoading = false;

    public function mount()
    {
        $this->product = new Product();
        $this->product->is_active = true; // Default to active
        
        // Get vendor and vendor's brand
        $user = Auth::user();
        if (!$user || !$user->vendor) {
            abort(403, 'Access denied. Vendor account required.');
        }
        $this->vendor = $user->vendor;
        $this->vendorBrand = $this->vendor->brand;
        
        // Set up dropdown data
        $this->categories = Category::all();
        $this->brands = Brand::all();
        $this->colors = Color::orderBy('name')->get();
        $this->sizes = Size::orderBy('name')->get();
        
        // Initialize with one empty specification (but no variants by default)
        $this->addSpecification();

        // Initialize variant state
        $this->hasVariants = false;

        // Debug logging
        \Log::info('CreateProduct mounted', [
            'product_attributes' => $this->product->getAttributes(),
            'vendor_id' => $this->vendor->id,
            'categories_count' => $this->categories->count(),
            'colors_count' => $this->colors->count(),
            'sizes_count' => $this->sizes->count()
        ]);
    }

    /**
     * Get the validation rules for the product form
     *
     * @return array
     */
    protected function rules()
    {
        return array_merge($this->getProductRules(), [
            'product.category_id' => 'required|exists:categories,id',
            'variants.*.image' => 'nullable|image|max:2048',
        ]);
    }

    /**
     * Get the validation rules for the product form
     *
     * @return array
     */
    protected function getProductRules()
    {
        return array_merge(parent::getProductRules(), [
            'product.category_id' => 'required|exists:categories,id',
            'variants.*.image' => 'nullable|image|max:2048',
        ]);
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    protected function messages()
    {
        return array_merge(parent::getMessages(), [
            'product.category_id.required' => 'Please select a category.',
            'product.category_id.exists' => 'Please select a valid category.',
        ]);
    }

    /**
     * CRITICAL FIX: Populate product model from individual properties
     */
    private function populateProductFromProperties()
    {
        $this->product->name = $this->name;
        $this->product->description = $this->description;
        $this->product->price = $this->price;
        $this->product->discount_price = $this->discount_price;
        $this->product->stock = $this->stock;
        $this->product->sku = $this->sku;
        $this->product->category_id = $this->category_id;
        $this->product->brand_id = $this->brand_id;
        $this->product->is_active = $this->is_active;
        $this->product->weight = $this->weight;
        $this->product->length = $this->length;
        $this->product->width = $this->width;
        $this->product->height = $this->height;
    }

    /**
     * Save the product and related data
     */
    public function save()
    {
        // Set loading state
        $this->isLoading = true;

        try {
            // Explicit authorization check
            $this->authorize('create', Product::class);

            // Debug validation data
            \Log::info('Validating product data', [
                'product_data' => $this->product->toArray(),
                'variants_count' => count($this->variants),
                'specifications_count' => count($this->specifications)
            ]);

            $this->validate();

            // CRITICAL FIX: Populate product model from individual properties
            $this->populateProductFromProperties();

            // Set vendor and brand
            $this->product->vendor_id = $this->vendor->id;
            if ($this->vendorBrand) {
                $this->product->brand_id = $this->vendorBrand->id;
            }

            // Generate slug
            $this->product->slug = Str::slug($this->name) . '-' . uniqid();

            // Save the product and handle media
            $this->saveProduct();

            // Save variants and specifications
            $this->saveVariants($this->product);
            $this->saveSpecifications($this->product);

            session()->flash('success', 'Product created successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors for debugging
            \Log::warning('Product validation failed', [
                'vendor_id' => $this->vendor->id,
                'errors' => $e->errors(),
                'product_data' => $this->product->toArray()
            ]);

            // Re-throw validation exceptions so Livewire can handle them
            $this->isLoading = false;
            throw $e;
        } catch (\Exception $e) {
            $this->isLoading = false;
            \Log::error('Error creating product', [
                'vendor_id' => $this->vendor->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'An error occurred while creating the product. Please try again.');
            return;
        } finally {
            $this->isLoading = false;
        }
    }



    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.vendor.products.create-product');
    }
}