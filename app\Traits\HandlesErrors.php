<?php

namespace App\Traits;

use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * SECURITY FIX: Standardized error handling trait for consistent error management
 */
trait HandlesErrors
{
    /**
     * Handle and log exceptions with consistent formatting
     */
    protected function handleException(\Exception $e, string $context = '', array $additionalData = []): void
    {
        $errorData = [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'context' => $context,
            'user_id' => auth()->id(),
            'timestamp' => now(),
            'trace' => $e->getTraceAsString()
        ];

        // Merge additional data
        $errorData = array_merge($errorData, $additionalData);

        // Log based on exception type
        if ($e instanceof ValidationException) {
            Log::info('Validation error occurred', $errorData);
        } elseif ($e instanceof \InvalidArgumentException) {
            Log::warning('Invalid argument provided', $errorData);
        } elseif ($e instanceof \UnauthorizedHttpException) {
            Log::warning('Unauthorized access attempt', $errorData);
        } else {
            Log::error('Unexpected error occurred', $errorData);
        }
    }

    /**
     * Flash error message to session with consistent formatting
     */
    protected function flashError(string $message, string $type = 'error'): void
    {
        session()->flash($type, $message);
    }

    /**
     * Flash success message to session
     */
    protected function flashSuccess(string $message): void
    {
        $this->flashError($message, 'success');
    }

    /**
     * Flash warning message to session
     */
    protected function flashWarning(string $message): void
    {
        $this->flashError($message, 'warning');
    }

    /**
     * Flash info message to session
     */
    protected function flashInfo(string $message): void
    {
        $this->flashError($message, 'info');
    }

    /**
     * Handle validation errors with user-friendly messages
     */
    protected function handleValidationError(ValidationException $e, string $context = ''): void
    {
        $this->handleException($e, $context);
        
        $errors = $e->validator->errors()->all();
        $message = 'Please correct the following errors: ' . implode(', ', $errors);
        
        $this->flashError($message);
    }

    /**
     * Handle authorization errors
     */
    protected function handleAuthorizationError(string $action = '', array $additionalData = []): void
    {
        $message = $action ? "You are not authorized to {$action}." : 'You are not authorized to perform this action.';
        
        $this->handleException(
            new \Exception('Authorization failed: ' . $action),
            'authorization_check',
            $additionalData
        );
        
        $this->flashError($message);
    }

    /**
     * Handle business logic errors
     */
    protected function handleBusinessLogicError(string $message, array $additionalData = []): void
    {
        $this->handleException(
            new \Exception('Business logic error: ' . $message),
            'business_logic',
            $additionalData
        );
        
        $this->flashError($message);
    }

    /**
     * Handle API errors with retry logic
     */
    protected function handleApiError(\Exception $e, string $apiName = '', int $retryCount = 0): void
    {
        $errorData = [
            'api_name' => $apiName,
            'retry_count' => $retryCount,
            'response_code' => method_exists($e, 'getCode') ? $e->getCode() : null
        ];

        $this->handleException($e, 'api_error', $errorData);
        
        $message = $apiName 
            ? "There was an issue with the {$apiName} service. Please try again later."
            : 'There was an issue with an external service. Please try again later.';
            
        $this->flashError($message);
    }

    /**
     * Handle database errors
     */
    protected function handleDatabaseError(\Exception $e, string $operation = ''): void
    {
        $errorData = [
            'operation' => $operation,
            'sql_state' => method_exists($e, 'getSQLState') ? $e->getSQLState() : null
        ];

        $this->handleException($e, 'database_error', $errorData);
        
        $message = $operation 
            ? "Unable to complete {$operation}. Please try again."
            : 'A database error occurred. Please try again.';
            
        $this->flashError($message);
    }

    /**
     * Handle file upload errors
     */
    protected function handleFileUploadError(\Exception $e, string $filename = ''): void
    {
        $errorData = [
            'filename' => $filename,
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size')
        ];

        $this->handleException($e, 'file_upload_error', $errorData);
        
        $message = $filename 
            ? "Failed to upload file '{$filename}'. Please check the file size and format."
            : 'File upload failed. Please check the file size and format.';
            
        $this->flashError($message);
    }

    /**
     * Handle payment processing errors
     */
    protected function handlePaymentError(\Exception $e, array $paymentData = []): void
    {
        // Sanitize payment data for logging (remove sensitive info)
        $sanitizedData = array_intersect_key($paymentData, array_flip([
            'amount', 'currency', 'reference', 'status', 'gateway'
        ]));

        $this->handleException($e, 'payment_error', $sanitizedData);
        
        $this->flashError('Payment processing failed. Please try again or contact support if the issue persists.');
    }

    /**
     * Handle commission calculation errors
     */
    protected function handleCommissionError(\Exception $e, array $orderData = []): void
    {
        $sanitizedData = array_intersect_key($orderData, array_flip([
            'order_id', 'vendor_id', 'total_amount', 'commission_rate'
        ]));

        $this->handleException($e, 'commission_calculation_error', $sanitizedData);
        
        $this->flashError('There was an issue processing the commission. Our team has been notified.');
    }

    /**
     * Check if user has permission and handle authorization gracefully
     */
    protected function authorizeOrFail(string $ability, $resource = null, string $action = ''): bool
    {
        try {
            $this->authorize($ability, $resource);
            return true;
        } catch (\Exception $e) {
            $this->handleAuthorizationError($action, [
                'ability' => $ability,
                'resource_type' => is_object($resource) ? get_class($resource) : gettype($resource)
            ]);
            return false;
        }
    }

    /**
     * Execute operation with error handling
     */
    protected function executeWithErrorHandling(callable $operation, string $context = '', array $errorData = [])
    {
        try {
            return $operation();
        } catch (ValidationException $e) {
            $this->handleValidationError($e, $context);
            return false;
        } catch (\Exception $e) {
            $this->handleException($e, $context, $errorData);
            $this->flashError('An unexpected error occurred. Please try again.');
            return false;
        }
    }
}
