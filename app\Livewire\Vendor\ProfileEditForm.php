<?php

namespace App\Livewire\Vendor;

use App\Models\Vendor;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class ProfileEditForm extends Component
{
    use WithFileUploads;

    public Vendor $vendor;
    public User $user;

    // User fields
    public $name;
    public $email;
    public $current_password;
    public $password;
    public $password_confirmation;

    // Vendor fields
    public $business_name;
    public $business_description;
    public $business_address;
    public $phone;
    public $logo;
    public $banner;

    // UI state
    public $saving = false;

    public function mount(Vendor $vendor, User $user)
    {
        $this->vendor = $vendor;
        $this->user = $user;

        // Populate user fields
        $this->name = $user->name;
        $this->email = $user->email;

        // Populate vendor fields
        $this->business_name = $vendor->business_name ?? '';
        $this->business_description = $vendor->business_description ?? '';
        $this->business_address = $vendor->business_address ?? '';
        $this->phone = $vendor->phone ?? '';
    }

    protected function rules()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $this->user->id,
            'business_name' => 'nullable|string|max:255',
            'business_description' => 'nullable|string|max:1000',
            'business_address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|regex:/^0[789][01]\d{8}$/',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'banner' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ];

        // Password validation only if provided
        if ($this->password) {
            $rules['current_password'] = 'required';
            $rules['password'] = 'string|min:8|confirmed';
        }

        return $rules;
    }

    protected function messages()
    {
        return [
            'name.required' => 'Full name is required.',
            'email.required' => 'Email address is required.',
            'email.unique' => 'This email address is already taken.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., ***********).',
            'current_password.required' => 'Current password is required to change password.',
            'password.min' => 'New password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'logo.image' => 'Logo must be an image file.',
            'logo.max' => 'Logo file size cannot exceed 2MB.',
            'banner.image' => 'Banner must be an image file.',
            'banner.max' => 'Banner file size cannot exceed 2MB.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function updatedEmail($value)
    {
        $this->validateOnly('email');
    }

    public function updatedPhone($value)
    {
        $this->validateOnly('phone');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            // Verify current password if changing password
            if ($this->password) {
                if (!Hash::check($this->current_password, $this->user->password)) {
                    $this->addError('current_password', 'The provided password does not match your current password.');
                    $this->saving = false;
                    return;
                }
            }

            // Update user information
            $userData = [
                'name' => $this->name,
                'email' => $this->email,
            ];

            if ($this->password) {
                $userData['password'] = Hash::make($this->password);
            }

            $this->user->update($userData);

            // Update vendor information
            $this->vendor->update([
                'business_name' => $this->business_name,
                'business_description' => $this->business_description,
                'business_address' => $this->business_address,
                'phone' => $this->phone,
            ]);

            // Handle logo upload
            if ($this->logo) {
                $this->vendor->clearMediaCollection('logo');
                $this->vendor->addMedia($this->logo->getRealPath())
                    ->usingName($this->vendor->shop_name . ' Logo')
                    ->toMediaCollection('logo');
            }

            // Handle banner upload
            if ($this->banner) {
                $this->vendor->clearMediaCollection('banner');
                $this->vendor->addMedia($this->banner->getRealPath())
                    ->usingName($this->vendor->shop_name . ' Banner')
                    ->toMediaCollection('banner');
            }

            // Clear password fields
            $this->current_password = '';
            $this->password = '';
            $this->password_confirmation = '';

            $this->dispatch('profile-updated');
            session()->flash('success', 'Profile updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update profile: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.vendor.profile-edit-form');
    }
}
