<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // FINANCIAL LOGIC FIX: Add missing vendor financial state fields
            $table->decimal('total_earnings', 12, 2)->default(0.00)->after('balance')
                  ->comment('Sum of all confirmed payments (lifetime earnings)');
            
            $table->decimal('pending_balance', 12, 2)->default(0.00)->after('total_earnings')
                  ->comment('Recently received funds with 7-14 day hold period');
            
            $table->decimal('available_balance', 12, 2)->default(0.00)->after('pending_balance')
                  ->comment('Funds available for immediate withdrawal');
            
            $table->decimal('withdrawn_amount', 12, 2)->default(0.00)->after('available_balance')
                  ->comment('Total amount successfully withdrawn');
            
            $table->timestamp('last_balance_update')->nullable()->after('withdrawn_amount')
                  ->comment('Timestamp of last balance calculation for audit purposes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn([
                'total_earnings',
                'pending_balance', 
                'available_balance',
                'withdrawn_amount',
                'last_balance_update'
            ]);
        });
    }
};
