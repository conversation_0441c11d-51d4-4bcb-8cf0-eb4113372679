// AJAX Product Search for Shop Page

document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded - initializing shop features');
    
    // Initialize search functionality
    initializeSearch();
    
    // initializeCartQuantityUpdates(); // This is likely handled by cart.js or needs review
});

function initializeSearch() {
    const searchForm = document.querySelector('form[action*="search"]');
    const searchInput = searchForm ? searchForm.querySelector('input[name="query"]') : null;
    const productsRow = document.querySelector('.row.g-4');
    const paginationDiv = document.querySelector('.mt-5.d-flex.justify-content-center');

    console.log('Search components:', { 
        formFound: !!searchForm, 
        inputFound: !!searchInput, 
        productsRowFound: !!productsRow 
    });

    if (searchForm && searchInput && productsRow) {
        searchForm.addEventListener('submit', function (e) {
            e.preventDefault();
            console.log('Search form submitted');
            
            const query = searchInput.value.trim();
            if (!query) {
                console.log('Empty search query, not submitting');
                return;
            }
            
            // Show loading indicator
            productsRow.innerHTML = '<div class="col-12 text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            
            fetch(`/search?query=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Search results received:', data);
                productsRow.innerHTML = '';
                
                if (!data.products || data.products.length === 0) {
                    productsRow.innerHTML = '<div class="col-12 text-center"><div class="alert alert-warning">No products found.</div></div>';
                } else {
                    data.products.forEach(product => {
                        const price = product.price ? parseFloat(product.price).toFixed(2) : '0.00';
                        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '';
                        
                        productsRow.innerHTML += `
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                <div class="card h-100 shadow-sm border-0">
                                    <img src="${product.image_url || 'https://via.placeholder.com/300x300?text=No+Image'}" class="card-img-top" alt="${product.name}">
                                    <div class="card-body d-flex flex-column">
                                        <h5 class="card-title fw-bold mb-1">${product.name}</h5>
                                        <p class="card-text mb-1 text-muted small">Vendor: <span class="fw-semibold">${product.vendor_name || 'N/A'}</span></p>
                                        <p class="card-text mb-1 text-muted small">Category: ${product.category_name || 'N/A'}</p>
                                        <div class="mt-auto">
                                            <div class="fw-bold fs-5 mb-3">$${price}</div>
                                            <form action="/cart/add/${product.id}" method="POST" class="add-to-cart-form">
                                                <input type="hidden" name="_token" value="${csrfToken}">
                                                <button type="submit" class="btn btn-dark w-100">Add to Cart</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    // Re-initialize cart buttons for the newly added products
                    // initializeAddToCart(); // Removed
                }
                
                if (paginationDiv) {
                    paginationDiv.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                productsRow.innerHTML = `<div class="col-12 text-center"><div class="alert alert-danger">Error loading products: ${error.message}</div></div>`;
            });
        });
    } else {
        console.warn('Search form or required elements not found on this page');
    }
}

function initializeCartQuantityUpdates() {
    const quantityForms = document.querySelectorAll('.cart-update-form');
    
    console.log('Initializing', quantityForms.length, 'cart quantity forms');
    
    quantityForms.forEach(form => {
        const quantityInput = form.querySelector('input[name="quantity"]');
        if (!quantityInput) return;
        
        quantityInput.addEventListener('change', function() {
            console.log('Cart quantity changed, auto-submitting form');
            form.submit();
        });
    });
    
    // Handle cart removals
    const removeButtons = document.querySelectorAll('.cart-remove-button');
    
    removeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (confirm('Are you sure you want to remove this item?')) {
                return true; // Continue with form submission
            } else {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * Shows a Bootstrap Toast notification.
 * @param {string} message The message to display.
 * @param {string} type 'success', 'error', 'info', or 'warning'. Defaults to 'info'.
 */
function showNotification(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        console.error('Toast container not found. Cannot display notification.');
        alert(message); // Fallback to alert
        return;
    }

    const toastId = 'toast-' + Date.now();
    let toastHeaderClass = 'text-dark'; // Default for info/warning
    let toastIconHtml = '<i class="fas fa-info-circle me-2"></i>';

    switch (type.toLowerCase()) {
        case 'success':
            toastHeaderClass = 'text-white bg-success';
            toastIconHtml = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'error':
            toastHeaderClass = 'text-white bg-danger';
            toastIconHtml = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
        case 'warning':
            toastHeaderClass = 'text-dark bg-warning';
            toastIconHtml = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
    }

    const toastHtml = `
        <div class="toast" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}" data-bs-delay="5000">
            <div class="toast-header ${toastHeaderClass}">
                ${toastIconHtml}
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close ${type === 'success' || type === 'error' ? 'btn-close-white' : ''}" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    
    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });

    toast.show();
}
