<!-- Dashboard Sidebar -->
<div class="bg-white rounded-lg shadow-md h-full">
    <div class="p-6">
        <!-- User Info -->
        <div class="text-center pb-4 border-b border-gray-200">
            <div class="mx-auto h-20 w-20 rounded-full bg-black text-white flex items-center justify-center mb-3">
                <span class="text-3xl font-bold">{{ substr(auth()->user()->name, 0, 1) }}</span>
            </div>
            <h5 class="font-bold text-lg">{{ auth()->user()->name }}</h5>
            <p class="text-gray-500 text-sm">{{ auth()->user()->email }}</p>
        </div>

        <!-- Navigation -->
        <nav class="mt-6 space-y-1">
            <a href="{{ route('dashboard') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard') ? 'bg-black text-white' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                <i class="fas fa-tachometer-alt fa-fw mr-3 text-gray-400 group-hover:text-gray-500"></i>
                <span>Dashboard</span>
            </a>
            <a href="{{ route('orders.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('orders.*') ? 'bg-black text-white' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                <i class="fas fa-shopping-bag fa-fw mr-3 text-gray-400 group-hover:text-gray-500"></i>
                <span>My Orders</span>
                @if(auth()->user()->orders()->where('status', 'pending')->count() > 0)
                    <span class="ml-auto inline-block py-0.5 px-3 text-xs rounded-full bg-yellow-400 text-black">{{ auth()->user()->orders()->where('status', 'pending')->count() }}</span>
                @endif
            </a>
            <a href="{{ route('wishlist.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('wishlist.*') ? 'bg-black text-white' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                <i class="fas fa-heart fa-fw mr-3 text-gray-400 group-hover:text-gray-500"></i>
                <span>Wishlist</span>
                @if(auth()->user()->wishlist()->count() > 0)
                    <span class="ml-auto inline-block py-0.5 px-3 text-xs rounded-full bg-red-500 text-white">{{ auth()->user()->wishlist()->count() }}</span>
                @endif
            </a>

            <hr class="my-4 border-gray-200">

            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" class="group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 hover:text-red-700">
                    <i class="fas fa-sign-out-alt fa-fw mr-3 text-red-500 group-hover:text-red-600"></i>
                    <span>Logout</span>
                </button>
            </form>
        </nav>

        <!-- Quick Stats -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <h6 class="font-bold text-lg">{{ auth()->user()->orders()->count() }}</h6>
                    <small class="text-gray-500">Orders</small>
                </div>
                <div>
                    <h6 class="font-bold text-lg">₦{{ number_format(auth()->user()->orders()->sum('total'), 0) }}</h6>
                    <small class="text-gray-500">Spent</small>
                </div>
                <div>
                    <h6 class="font-bold text-lg">{{ auth()->user()->wishlist()->count() }}</h6>
                    <small class="text-gray-500">Saved</small>
                </div>
            </div>
        </div>
    </div>
</div>
