<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class CartController extends Controller
{
    public function add(Product $product)
    {
        // CRITICAL FIX: Add comprehensive validation matching Livewire components

        // Check if product is active
        if (!$product->is_active) {
            return redirect()->back()->with('error', 'This product is currently unavailable.');
        }

        $cart = session()->get('cart', []);
        $requestedQuantity = 1;

        // Calculate total quantity if product already in cart
        if (isset($cart[$product->id])) {
            $requestedQuantity = $cart[$product->id]['quantity'] + 1;
        }

        // Check stock availability
        if (!$product->hasSufficientStock($requestedQuantity)) {
            $availableStock = $product->getStockLevel();
            return redirect()->back()->with('error', "Sorry, only {$availableStock} units of {$product->name} are available.");
        }

        // Use current price from database to prevent manipulation
        $currentPrice = $product->getCurrentPrice();

        if(isset($cart[$product->id])) {
            // Double-check stock before incrementing
            if (!$product->hasSufficientStock($cart[$product->id]['quantity'] + 1)) {
                return redirect()->back()->with('error', 'Insufficient stock to add more of this item.');
            }

            $cart[$product->id]['quantity']++;
            // Update price to current price
            $cart[$product->id]['price'] = $currentPrice;
        } else {
            $cart[$product->id] = [
                "name" => $product->name,
                "quantity" => 1,
                "price" => $currentPrice,
                "image" => $product->image_url ?? asset('images/product-placeholder.svg'),
                "vendor_id" => $product->vendor_id,
                "sku" => $product->sku,
            ];
        }

        session()->put('cart', $cart);

        // Log cart additions for analytics
        \Log::info('Product added to cart via controller', [
            'product_id' => $product->id,
            'product_name' => $product->name,
            'quantity' => $requestedQuantity,
            'price' => $currentPrice,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'ip_address' => request()->ip()
        ]);

        return redirect()->back()->with('success', "{$product->name} has been added to your cart.");
    }
}
