<?php

namespace App\Livewire\Admin\Settings;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

#[Layout('layouts.admin')]
class Index extends Component
{
    public string $site_name = '';
    public string $contact_email = '';
    public string $support_phone = '';

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $this->site_name = config('app.name');
        $this->contact_email = config('mail.from.address');
        $this->support_phone = config('settings.support_phone', '');
    }

    protected function rules(): array
    {
        return [
            'site_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255',
            'support_phone' => 'nullable|string|max:255',
        ];
    }

    public function save()
    {
        $this->validate();

        // SECURITY FIX: Instead of directly modifying .env file, use cache/database
        // Store settings in cache for runtime use
        cache()->put('app.settings', [
            'site_name' => $this->site_name,
            'contact_email' => $this->contact_email,
            'support_phone' => $this->support_phone,
        ], now()->addDays(30));

        // TODO: Consider creating a Settings model to store these in database
        // This is much safer than modifying .env files directly

        session()->flash('success', 'Settings updated successfully. Note: For permanent changes, update your .env file manually.');

        return redirect()->route('admin.settings.index');
    }

    // SECURITY FIX: Removed dangerous updateEnvFile method
    // Direct .env file modification is a critical security vulnerability

    public function render()
    {
        return view('livewire.admin.settings.index')
            ->layout('layouts.admin');
    }
}
