<?php

namespace App\Livewire\Vendor\Products;

use App\Models\Product;
use App\Models\Variant;
use App\Models\Specification;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithFileUploads;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProductForm extends Component
{
    use WithFileUploads;

    public Product $product;
    public $image;
    public $gallery = [];
    public array $variants = [];
    public array $specifications = [];
    public array $mediaToDelete = [];

    public function mount(Product $product = null)
    {
        $this->product = $product ?? new Product();
        if ($this->product->exists) {
            foreach ($this->product->variants as $variant) {
                $this->variants[] = [
                    'id' => $variant->id,
                    'name' => $variant->name,
                    'value' => $variant->value,
                    'price' => $variant->price,
                ];
            }
            foreach ($this->product->specifications as $specification) {
                $this->specifications[] = [
                    'id' => $specification->id,
                    'key' => $specification->key,
                    'value' => $specification->value,
                ];
            }
        }
    }

    /**
     * CRITICAL FIX DV1/FV1: Use unified validation rules from parent
     * This ensures consistency across all product forms
     */
    protected function rules()
    {
        return array_merge(
            $this->getProductRules(), // Use unified rules from parent
            [
                'product.category_id' => 'required|exists:categories,id',
                'variants.*.image' => 'nullable|image|max:2048',
            ]
        );
    }

    public function addVariant()
    {
        $this->variants[] = ['name' => '', 'value' => '', 'price' => null];
    }

    public function removeVariant($index)
    {
        if (isset($this->variants[$index]['id'])) {
            $this->mediaToDelete[] = $this->variants[$index]['id'];
        }
        unset($this->variants[$index]);
        $this->variants = array_values($this->variants);
    }

    public function addSpecification()
    {
        $this->specifications[] = ['key' => '', 'value' => ''];
    }

    public function removeSpecification($index)
    {
        if (isset($this->specifications[$index]['id'])) {
            $this->mediaToDelete[] = $this->specifications[$index]['id'];
        }
        unset($this->specifications[$index]);
        $this->specifications = array_values($this->specifications);
    }

    public function save()
    {
        $this->validate();

        DB::transaction(function () {
            $vendor = auth()->user()?->vendor;
            $this->product->vendor_id = $vendor->id;

            if ($vendor?->brand) {
                $this->product->brand_id = $vendor->brand->id;
            }

            if (!$this->product->exists) {
                $this->product->slug = Str::slug($this->product->name) . '-' . uniqid();
            }

            $this->product->save();

            // Handle main image
            if ($this->image) {
                $this->product->clearMediaCollection('product_images');
                $this->product->addMedia($this->image->getRealPath())
                    ->usingName($this->product->name)
                    ->usingFileName($this->image->getClientOriginalName())
                    ->toMediaCollection('product_images');
            }

            // Handle gallery images
            foreach ($this->gallery as $image) {
                $this->product->addMedia($image->getRealPath())
                    ->usingName($this->product->name)
                    ->toMediaCollection('product_gallery');
            }

            // Delete media marked for deletion
            Media::whereIn('id', $this->mediaToDelete)->delete();

            // Handle variants
            $variantIds = [];
            foreach ($this->variants as $variantData) {
                $variant = $this->product->variants()->updateOrCreate(
                    ['id' => $variantData['id'] ?? null],
                    $variantData
                );
                $variantIds[] = $variant->id;
            }
            // Delete variants not in the list
            $this->product->variants()->whereNotIn('id', $variantIds)->delete();

            // Handle specifications
            $specificationIds = [];
            foreach ($this->specifications as $specificationData) {
                $specification = $this->product->specifications()->updateOrCreate(
                    ['id' => $specificationData['id'] ?? null],
                    $specificationData
                );
                $specificationIds[] = $specification->id;
            }
            // Delete specifications not in the list
            $this->product->specifications()->whereNotIn('id', $specificationIds)->delete();
        });

        session()->flash('success', 'Product saved successfully.');

        return redirect()->route('vendor.products.index');
    }

    public function deleteMedia($mediaId)
    {
        $media = Media::find($mediaId);
        if ($media) {
            $media->delete();
            $this->mediaToDelete[] = $mediaId;
        }
    }

    public function render()
    {
        return view('livewire.vendor.products.product-form');
    }
}
