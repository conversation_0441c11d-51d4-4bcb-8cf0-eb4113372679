<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VendorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        \Log::info('VendorMiddleware Check Started', [
            'user_id' => auth()->id(),
            'is_authenticated' => auth()->check(),
            'is_vendor' => auth()->check() ? auth()->user()->isVendor() : false,
            'route' => $request->route()?->getName(),
            'url' => $request->url(),
            'timestamp' => now()->toISOString()
        ]);

        if (!auth()->check() || !auth()->user()->isVendor()) {
            \Log::warning('VendorMiddleware Access Denied', [
                'user_id' => auth()->id(),
                'is_authenticated' => auth()->check(),
                'is_vendor' => auth()->check() ? auth()->user()->isVendor() : false,
                'route' => $request->route()?->getName(),
                'url' => $request->url(),
                'ip_address' => $request->ip()
            ]);
            abort(403, 'Unauthorized action.');
        }

        \Log::info('VendorMiddleware Access Granted', [
            'user_id' => auth()->id(),
            'route' => $request->route()?->getName()
        ]);

        return $next($request);
    }
}
