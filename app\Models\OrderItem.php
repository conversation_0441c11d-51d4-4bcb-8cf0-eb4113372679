<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'vendor_id',
        'product_id',
        'product_name',
        'product_sku',
        'quantity',
        'price',
        'unit_price',
        'subtotal',
        'total',
        'status',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    /**
     * Boot function to handle model events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-calculate subtotal when saving
        static::saving(function ($orderItem) {
            if (empty($orderItem->unit_price)) {
                $orderItem->unit_price = $orderItem->price;
            }

            if (empty($orderItem->subtotal)) {
                $orderItem->subtotal = $orderItem->price * $orderItem->quantity;
            }

            // PERFORMANCE FIX: Only load product if not already loaded and product_name is empty
            if (empty($orderItem->product_name) && $orderItem->product_id) {
                // Check if product is already loaded to avoid N+1 query
                if ($orderItem->relationLoaded('product') && $orderItem->product) {
                    $orderItem->product_name = $orderItem->product->name;
                } elseif (!$orderItem->relationLoaded('product')) {
                    // Only load product if absolutely necessary
                    $product = Product::find($orderItem->product_id);
                    if ($product) {
                        $orderItem->product_name = $product->name;
                    }
                }
            }
        });
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get formatted price with Naira symbol
     */
    public function getFormattedPriceAttribute()
    {
        return '₦' . number_format($this->price, 2);
    }

    /**
     * Get formatted unit price with Naira symbol
     */
    public function getFormattedUnitPriceAttribute()
    {
        return '₦' . number_format($this->unit_price, 2);
    }

    /**
     * Get formatted subtotal with Naira symbol
     */
    public function getFormattedSubtotalAttribute()
    {
        return '₦' . number_format($this->subtotal, 2);
    }
}
