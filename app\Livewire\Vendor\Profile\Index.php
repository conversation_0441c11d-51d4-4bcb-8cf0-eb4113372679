<?php

namespace App\Livewire\Vendor\Profile;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithFileUploads;

    public Vendor $vendor;
    public User $user;

    // Form fields
    public $name;
    public $email;
    public $business_name;
    public $business_address;
    public $business_description;
    public $phone;
    public $logoUrl;
    public $logo; // For file upload

    // Password fields
    public $current_password;
    public $password;
    public $password_confirmation;

    public function mount()
    {
        $this->user = Auth::user();
        $this->vendor = $this->user->vendor;

        $this->name = $this->user->name;
        $this->email = $this->user->email;

        $this->business_name = $this->vendor->business_name;
        $this->business_address = $this->vendor->business_address;
        $this->business_description = $this->vendor->business_description;
        $this->phone = $this->vendor->phone;
        $this->logoUrl = $this->vendor->logo_url; // Use the accessor method
    }

    public function save()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $this->user->id,
            'business_name' => 'required|string|max:255',
            'business_address' => 'required|string|max:500',
            'business_description' => 'required|string|max:1000',
            'phone' => 'required|string|max:20',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|min:8|confirmed',
        ];

        $this->validate($rules);

        // Update user information
        $this->user->name = $this->name;
        $this->user->email = $this->email;

        // Update password if provided
        if (!empty($this->password)) {
            if (!Hash::check($this->current_password, $this->user->password)) {
                $this->addError('current_password', 'The provided password does not match your current password.');
                return;
            }
            $this->user->password = Hash::make($this->password);
        }

        $this->user->save();

        // Update vendor information first
        $this->vendor->business_name = $this->business_name;
        $this->vendor->business_address = $this->business_address;
        $this->vendor->business_description = $this->business_description;
        $this->vendor->phone = $this->phone;
        $this->vendor->save();

        // Process logo if uploaded (after vendor is saved)
        if ($this->logo) {
            try {
                // Clear existing logo from media library
                $this->vendor->clearMediaCollection('logo');

                // FIXED: Use addMedia() for Livewire file uploads instead of addMediaFromFile()
                $this->vendor->addMedia($this->logo->getRealPath())
                    ->usingName($this->vendor->shop_name . ' Logo')
                    ->usingFileName($this->logo->getClientOriginalName())
                    ->toMediaCollection('logo');

                // Update the logo URL for display
                $this->logoUrl = $this->vendor->logo_url;

                // Reset the logo property to clear the temporary file
                $this->logo = null;
            } catch (\Exception $e) {
                session()->flash('error', 'Failed to upload logo: ' . $e->getMessage());
                return;
            }
        }

        $this->reset(['current_password', 'password', 'password_confirmation', 'logo']);

        session()->flash('success', 'Profile updated successfully.');
    }

    public function render()
    {
        return view('livewire.vendor.profile.index');
    }
}
