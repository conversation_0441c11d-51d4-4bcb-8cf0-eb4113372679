<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\SubscriptionPlan;
use App\Services\VendorEarningsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SystematicRemediationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->vendor = User::factory()->create(['role' => 'vendor']);
        $this->customer = User::factory()->create(['role' => 'customer']);
    }

    /** @test */
    public function phase_1_critical_fixes_are_working()
    {
        // F1: processOrderEarnings method exists
        $this->assertTrue(method_exists(VendorEarningsService::class, 'processOrderEarnings'));
        
        // F3: Vendor financial states work
        $vendor = Vendor::factory()->create(['user_id' => $this->vendor->id]);
        $this->assertNotNull($vendor->total_earnings);
        $this->assertNotNull($vendor->available_balance);
        $this->assertNotNull($vendor->pending_balance);
    }

    /** @test */
    public function phase_2_business_logic_fixes_are_working()
    {
        // DV1: Product validation is unified
        $this->actingAs($this->admin);
        
        $response = $this->post(route('admin.products.store'), [
            'name' => 'Test Product',
            'description' => '', // Should fail validation
            'price' => 100,
            'stock' => 10,
        ]);
        
        $response->assertSessionHasErrors(['description']);
    }

    /** @test */
    public function phase_3_ui_consistency_fixes_are_working()
    {
        // NA2: Navigation has wire:navigate
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('wire:navigate');
        
        // RD2: Product cards have consistent heights
        $product = Product::factory()->create(['is_active' => true]);
        $response = $this->get('/');
        $response->assertSee('h-[450px] sm:h-[480px] lg:h-[520px]');
    }

    /** @test */
    public function subscription_plan_management_works()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.subscription-plans.index'));
        $response->assertStatus(200);
        
        // Test plan creation
        $planData = [
            'name' => 'Test Plan',
            'description' => 'Test description',
            'price' => 1000,
            'interval' => 'monthly',
            'duration_days' => 30,
            'is_active' => true,
        ];
        
        $this->assertDatabaseMissing('subscription_plans', ['name' => 'Test Plan']);
    }

    /** @test */
    public function mobile_responsiveness_is_implemented()
    {
        $this->actingAs($this->admin);
        
        // Check admin users page has mobile view
        $response = $this->get(route('admin.users.index'));
        $response->assertStatus(200);
        $response->assertSee('block lg:hidden'); // Mobile card view
        $response->assertSee('hidden lg:block'); // Desktop table view
        
        // Check admin brands page has mobile view
        $response = $this->get(route('admin.brands.index'));
        $response->assertStatus(200);
        $response->assertSee('block lg:hidden'); // Mobile card view
    }

    /** @test */
    public function product_authorization_policy_works()
    {
        $product = Product::factory()->create(['is_active' => true]);
        
        // Guest can view active products
        $response = $this->get(route('products.show', $product->slug));
        $response->assertStatus(200);
        
        // Inactive products require authentication
        $product->update(['is_active' => false]);
        $response = $this->get(route('products.show', $product->slug));
        $response->assertStatus(403);
    }

    /** @test */
    public function javascript_framework_conflicts_resolved()
    {
        // Check that Alpine.js is loaded via Vite bundle
        $response = $this->get('/');
        $response->assertStatus(200);
        
        // Should not have CDN Alpine.js
        $response->assertDontSee('cdn.jsdelivr.net/npm/alpinejs');
        
        // Should have unified cart manager
        $response->assertSee('cart-manager');
    }

    /** @test */
    public function search_implementation_is_unified()
    {
        // Check that only Livewire search exists
        $this->assertFileDoesNotExist(public_path('js/search.js'));
        
        // Livewire search component should work
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'is_active' => true
        ]);
        
        $response = $this->get(route('products.search', ['query' => 'Test']));
        $response->assertStatus(200);
        $response->assertSee('Test Product');
    }

    /** @test */
    public function stock_vs_quantity_field_is_standardized()
    {
        $product = Product::factory()->create(['stock' => 10]);
        
        // Should use stock field as primary
        $this->assertEquals(10, $product->getStockLevel());
        $this->assertTrue($product->hasSufficientStock(5));
        $this->assertFalse($product->hasSufficientStock(15));
    }

    /** @test */
    public function wire_navigate_is_consistent()
    {
        $this->actingAs($this->customer);
        
        // Customer navigation should have wire:navigate
        $response = $this->get(route('dashboard'));
        $response->assertStatus(200);
        $response->assertSee('wire:navigate');
        
        // Product cards should have wire:navigate
        $product = Product::factory()->create(['is_active' => true]);
        $response = $this->get('/');
        $response->assertSee('wire:navigate');
    }

    /** @test */
    public function all_critical_routes_are_accessible()
    {
        // Admin routes
        $this->actingAs($this->admin);
        $this->get(route('admin.dashboard'))->assertStatus(200);
        $this->get(route('admin.users.index'))->assertStatus(200);
        $this->get(route('admin.subscription-plans.index'))->assertStatus(200);
        
        // Vendor routes
        $vendor = Vendor::factory()->create(['user_id' => $this->vendor->id]);
        $this->actingAs($this->vendor);
        $this->get(route('vendor.dashboard'))->assertStatus(200);
        $this->get(route('vendor.products.index'))->assertStatus(200);
        
        // Customer routes
        $this->actingAs($this->customer);
        $this->get(route('dashboard'))->assertStatus(200);
        $this->get('/')->assertStatus(200);
    }
}
