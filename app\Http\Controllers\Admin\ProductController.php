<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index()
    {
        return view('admin.products.index');
    }

    /**
     * Show the form for creating a new product
     */
    public function create()
    {
        $vendors = Vendor::select('id', 'shop_name')
            ->where('is_active', true)
            ->orderBy('shop_name')
            ->get();

        $categories = Category::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $brands = Brand::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.products.create', compact('vendors', 'categories', 'brands'));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product)
    {
        // Load product with relationships
        $product->load(['vendor', 'category', 'brand', 'variants', 'specifications']);

        // Load dropdown data
        $vendors = Vendor::select('id', 'shop_name')
            ->where('is_active', true)
            ->orderBy('shop_name')
            ->get();

        $categories = Category::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $brands = Brand::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.products.edit', compact('product', 'vendors', 'categories', 'brands'));
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        $product->load(['vendor', 'category', 'brand', 'variants', 'specifications']);
        return view('admin.products.show', compact('product'));
    }

    /**
     * Remove the specified product from storage
     */
    public function destroy(Product $product)
    {
        $product->delete();
        
        return redirect()
            ->route('admin.products.index')
            ->with('success', 'Product deleted successfully.');
    }
}
