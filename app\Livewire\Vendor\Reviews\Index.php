<?php

namespace App\Livewire\Vendor\Reviews;

use Livewire\Component;
use App\Models\Review;
use Illuminate\Support\Facades\Auth;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public function mount()
    {
        // SECURITY: Authorize that the user can view any reviews.
        $this->authorize('viewAny', Review::class);
    }

    public function render()
    {
        $vendor = Auth::user()->vendor;
        // PERFORMANCE: Eager-load relationships to prevent N+1 query issues.
        $reviews = Review::with(['product', 'user'])
            ->whereHas('product', function ($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            })
            ->latest()
            ->paginate(10);

        return view('livewire.vendor.reviews.index', [
            'reviews' => $reviews,
        ])->layout('layouts.vendor');
    }
}
