<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Category extends Model
{
    use HasFactory;
        protected $fillable = [
        'name',
        'slug',
        'parent_id',
        'description',
        'is_active',
        'order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getAllProducts()
    {
        // PERFORMANCE FIX: Use single query instead of recursive calls to avoid N+1 problem
        $categoryIds = $this->getAllCategoryIds();

        return Product::whereIn('category_id', $categoryIds)
            ->where('is_active', true);
    }

    /**
     * Get all category IDs including children recursively using a single query
     */
    private function getAllCategoryIds()
    {
        $categoryIds = [$this->id];

        // Use a single query to get all descendant categories
        $descendants = Category::where('parent_id', $this->id)->pluck('id');

        foreach ($descendants as $descendantId) {
            $descendant = Category::find($descendantId);
            if ($descendant) {
                $categoryIds = array_merge($categoryIds, $descendant->getAllCategoryIds());
            }
        }

        return array_unique($categoryIds);
    }

    /**
     * Get the category's image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        $imageUrlValue = $this->attributes['image_url'] ?? null;

        if ($imageUrlValue && Storage::disk('public')->exists($imageUrlValue)) {
            return Storage::url($imageUrlValue);
        }
        return asset('images/default-category.png'); // Default image
    }
}
