<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Traits\HandlesErrors;

#[Layout('layouts.admin')]
class UserForm extends Component
{
    use HandlesErrors;
    public ?User $user = null;

    // Form fields
    public string $name = '';
    public string $email = '';
    public ?string $password = null;
    public ?string $password_confirmation = null;
    public ?int $selectedRole = null;

    public $allRoles;

    public function mount(?User $user = null): void
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // SECURITY FIX: Verify user management authorization
        if (!Gate::allows('manage-users')) {
            abort(403, 'Unauthorized to manage users.');
        }

        $this->user = $user;
        $this->allRoles = Role::pluck('name', 'id');

        if ($this->user && $this->user->exists) {
            $this->name = $this->user->name;
            $this->email = $this->user->email;
            $this->selectedRole = $this->user->role_id;
        }
    }

    protected function rules(): array
    {
        $userId = $this->user?->id ?? 'NULL';
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $userId,
            'selectedRole' => 'required|exists:roles,id',
        ];

        if (!$this->user || !$this->user->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        } else {
            $rules['password'] = 'nullable|string|min:8|confirmed';
        }

        return $rules;
    }

    public function save()
    {
        $this->validate();

        // SECURITY FIX: Verify authorization before role assignment
        if ($this->selectedRole && !Gate::allows('assign-role', $this->selectedRole)) {
            session()->flash('error', 'Unauthorized role assignment attempt.');
            return;
        }

        DB::transaction(function () {
            $data = [
                'name' => $this->name,
                'email' => $this->email,
            ];

            // SECURITY FIX: Only assign role if authorized
            if ($this->selectedRole && Gate::allows('assign-role', $this->selectedRole)) {
                $data['role_id'] = $this->selectedRole;
            } else {
                // Log unauthorized attempt
                \Log::warning('Unauthorized role assignment attempt blocked', [
                    'admin_user_id' => Auth::id(),
                    'admin_email' => Auth::user()->email,
                    'attempted_role_id' => $this->selectedRole,
                    'target_user_id' => $this->user?->id,
                    'timestamp' => now()
                ]);

                session()->flash('error', 'You are not authorized to assign this role.');
                return;
            }

            if ($this->password) {
                $data['password'] = Hash::make($this->password);
            }

            if ($this->user && $this->user->exists) {
                // SECURITY FIX: Additional check for existing user updates
                if (!Gate::allows('manage-users')) {
                    abort(403, 'Unauthorized to update users.');
                }

                $this->user->update($data);
                session()->flash('success', 'User updated successfully.');

                // Log successful user update
                \Log::info('User updated by admin', [
                    'admin_user_id' => Auth::id(),
                    'updated_user_id' => $this->user->id,
                    'updated_role_id' => $this->selectedRole,
                    'timestamp' => now()
                ]);
            } else {
                $this->user = User::create($data);
                session()->flash('success', 'User created successfully.');

                // Log successful user creation
                \Log::info('User created by admin', [
                    'admin_user_id' => Auth::id(),
                    'created_user_id' => $this->user->id,
                    'assigned_role_id' => $this->selectedRole,
                    'timestamp' => now()
                ]);
            }
        });

        return redirect()->route('admin.users.index');
    }

    public function render()
    {
        return view('livewire.admin.users.user-form');
    }
}
