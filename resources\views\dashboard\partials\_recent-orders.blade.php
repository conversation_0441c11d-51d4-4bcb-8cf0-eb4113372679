<!-- Recent Orders -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200 flex justify-between items-center">
        <h5 class="font-bold text-lg">Recent Orders</h5>
        <a href="{{ route('orders.index') }}" class="text-sm text-black hover:underline">
            View All <i class="fas fa-arrow-right ml-1"></i>
        </a>
    </div>
    <div class="p-6">
        @php
            $orders = auth()->user()->orders()->latest()->take(5)->get();
        @endphp

        @forelse($orders as $order)
            @php
                $statusClasses = [
                    'completed' => 'bg-green-100 text-green-800',
                    'pending' => 'bg-yellow-100 text-yellow-800',
                    'processing' => 'bg-blue-100 text-blue-800',
                    'shipped' => 'bg-indigo-100 text-indigo-800',
                    'cancelled' => 'bg-red-100 text-red-800',
                    'default' => 'bg-gray-100 text-gray-800',
                ];
                $statusClass = $statusClasses[$order->status] ?? $statusClasses['default'];
            @endphp
            <div class="flex justify-between items-center py-4 @if(!$loop->last) border-b border-gray-200 @endif">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-4">
                        <i class="fas fa-shopping-bag text-gray-500"></i>
                    </div>
                    <div>
                        <h6 class="font-bold">Order #{{ $order->order_number }}</h6>
                        <p class="text-sm text-gray-500">{{ $order->created_at->format('M d, Y') }} • {{ $order->items->count() }} item(s)</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold">₦{{ number_format($order->total, 2) }}</p>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusClass }}">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
                <div class="ml-4">
                    <a href="{{ route('orders.show', $order->id) }}" class="text-sm text-black hover:underline">
                        <i class="fas fa-eye mr-1"></i> View
                    </a>
                </div>
            </div>
        @empty
            <div class="text-center py-12">
                <i class="fas fa-receipt fa-4x text-gray-300 mb-4"></i>
                <h5 class="font-bold text-lg">No Orders Found</h5>
                <p class="text-gray-500 mb-4">You haven't placed any orders yet.</p>
                <a href="{{ route('products.index') }}" class="inline-flex items-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    <i class="fas fa-shopping-cart mr-2"></i> Start Shopping
                </a>
            </div>
        @endforelse
    </div>
</div>
