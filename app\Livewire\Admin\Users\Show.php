<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.admin')]
class Show extends Component
{
    public User $user;

    public function mount(User $user)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
        
        // Load the user with all necessary relationships
        $this->user = $user->load([
            'role',
            'vendor',
            'orders',
            'reviews'
        ]);
    }

    public function render()
    {
        return view('livewire.admin.users.show');
    }
}
