<?php

namespace Tests\Unit\Services;

use App\Services\CommissionService;
use App\Models\Order;
use App\Models\Vendor;
use App\Models\Commission;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CommissionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CommissionService $commissionService;
    protected $vendor;
    protected $order;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->commissionService = new CommissionService();
        
        // Create test data
        $user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'price' => 1000.00,
        ]);

        $this->order = Order::factory()->create([
            'user_id' => $user->id,
            'vendor_id' => $this->vendor->id,
            'total_amount' => 1000.00,
            'payment_status' => 'paid',
        ]);
    }

    /** @test */
    public function it_calculates_commission_with_default_rate()
    {
        $amount = 1000.00;
        $expectedCommission = 27.00; // 2.7% of 1000

        $commission = $this->commissionService->calculateCommission($amount);

        $this->assertEquals($expectedCommission, $commission);
    }

    /** @test */
    public function it_calculates_commission_with_custom_rate()
    {
        $amount = 1000.00;
        $customRate = 0.05; // 5%
        $expectedCommission = 50.00;

        $commission = $this->commissionService->calculateCommission($amount, $customRate);

        $this->assertEquals($expectedCommission, $commission);
    }

    /** @test */
    public function it_calculates_vendor_earnings_correctly()
    {
        $amount = 1000.00;
        $expectedEarnings = 973.00; // 1000 - (1000 * 0.027)

        $earnings = $this->commissionService->calculateVendorEarnings($amount);

        $this->assertEquals($expectedEarnings, $earnings);
    }

    /** @test */
    public function it_rounds_commission_to_two_decimal_places()
    {
        $amount = 33.33;
        $expectedCommission = 0.90; // 33.33 * 0.027 = 0.89991, rounded to 0.90

        $commission = $this->commissionService->calculateCommission($amount);

        $this->assertEquals($expectedCommission, $commission);
    }

    /** @test */
    public function it_handles_zero_amount()
    {
        $commission = $this->commissionService->calculateCommission(0);
        $earnings = $this->commissionService->calculateVendorEarnings(0);

        $this->assertEquals(0.00, $commission);
        $this->assertEquals(0.00, $earnings);
    }

    /** @test */
    public function it_handles_negative_amounts()
    {
        $amount = -100.00;
        $expectedCommission = -2.70; // -100 * 0.027
        $expectedEarnings = -97.30; // -100 - (-2.70)

        $commission = $this->commissionService->calculateCommission($amount);
        $earnings = $this->commissionService->calculateVendorEarnings($amount);

        $this->assertEquals($expectedCommission, $commission);
        $this->assertEquals($expectedEarnings, $earnings);
    }

    /** @test */
    public function it_returns_correct_commission_rate()
    {
        $rate = $this->commissionService->getCommissionRate();
        $this->assertEquals(0.027, $rate);
    }

    /** @test */
    public function it_creates_commission_record_for_order()
    {
        $commission = $this->commissionService->createCommissionForOrder($this->order);

        $this->assertInstanceOf(Commission::class, $commission);
        $this->assertEquals($this->order->id, $commission->order_id);
        $this->assertEquals($this->vendor->id, $commission->vendor_id);
        $this->assertEquals(27.00, $commission->amount);
        $this->assertEquals('pending', $commission->status);
    }

    /** @test */
    public function it_prevents_duplicate_commission_creation()
    {
        // Create first commission
        $this->commissionService->createCommissionForOrder($this->order);

        // Attempt to create duplicate
        $duplicate = $this->commissionService->createCommissionForOrder($this->order);

        $this->assertNull($duplicate);
        $this->assertEquals(1, Commission::where('order_id', $this->order->id)->count());
    }

    /** @test */
    public function it_calculates_total_commission_for_vendor()
    {
        // Create multiple orders for the vendor
        $order2 = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 500.00,
            'payment_status' => 'paid',
        ]);

        $order3 = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 750.00,
            'payment_status' => 'paid',
        ]);

        // Create commissions
        $this->commissionService->createCommissionForOrder($this->order);
        $this->commissionService->createCommissionForOrder($order2);
        $this->commissionService->createCommissionForOrder($order3);

        $totalCommission = $this->commissionService->getTotalCommissionForVendor($this->vendor);

        // Expected: (1000 * 0.027) + (500 * 0.027) + (750 * 0.027) = 27 + 13.5 + 20.25 = 60.75
        $this->assertEquals(60.75, $totalCommission);
    }

    /** @test */
    public function it_calculates_commission_for_date_range()
    {
        // Create orders on different dates
        $oldOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 1000.00,
            'payment_status' => 'paid',
            'created_at' => now()->subDays(10),
        ]);

        $recentOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 500.00,
            'payment_status' => 'paid',
            'created_at' => now()->subDays(2),
        ]);

        $this->commissionService->createCommissionForOrder($oldOrder);
        $this->commissionService->createCommissionForOrder($recentOrder);

        // Get commission for last 5 days
        $recentCommission = $this->commissionService->getCommissionForDateRange(
            $this->vendor,
            now()->subDays(5),
            now()
        );

        // Should only include the recent order commission
        $this->assertEquals(13.50, $recentCommission); // 500 * 0.027
    }

    /** @test */
    public function it_marks_commission_as_paid()
    {
        $commission = $this->commissionService->createCommissionForOrder($this->order);

        $this->commissionService->markCommissionAsPaid($commission);

        $commission->refresh();
        $this->assertEquals('paid', $commission->status);
        $this->assertNotNull($commission->paid_at);
    }

    /** @test */
    public function it_gets_pending_commissions_for_vendor()
    {
        // Create multiple commissions
        $commission1 = $this->commissionService->createCommissionForOrder($this->order);
        
        $order2 = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 500.00,
            'payment_status' => 'paid',
        ]);
        $commission2 = $this->commissionService->createCommissionForOrder($order2);

        // Mark one as paid
        $this->commissionService->markCommissionAsPaid($commission1);

        $pendingCommissions = $this->commissionService->getPendingCommissionsForVendor($this->vendor);

        $this->assertCount(1, $pendingCommissions);
        $this->assertEquals($commission2->id, $pendingCommissions->first()->id);
    }

    /** @test */
    public function it_validates_commission_calculations_against_order_total()
    {
        $commission = $this->commissionService->calculateCommission($this->order->total_amount);
        $vendorEarnings = $this->commissionService->calculateVendorEarnings($this->order->total_amount);

        // Commission + Vendor Earnings should equal Order Total
        $this->assertEquals(
            $this->order->total_amount,
            $commission + $vendorEarnings
        );
    }

    /** @test */
    public function it_handles_large_amounts_correctly()
    {
        $largeAmount = 999999.99;
        $expectedCommission = 26999.99; // 999999.99 * 0.027, rounded
        $expectedEarnings = 973000.00; // 999999.99 - 26999.99

        $commission = $this->commissionService->calculateCommission($largeAmount);
        $earnings = $this->commissionService->calculateVendorEarnings($largeAmount);

        $this->assertEquals($expectedCommission, $commission);
        $this->assertEquals($expectedEarnings, $earnings);
    }

    /** @test */
    public function it_provides_commission_breakdown_for_order()
    {
        $breakdown = $this->commissionService->getCommissionBreakdown($this->order);

        $this->assertIsArray($breakdown);
        $this->assertArrayHasKey('order_total', $breakdown);
        $this->assertArrayHasKey('commission_amount', $breakdown);
        $this->assertArrayHasKey('commission_rate', $breakdown);
        $this->assertArrayHasKey('vendor_earnings', $breakdown);
        $this->assertArrayHasKey('commission_percentage', $breakdown);

        $this->assertEquals(1000.00, $breakdown['order_total']);
        $this->assertEquals(27.00, $breakdown['commission_amount']);
        $this->assertEquals(0.027, $breakdown['commission_rate']);
        $this->assertEquals(973.00, $breakdown['vendor_earnings']);
        $this->assertEquals('2.70%', $breakdown['commission_percentage']);
    }
}
