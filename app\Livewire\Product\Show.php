<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Services\ViewedProductsService;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Show extends Component
{
    public Product $product;
    public $relatedProducts;
    public $recentlyViewedProducts;

    public function mount(Product $product, ViewedProductsService $viewedProductsService)
    {
        // CRITICAL FIX: Add error handling for product loading
        try {
            $this->product = $product;

            // Ensure product is active and accessible
            if (!$this->product->is_active) {
                abort(404, 'Product not found or is no longer available.');
            }

            // Track this product view
            $viewedProductsService->addProduct($this->product);

            // Load related products with error handling
            $this->relatedProducts = Product::query()
                ->where('category_id', $this->product->category_id)
                ->where('id', '!=', $this->product->id)
                ->where('is_active', true)
                ->limit(4)
                ->get();

            // Get recently viewed products with error handling
            $this->recentlyViewedProducts = $viewedProductsService->getRecentlyViewedProducts(5)
                ->filter(fn($item) => $item->id !== $this->product->id)
                ->take(4);

        } catch (\Exception $e) {
            \Log::error('Product show page error', [
                'product_id' => $product->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            abort(500, 'Unable to load product details. Please try again later.');
        }
    }

    public function render()
    {
        return view('livewire.product.show');
    }
}
