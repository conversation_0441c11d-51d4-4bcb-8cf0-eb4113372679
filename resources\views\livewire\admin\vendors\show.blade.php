<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $vendor->shop_name }}</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Vendor Details & Management</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{{ route('admin.vendors.edit', $vendor) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                <i class="fas fa-edit mr-2"></i>
                Edit Vendor
            </a>
            <a href="{{ route('admin.vendors.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Vendors
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
            {{ session('success') }}
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Vendor Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Vendor Details Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Vendor Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Shop Name</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->shop_name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Owner Name</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->user->name ?? 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->user->email ?? 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->phone ?? 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Type</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->business_type ?? 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $vendor->status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($vendor->status) }}
                        </span>
                    </div>
                </div>
                
                @if($vendor->business_description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Description</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->business_description }}</p>
                </div>
                @endif

                @if($vendor->address)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->address }}</p>
                </div>
                @endif
            </div>

            <!-- Subscription Information -->
            @if($vendor->subscription)
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Subscription Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Plan</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->subscription->plan->name ?? 'N/A' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $vendor->subscription->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ ucfirst($vendor->subscription->status) }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Started</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->subscription->created_at->format('M d, Y') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Expires</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $vendor->subscription->expires_at ? $vendor->subscription->expires_at->format('M d, Y') : 'N/A' }}</p>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Vendor Logo -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Vendor Logo</h2>
                @if($vendor->logo_url)
                    <img src="{{ $vendor->logo_url }}" alt="{{ $vendor->shop_name }}" class="w-full h-32 object-contain rounded-lg bg-gray-50">
                @else
                    <div class="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        <i class="fas fa-store text-gray-400 text-3xl"></i>
                    </div>
                @endif
            </div>

            <!-- Vendor Actions -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Vendor Actions</h2>
                
                <div class="space-y-4">
                    <!-- Status Toggle -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Approved</span>
                        <button wire:click="toggleStatus" 
                                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $vendor->status === 'approved' ? 'bg-blue-600' : 'bg-gray-200' }}">
                            <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {{ $vendor->status === 'approved' ? 'translate-x-5' : 'translate-x-0' }}"></span>
                        </button>
                    </div>

                    <!-- Featured Toggle -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Featured</span>
                        <button wire:click="toggleFeatured" 
                                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $vendor->is_featured ? 'bg-blue-600' : 'bg-gray-200' }}">
                            <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {{ $vendor->is_featured ? 'translate-x-5' : 'translate-x-0' }}"></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Vendor Stats -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Statistics</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Products</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $vendor->products->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Orders</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $vendor->orders->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Balance</span>
                        <span class="text-sm text-gray-900 dark:text-white">₦{{ number_format($vendor->balance ?? 0, 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Joined</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $vendor->created_at->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
