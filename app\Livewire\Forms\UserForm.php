<?php

namespace App\Livewire\Forms;

use App\Models\User;
use App\Models\Role;
use Livewire\Attributes\Validate;
use Livewire\Form;
use Illuminate\Validation\Rule;

class UserForm extends Form
{
    public ?User $user = null;

    // Form fields
    #[Validate('required|string|max:255')]
    public string $name = '';

    #[Validate('required|email|max:255')]
    public string $email = '';

    #[Validate('nullable|string|min:8|confirmed')]
    public ?string $password = null;

    #[Validate('nullable|string|min:8')]
    public ?string $password_confirmation = null;

    #[Validate('required|exists:roles,id')]
    public ?int $selectedRole = null;

    /**
     * Set the user for editing
     */
    public function setUser(User $user): void
    {
        $this->user = $user;
        
        $this->name = $user->name;
        $this->email = $user->email;
        $this->selectedRole = $user->role_id;
    }

    /**
     * Store a new user
     */
    public function store(): User
    {
        $this->validate();

        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'password' => bcrypt($this->password),
            'role_id' => $this->selectedRole,
        ];

        $user = User::create($userData);

        $this->reset();

        return $user;
    }

    /**
     * Update the existing user
     */
    public function update(): void
    {
        $this->validate();

        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'role_id' => $this->selectedRole,
        ];

        if ($this->password) {
            $userData['password'] = bcrypt($this->password);
        }

        $this->user->update($userData);
    }

    /**
     * Custom validation rules that require runtime logic
     */
    protected function rules(): array
    {
        $rules = [];

        // Email uniqueness validation
        if ($this->user) {
            $rules['email'] = [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($this->user->id)
            ];
        } else {
            $rules['email'] = [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ];
        }

        // Password validation for new users
        if (!$this->user || !$this->user->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        }

        return $rules;
    }

    /**
     * Custom validation messages
     */
    protected function messages(): array
    {
        return [
            'name.required' => 'Full name is required.',
            'name.max' => 'Full name cannot exceed 255 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required for new users.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'selectedRole.required' => 'Please select a role for the user.',
            'selectedRole.exists' => 'Selected role is invalid.',
        ];
    }

    /**
     * Custom attribute names for validation messages
     */
    protected function validationAttributes(): array
    {
        return [
            'name' => 'full name',
            'email' => 'email address',
            'selectedRole' => 'role',
        ];
    }

    /**
     * Validate only user information fields
     */
    public function validateUserInfo(): void
    {
        $this->validateOnly([
            'name',
            'email'
        ]);
    }

    /**
     * Validate password fields
     */
    public function validatePassword(): void
    {
        $this->validateOnly([
            'password',
            'password_confirmation'
        ]);
    }

    /**
     * Validate role selection
     */
    public function validateRole(): void
    {
        $this->validateOnly('selectedRole');
    }
}
