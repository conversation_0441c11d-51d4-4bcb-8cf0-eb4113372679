<?php

namespace App\Livewire\Vendor\Settings;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;

#[Layout('layouts.vendor')]
class Profile extends Component
{
    use WithFileUploads;

    public $vendor;
    public $banner;
    public $about;
    public $facebook_url;
    public $twitter_url;
    public $instagram_url;

    protected $rules = [
        'about' => 'nullable|string|max:5000',
        'facebook_url' => 'nullable|url|max:255',
        'twitter_url' => 'nullable|url|max:255',
        'instagram_url' => 'nullable|url|max:255',
        'banner' => 'nullable|image|mimes:jpg,jpeg,png|max:2048', // 2MB Max
    ];

    public function mount()
    {
        $this->vendor = Auth::user()->vendor;
        $this->about = $this->vendor->about;
        $this->facebook_url = $this->vendor->facebook_url;
        $this->twitter_url = $this->vendor->twitter_url;
        $this->instagram_url = $this->vendor->instagram_url;
    }

    public function save()
    {
        $this->validate();

        $this->vendor->update([
            'about' => $this->about,
            'facebook_url' => $this->facebook_url,
            'twitter_url' => $this->twitter_url,
            'instagram_url' => $this->instagram_url,
        ]);

        if ($this->banner) {
            $this->vendor->addMedia($this->banner->getRealPath())
                         ->toMediaCollection('banner');
        }

        session()->flash('success', 'Profile updated successfully.');

        return $this->redirect(route('vendor.settings.profile'), navigate: true);
    }

    public function render()
    {
        return view('livewire.vendor.settings.profile');
    }
}
