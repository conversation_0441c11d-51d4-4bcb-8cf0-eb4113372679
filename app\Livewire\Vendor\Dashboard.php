<?php

namespace App\Livewire\Vendor;

use App\Models\Order;
use App\Traits\RequiresVendorAuthorization;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Dashboard extends Component
{
    use RequiresVendorAuthorization;
    public function render()
    {
        \Log::info('Vendor Dashboard Render Started', [
            'user_id' => Auth::id(),
            'timestamp' => now()->toISOString()
        ]);

        $vendor = Auth::user()->vendor;

        // CRITICAL FIX: Handle case where vendor doesn't exist
        if (!$vendor) {
            \Log::warning('Vendor Dashboard - No Vendor Found', [
                'user_id' => Auth::id(),
                'user_email' => Auth::user()->email
            ]);
            return redirect()->route('vendor.onboarding')
                ->with('error', 'Please complete your vendor onboarding first.');
        }

        \Log::info('Vendor Dashboard - Vendor Found', [
            'vendor_id' => $vendor->id,
            'vendor_name' => $vendor->business_name,
            'is_approved' => $vendor->is_approved
        ]);

        // Get total sales
        $totalSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->sum(DB::raw('order_items.price * order_items.quantity'));

        // Get last month's sales for comparison
        $lastMonthSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.created_at', '>=', Carbon::now()->subMonth())
            ->sum(DB::raw('order_items.price * order_items.quantity'));

        $prevMonthSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [Carbon::now()->subMonths(2), Carbon::now()->subMonth()])
            ->sum(DB::raw('order_items.price * order_items.quantity'));

        // Calculate growth percentage
        $salesGrowth = $prevMonthSales > 0
            ? round((($lastMonthSales - $prevMonthSales) / $prevMonthSales) * 100, 1)
            : ($lastMonthSales > 0 ? 100 : 0);

        // Count total orders
        $totalOrders = Order::whereHas('items.product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->count();

        // Count last month's orders
        $lastMonthOrders = Order::whereHas('items.product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
            ->where('created_at', '>=', Carbon::now()->subMonth())
            ->count();

        $prevMonthOrders = Order::whereHas('items.product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
            ->whereBetween('created_at', [Carbon::now()->subMonths(2), Carbon::now()->subMonth()])
            ->count();

        // Calculate order growth percentage
        $orderGrowth = $prevMonthOrders > 0
            ? round((($lastMonthOrders - $prevMonthOrders) / $prevMonthOrders) * 100, 1)
            : ($lastMonthOrders > 0 ? 100 : 0);

        // Count total products
        $totalProducts = $vendor->products()->count();
        $newProductsThisMonth = $vendor->products()
            ->where('created_at', '>=', Carbon::now()->startOfMonth())
            ->count();

        // Get subscription info
        $subscription = $vendor->subscription;
        $subscriptionName = $subscription && $subscription->plan ? $subscription->plan->name : 'Free';
        $daysRemaining = $subscription ? Carbon::now()->diffInDays($subscription->ends_at, false) : 0;

        // PERFORMANCE FIX: Optimized recent orders query with proper eager loading
        $recentOrders = Order::whereHas('items.product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
            ->with([
                'items' => function($query) use ($vendor) {
                    $query->whereHas('product', function($q) use ($vendor) {
                        $q->where('vendor_id', $vendor->id);
                    });
                },
                'items.product:id,name,price,image_url',
                'user:id,name,email'
            ])
            ->latest()
            ->limit(5)
            ->get();

        // PERFORMANCE FIX: Optimized product performance query
        $productPerformance = $vendor->products()
            ->select(['id', 'name', 'price', 'image_url', 'stock'])
            ->withCount(['orderItems as total_sold'])
            ->withSum('orderItems as total_revenue', \DB::raw('price * quantity'))
            ->orderBy('total_sold', 'desc')
            ->limit(5)
            ->get();

        return view('livewire.vendor.dashboard', compact(
            'totalSales', 'salesGrowth', 'totalOrders', 'orderGrowth',
            'totalProducts', 'newProductsThisMonth', 'subscriptionName',
            'daysRemaining', 'recentOrders', 'productPerformance'
        ));
    }

    public function getAnalyticsData()
    {
        $vendor = Auth::user()->vendor;

        $salesOverTime = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(order_items.price * order_items.quantity) as total')
            )
            ->where('orders.created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();

        return ['sales_over_time' => $salesOverTime];
    }

    public function getMapData()
    {
        $vendor = Auth::user()->vendor;

        $locations = Order::whereHas('items.product', function ($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            })
            ->select('shipping_city', 'shipping_state', DB::raw('count(*) as order_count'))
            ->whereNotNull('shipping_city')
            ->whereNotNull('shipping_state')
            ->groupBy('shipping_city', 'shipping_state')
            ->orderBy('order_count', 'desc')
            ->limit(10)
            ->get();

        return ['locations' => $locations];
    }
}
