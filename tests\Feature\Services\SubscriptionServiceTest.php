<?php

namespace Tests\Feature\Services;

use App\Models\Plan;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorSubscription;
use App\Notifications\VendorSubscriptionReminder;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;

    private SubscriptionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = $this->app->make(SubscriptionService::class);
    }

    #[Test]
    public function it_can_block_vendor_orders_by_suspending_the_vendor(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create(['status' => 'active']);

        // Act
        $this->service->blockVendorOrders($vendor);

        // Assert
        $this->assertDatabaseHas('vendors', [
            'id' => $vendor->id,
            'status' => 'suspended',
        ]);
    }

    #[Test]
    public function an_active_vendor_can_receive_orders(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create(['status' => 'active']);

        // Act & Assert
        $this->assertTrue($this->service->canReceiveOrders($vendor));
    }

    #[Test]
    public function a_suspended_vendor_cannot_receive_orders(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create(['status' => 'suspended']);

        // Act & Assert
        $this->assertFalse($this->service->canReceiveOrders($vendor));
    }

    #[Test]
    public function it_sends_a_reminder_for_a_subscription_expiring_soon(): void
    {
        // Arrange
        Notification::fake();
        $vendor = Vendor::factory()->create();
        VendorSubscription::factory()->for($vendor)->create([
            'ends_at' => now()->addDays(5),
        ]);

        // Act
        $this->service->sendSubscriptionReminders();

        // Assert
        Notification::assertSentTo($vendor->user, VendorSubscriptionReminder::class);
    }

    #[Test]
    public function it_does_not_send_a_reminder_for_a_subscription_not_expiring_soon(): void
    {
        // Arrange
        Notification::fake();
        $vendor = Vendor::factory()->create();
        VendorSubscription::factory()->for($vendor)->create([
            'ends_at' => now()->addDays(30),
        ]);

        // Act
        $this->service->sendSubscriptionReminders();

        // Assert
        Notification::assertNotSentTo($vendor->user, VendorSubscriptionReminder::class);
    }
}
