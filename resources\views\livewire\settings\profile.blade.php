<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Profile Settings
                </h1>
                <p class="text-gray-300 text-lg">Update your personal information and preferences</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Profile Form Section --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Personal Information</h3>
                    <p class="text-gray-500 mt-1">Update your name and email address</p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                </div>
            </div>
        </div>

        <div class="p-8">
            <form wire:submit="updateProfileInformation" class="space-y-6">
                {{-- Success Message --}}
                <div x-data="{ show: false }" x-show="show" x-transition x-init="@this.on('profile-updated', () => { show = true; setTimeout(() => show = false, 3000) })">
                    <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-500 rounded-full mr-4">
                                <i class="fas fa-check text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-green-800">Profile Updated!</h4>
                                <p class="text-sm text-green-700 mt-1">Your profile information has been saved successfully.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {{-- Name Field --}}
                    <x-input
                        label="Full Name"
                        wire:model.live.debounce.300ms="name"
                        id="name"
                        autocomplete="name"
                        placeholder="Enter your full name"
                        required
                    />

                    {{-- Email Field --}}
                    <x-input
                        label="Email Address"
                        type="email"
                        wire:model.live.debounce.300ms="email"
                        id="email"
                        autocomplete="email"
                        placeholder="Enter your email address"
                        required
                    />
                </div>

                {{-- Email Verification Notice --}}
                @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail &&! auth()->user()->hasVerifiedEmail())
                    <div class="rounded-2xl bg-gradient-to-r from-yellow-50 to-orange-50 border-l-4 border-yellow-500 p-6 shadow-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-500 rounded-full mr-4">
                                <i class="fas fa-exclamation-triangle text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-semibold text-yellow-800">Email Verification Required</h4>
                                <p class="text-sm text-yellow-700 mt-1">Your email address is unverified. Please check your inbox for a verification email.</p>
                                <button type="button"
                                        wire:click.prevent="resendVerificationNotification"
                                        class="mt-3 inline-flex items-center px-4 py-2 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition-all duration-300 hover:scale-105">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Resend Verification Email
                                </button>
                            </div>
                        </div>
                    </div>

                    @if (session('status') === 'verification-link-sent')
                        <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-500 rounded-full mr-4">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-green-800">Verification Email Sent!</h4>
                                    <p class="text-sm text-green-700 mt-1">A new verification link has been sent to your email address.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                @endif

                {{-- Submit Button --}}
                <div class="flex items-center justify-between pt-6 border-t border-gray-100">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Changes will be saved to your account immediately
                    </div>
                    <div class="flex items-center space-x-4">
                        <div wire:loading wire:target="updateProfileInformation" class="flex items-center text-blue-600">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                            <span class="text-sm font-medium">Saving...</span>
                        </div>
                        <x-button
                            wire:loading.attr="disabled"
                            wire:target="updateProfileInformation"
                        >
                            <i class="fas fa-save transition-transform duration-300 group-hover:scale-110"></i>
                            <span>Save Changes</span>
                        </x-button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Account Deletion Section --}}
    <div class="bg-white rounded-2xl shadow-lg border border-red-200 overflow-hidden">
        <div class="p-8 border-b border-red-100 bg-red-50">
            <div class="flex items-center">
                <div class="p-3 bg-red-500 rounded-full mr-4">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-red-900">Danger Zone</h3>
                    <p class="text-red-700 mt-1">Permanently delete your account and all associated data</p>
                </div>
            </div>
        </div>
        <div class="p-8">
            <livewire:settings.delete-user-form />
        </div>
    </div>
</div>
