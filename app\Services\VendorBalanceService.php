<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\VendorTransaction;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Vendor Balance Service
 * 
 * Handles all vendor balance calculations, updates, and financial operations
 * with atomic transactions and integrity validation.
 */
class VendorBalanceService
{
    /**
     * Calculate vendor's current balance from transactions
     *
     * @param Vendor $vendor
     * @return float Current balance
     */
    public function calculateVendorBalance(Vendor $vendor): float
    {
        \Log::info('VendorBalanceService Calculate Balance Started', [
            'vendor_id' => $vendor->id,
            'vendor_name' => $vendor->business_name,
            'timestamp' => now()->toISOString()
        ]);

        $balance = VendorTransaction::where('vendor_id', $vendor->id)
            ->sum('amount');

        \Log::info('VendorBalanceService Calculate Balance Completed', [
            'vendor_id' => $vendor->id,
            'calculated_balance' => $balance
        ]);

        return $balance;
    }

    /**
     * Calculate vendor's total earnings (sales only)
     *
     * @param Vendor $vendor
     * @return float Total earnings from sales
     */
    public function calculateTotalEarnings(Vendor $vendor): float
    {
        return VendorTransaction::where('vendor_id', $vendor->id)
            ->where('type', 'sale')
            ->sum('amount');
    }

    /**
     * Calculate vendor's total withdrawn amount
     *
     * @param Vendor $vendor
     * @return float Total withdrawn amount (positive value)
     */
    public function calculateTotalWithdrawn(Vendor $vendor): float
    {
        return abs(VendorTransaction::where('vendor_id', $vendor->id)
            ->whereIn('type', ['withdrawal', 'commission'])
            ->sum('amount'));
    }

    /**
     * Calculate pending balance (orders not yet available for withdrawal)
     *
     * @param Vendor $vendor
     * @return float Pending balance amount
     */
    public function calculatePendingBalance(Vendor $vendor): float
    {
        // Orders that are paid but not yet delivered are considered pending
        return Order::where('vendor_id', $vendor->id)
            ->where('payment_status', 'paid')
            ->whereIn('status', ['processing', 'shipped', 'confirmed'])
            ->sum('total_amount');
    }

    /**
     * Calculate available balance (ready for withdrawal)
     *
     * @param Vendor $vendor
     * @return float Available balance
     */
    public function calculateAvailableBalance(Vendor $vendor): float
    {
        $totalBalance = $this->calculateVendorBalance($vendor);
        $pendingBalance = $this->calculatePendingBalance($vendor);
        
        return max(0, $totalBalance - $pendingBalance);
    }

    /**
     * Update vendor balance fields atomically
     *
     * @param Vendor $vendor
     * @return bool Success status
     */
    public function updateVendorBalance(Vendor $vendor): bool
    {
        try {
            return DB::transaction(function () use ($vendor) {
                // Lock the vendor record for update
                $lockedVendor = Vendor::where('id', $vendor->id)->lockForUpdate()->first();
                
                if (!$lockedVendor) {
                    throw new \Exception('Vendor not found for balance update');
                }

                $totalEarnings = $this->calculateTotalEarnings($vendor);
                $totalWithdrawn = $this->calculateTotalWithdrawn($vendor);
                $pendingBalance = $this->calculatePendingBalance($vendor);
                $currentBalance = $this->calculateVendorBalance($vendor);
                $availableBalance = max(0, $currentBalance - $pendingBalance);

                $updateData = [
                    'total_earnings' => $totalEarnings,
                    'withdrawn_amount' => $totalWithdrawn,
                    'pending_balance' => $pendingBalance,
                    'available_balance' => $availableBalance,
                    'last_balance_update' => now(),
                ];

                $result = $lockedVendor->update($updateData);

                Log::info('Vendor balance updated', [
                    'vendor_id' => $vendor->id,
                    'previous_balance' => $vendor->available_balance,
                    'new_balance' => $availableBalance,
                    'total_earnings' => $totalEarnings,
                    'total_withdrawn' => $totalWithdrawn,
                    'pending_balance' => $pendingBalance,
                ]);

                // Refresh the vendor instance
                $vendor->refresh();

                return $result;
            });
        } catch (\Exception $e) {
            Log::error('Failed to update vendor balance', [
                'vendor_id' => $vendor->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Reconcile vendor balance and detect discrepancies
     *
     * @param Vendor $vendor
     * @return array Reconciliation results
     */
    public function reconcileVendorBalance(Vendor $vendor): array
    {
        $calculatedTotalEarnings = $this->calculateTotalEarnings($vendor);
        $calculatedWithdrawn = $this->calculateTotalWithdrawn($vendor);
        $calculatedPending = $this->calculatePendingBalance($vendor);
        $calculatedAvailable = $this->calculateAvailableBalance($vendor);

        $discrepancies = [];

        // Check for discrepancies
        if (abs($vendor->total_earnings - $calculatedTotalEarnings) > 0.01) {
            $discrepancies['total_earnings'] = [
                'stored' => $vendor->total_earnings,
                'calculated' => $calculatedTotalEarnings,
                'difference' => $vendor->total_earnings - $calculatedTotalEarnings,
            ];
        }

        if (abs($vendor->withdrawn_amount - $calculatedWithdrawn) > 0.01) {
            $discrepancies['withdrawn_amount'] = [
                'stored' => $vendor->withdrawn_amount,
                'calculated' => $calculatedWithdrawn,
                'difference' => $vendor->withdrawn_amount - $calculatedWithdrawn,
            ];
        }

        if (abs($vendor->pending_balance - $calculatedPending) > 0.01) {
            $discrepancies['pending_balance'] = [
                'stored' => $vendor->pending_balance,
                'calculated' => $calculatedPending,
                'difference' => $vendor->pending_balance - $calculatedPending,
            ];
        }

        if (abs($vendor->available_balance - $calculatedAvailable) > 0.01) {
            $discrepancies['available_balance'] = [
                'stored' => $vendor->available_balance,
                'calculated' => $calculatedAvailable,
                'difference' => $vendor->available_balance - $calculatedAvailable,
            ];
        }

        $hasDiscrepancies = !empty($discrepancies);

        // Auto-correct if discrepancies found
        if ($hasDiscrepancies) {
            Log::warning('Vendor balance discrepancies detected', [
                'vendor_id' => $vendor->id,
                'discrepancies' => $discrepancies,
            ]);

            $this->updateVendorBalance($vendor);
        }

        return [
            'vendor_id' => $vendor->id,
            'has_discrepancies' => $hasDiscrepancies,
            'discrepancies' => $discrepancies,
            'calculated_total_earnings' => $calculatedTotalEarnings,
            'calculated_withdrawn' => $calculatedWithdrawn,
            'calculated_pending' => $calculatedPending,
            'calculated_available' => $calculatedAvailable,
            'reconciled_at' => now()->toISOString(),
        ];
    }

    /**
     * Process vendor withdrawal
     *
     * @param Vendor $vendor
     * @param float $amount
     * @param string $description
     * @param array $metadata
     * @return array Result of withdrawal processing
     */
    public function processWithdrawal(Vendor $vendor, float $amount, string $description, array $metadata = []): array
    {
        if ($amount <= 0) {
            return [
                'success' => false,
                'message' => 'Withdrawal amount must be greater than zero.',
            ];
        }

        // Refresh vendor balance
        $this->updateVendorBalance($vendor);
        $vendor->refresh();

        if ($amount > $vendor->available_balance) {
            return [
                'success' => false,
                'message' => 'Insufficient available balance for withdrawal.',
                'available_balance' => $vendor->available_balance,
                'requested_amount' => $amount,
            ];
        }

        try {
            return DB::transaction(function () use ($vendor, $amount, $description, $metadata) {
                // Create withdrawal transaction
                $transaction = VendorTransaction::create([
                    'vendor_id' => $vendor->id,
                    'type' => 'withdrawal',
                    'amount' => -$amount, // Negative for withdrawal
                    'description' => $description,
                    'metadata' => $metadata,
                ]);

                // Update vendor balance
                $this->updateVendorBalance($vendor);

                Log::info('Vendor withdrawal processed', [
                    'vendor_id' => $vendor->id,
                    'transaction_id' => $transaction->id,
                    'amount' => $amount,
                    'description' => $description,
                    'new_available_balance' => $vendor->available_balance - $amount,
                ]);

                return [
                    'success' => true,
                    'message' => 'Withdrawal processed successfully.',
                    'transaction_id' => $transaction->id,
                    'amount' => $amount,
                    'new_available_balance' => $vendor->available_balance - $amount,
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to process vendor withdrawal', [
                'vendor_id' => $vendor->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process withdrawal. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get vendor balance history
     *
     * @param Vendor $vendor
     * @param int $days Number of days to look back
     * @return array Balance history
     */
    public function getBalanceHistory(Vendor $vendor, int $days = 30): array
    {
        $transactions = VendorTransaction::where('vendor_id', $vendor->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'asc')
            ->get();

        $history = [];
        $runningBalance = 0;

        foreach ($transactions as $transaction) {
            $runningBalance += $transaction->amount;
            
            $history[] = [
                'transaction_id' => $transaction->id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'running_balance' => $runningBalance,
                'description' => $transaction->description,
                'created_at' => $transaction->created_at->toISOString(),
            ];
        }

        return $history;
    }

    /**
     * Validate balance integrity
     *
     * @param Vendor $vendor
     * @return array Validation results
     */
    public function validateBalanceIntegrity(Vendor $vendor): array
    {
        $reconciliation = $this->reconcileVendorBalance($vendor);
        
        return [
            'vendor_id' => $vendor->id,
            'is_valid' => !$reconciliation['has_discrepancies'],
            'discrepancies' => $reconciliation['discrepancies'],
            'validated_at' => now()->toISOString(),
        ];
    }

    /**
     * Get comprehensive vendor financial summary
     *
     * @param Vendor $vendor
     * @return array Financial summary
     */
    public function getVendorFinancialSummary(Vendor $vendor): array
    {
        $totalEarnings = $this->calculateTotalEarnings($vendor);
        $totalCommissions = VendorTransaction::where('vendor_id', $vendor->id)
            ->where('type', 'commission')
            ->sum('amount');
        $totalWithdrawals = VendorTransaction::where('vendor_id', $vendor->id)
            ->where('type', 'withdrawal')
            ->sum('amount');
        
        $transactionCount = VendorTransaction::where('vendor_id', $vendor->id)->count();
        $pendingBalance = $this->calculatePendingBalance($vendor);
        $availableBalance = $this->calculateAvailableBalance($vendor);

        return [
            'vendor_id' => $vendor->id,
            'total_earnings' => $totalEarnings,
            'total_commissions' => abs($totalCommissions),
            'total_withdrawals' => abs($totalWithdrawals),
            'pending_balance' => $pendingBalance,
            'available_balance' => $availableBalance,
            'transaction_count' => $transactionCount,
            'last_transaction_date' => VendorTransaction::where('vendor_id', $vendor->id)
                ->latest()
                ->value('created_at'),
            'generated_at' => now()->toISOString(),
        ];
    }
}
