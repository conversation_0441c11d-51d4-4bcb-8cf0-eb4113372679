<?php

namespace Tests\Feature;

use App\Models\CheckoutSession;
use App\Models\Order;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PaystackWebhookTest extends TestCase
{
    use RefreshDatabase;

    protected $webhookSecret;
    protected $user;
    protected $vendor;
    protected $product;
    protected $checkoutSession;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->webhookSecret = 'test_webhook_secret';
        config(['services.paystack.secret' => $this->webhookSecret]);

        // Create test data
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'price' => 1000.00,
        ]);

        $this->checkoutSession = CheckoutSession::factory()->create([
            'user_id' => $this->user->id,
            'transaction_id' => 'test_transaction_123',
            'total_amount' => 1000.00,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function webhook_rejects_invalid_signature()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => 'invalid_signature',
        ]);

        $response->assertStatus(401);
        $response->assertJson(['status' => 'error', 'message' => 'Invalid signature']);
    }

    /** @test */
    public function webhook_accepts_valid_signature()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000, // 1000.00 in kobo
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        $response->assertStatus(200);
    }

    /** @test */
    public function webhook_prevents_replay_attacks()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000,
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        // First request should succeed
        $response1 = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);
        $response1->assertStatus(200);

        // Second identical request should be rejected (replay attack)
        $response2 = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);
        $response2->assertStatus(409);
        $response2->assertJson(['status' => 'error', 'message' => 'Duplicate webhook event']);
    }

    /** @test */
    public function webhook_requires_checkout_transaction_id_in_metadata()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000,
                'metadata' => [], // Missing checkout_transaction_id
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        $response->assertStatus(400);
        $response->assertJson(['status' => 'error', 'message' => 'Transaction ID missing']);
    }

    /** @test */
    public function webhook_processes_successful_payment()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000,
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        $response->assertStatus(200);

        // Verify checkout session was updated
        $this->checkoutSession->refresh();
        $this->assertEquals('completed', $this->checkoutSession->status);

        // Verify order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'payment_status' => 'paid',
            'payment_reference' => 'test_ref_123',
        ]);
    }

    /** @test */
    public function webhook_logs_all_events_properly()
    {
        Log::spy();

        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000,
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        Log::shouldHaveReceived('info')
            ->with('Paystack webhook received', \Mockery::type('array'))
            ->once();
    }

    /** @test */
    public function webhook_handles_invalid_checkout_session()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000,
                'metadata' => [
                    'checkout_transaction_id' => 'non_existent_transaction',
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        $response->assertStatus(404);
        $response->assertJson(['status' => 'error', 'message' => 'Checkout session not found']);
    }

    /** @test */
    public function webhook_ignores_non_success_events()
    {
        $payload = [
            'event' => 'charge.failed',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'failed',
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $response = $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        $response->assertStatus(200);

        // Verify checkout session was not updated
        $this->checkoutSession->refresh();
        $this->assertEquals('pending', $this->checkoutSession->status);
    }

    /** @test */
    public function webhook_calculates_commission_correctly()
    {
        config(['brandify.commission_rate' => 0.027]); // 2.7%

        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'status' => 'success',
                'amount' => 100000, // ₦1000.00
                'metadata' => [
                    'checkout_transaction_id' => $this->checkoutSession->transaction_id,
                ],
            ],
        ];

        $signature = hash_hmac('sha512', json_encode($payload), $this->webhookSecret);

        $this->postJson('/paystack/webhook', $payload, [
            'x-paystack-signature' => $signature,
        ]);

        // Verify commission was calculated correctly
        $this->assertDatabaseHas('commissions', [
            'vendor_id' => $this->vendor->id,
            'amount' => 27.00, // 2.7% of ₦1000
        ]);

        // Verify vendor earnings
        $this->assertDatabaseHas('vendor_transactions', [
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        $this->assertDatabaseHas('vendor_transactions', [
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
        ]);
    }

    /** @test */
    public function webhook_test_endpoint_is_accessible()
    {
        $response = $this->get('/paystack/webhook/test');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'timestamp',
            'environment',
            'webhook_url',
            'callback_url',
        ]);
    }

    /** @test */
    public function webhook_handles_missing_secret_configuration()
    {
        config(['services.paystack.secret' => null]);

        $payload = [
            'event' => 'charge.success',
            'data' => ['reference' => 'test_ref_123'],
        ];

        $response = $this->postJson('/paystack/webhook', $payload);

        $response->assertStatus(500);
        $response->assertJson(['status' => 'error', 'message' => 'Webhook secret not configured']);
    }
}
