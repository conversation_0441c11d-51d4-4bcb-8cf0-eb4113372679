<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * RACE CONDITION FIX: Create stock reservations table for temporary stock holds
     */
    public function up(): void
    {
        Schema::create('stock_reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('quantity');
            $table->string('reservation_id')->unique(); // UUID or session-based ID
            $table->timestamp('expires_at'); // When the reservation expires
            $table->string('status')->default('active'); // active, consumed, expired
            $table->string('type')->default('cart'); // cart, checkout, order
            $table->json('metadata')->nullable(); // Additional data (user_id, session_id, etc.)
            $table->timestamps();

            // Indexes for performance
            $table->index('product_id');
            $table->index('reservation_id');
            $table->index('expires_at');
            $table->index(['product_id', 'status']);
            $table->index(['expires_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_reservations');
    }
};
