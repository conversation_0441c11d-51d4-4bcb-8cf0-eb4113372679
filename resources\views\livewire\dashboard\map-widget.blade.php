<div>
    <div id="map" style="height: 500px;"></div>

    @push('scripts')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        document.addEventListener('livewire:load', function () {
            var map = L.map('map').setView([9.0820, 8.6753], 6);

            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            var orderData = @json($orderData);

            fetch('/geojson/nigeria-states.json')
                .then(response => response.json())
                .then(data => {
                    L.geo<PERSON>son(data, {
                        style: function (feature) {
                            var stateName = feature.properties.state;
                            var orderCount = orderData[stateName] || 0;
                            return {
                                fillColor: getColor(orderCount),
                                weight: 2,
                                opacity: 1,
                                color: 'white',
                                dashArray: '3',
                                fillOpacity: 0.7
                            };
                        },
                        onEachFeature: function (feature, layer) {
                            var stateName = feature.properties.state;
                            var orderCount = orderData[stateName] || 0;
                            layer.bindPopup('State: ' + stateName + '<br>Orders: ' + orderCount);
                        }
                    }).addTo(map);
                });

            function getColor(d) {
                return d > 1000 ? '#800026' :
                       d > 500  ? '#BD0026' :
                       d > 200  ? '#E31A1C' :
                       d > 100  ? '#FC4E2A' :
                       d > 50   ? '#FD8D3C' :
                       d > 20   ? '#FEB24C' :
                       d > 10   ? '#FED976' :
                                  '#FFEDA0';
            }
        });
    </script>
    @endpush
</div>