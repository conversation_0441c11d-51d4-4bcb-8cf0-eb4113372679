@extends('layouts.app')

@section('title', 'About Us')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
    <!-- Hero Section with Mobile Spacing -->
    <div class="relative overflow-hidden bg-gradient-to-r from-gray-800 via-slate-900 to-black mt-6 sm:mt-8 md:mt-4 lg:mt-0">
        <div class="absolute top-0 left-0 right-0 bottom-0 bg-black/30"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 md:py-24 lg:py-32">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
                    Curating the Future of
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-violet-500">Fashion</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto mb-8 leading-relaxed">
                    Brandify is the definitive online destination where style-setters discover exclusive fashion collections from the world's most exciting emerging and established designers.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('products.index') }}"
                       class="inline-flex items-center px-8 py-4 bg-white text-gray-900 font-semibold rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Explore Products
                    </a>
                    <a href="{{ route('vendor.register') }}"
                       class="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0V6a2 2 0 00-2-2H6a2 2 0 00-2 2v0M16 6v12a2 2 0 002 2h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2z"></path>
                        </svg>
                        Become a Vendor
                    </a>
                </div>
            </div>
        </div>

        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full"></div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <!-- Our Vision Section -->
        <div class="grid lg:grid-cols-2 gap-16 items-center mb-32">
            <div class="order-2 lg:order-1">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1551803091-e2ab652d5074?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                         alt="Fashion designer's studio"
                         class="rounded-2xl shadow-2xl w-full">
                    <div class="absolute -bottom-6 -right-6 w-24 h-24 bg-gradient-to-r from-pink-500 to-violet-600 rounded-2xl flex items-center justify-center">
                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="order-1 lg:order-2">
                <div class="inline-flex items-center px-4 py-2 bg-pink-100 text-pink-800 rounded-full text-sm font-medium mb-6">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                    </svg>
                    Our Vision
                </div>
                <h2 class="text-4xl font-bold text-gray-900 mb-6">From Vision to Vogue</h2>
                <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                    Born from a passion for style and a desire to celebrate unique expression, Brandify was created to connect discerning individuals with extraordinary designers. We saw a world of creativity waiting to be discovered and a community eager for authentic, high-quality fashion.
                </p>
                <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                    We are committed to building a vibrant ecosystem for fashion. Our platform not only offers exclusive, curated collections but also empowers designers with the global stage they deserve, fostering a community where style and creativity thrive.
                </p>
                <div class="flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">1000+</div>
                        <div class="text-sm text-gray-500">Active Vendors</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">50K+</div>
                        <div class="text-sm text-gray-500">Happy Customers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-indigo-600">100K+</div>
                        <div class="text-sm text-gray-500">Products Sold</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mission & Values Section -->
        <div class="bg-gradient-to-r from-gray-900 to-black rounded-3xl p-12 mb-32 relative overflow-hidden">
            <div class="absolute top-0 left-0 right-0 bottom-0 bg-gradient-to-r from-pink-500/10 to-violet-500/10"></div>
            <div class="relative z-10">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-white mb-6">Our Mission & Values</h2>
                    <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                        Redefining the fashion landscape with curation, creativity, and connection.
                    </p>
                </div>
                <div class="grid lg:grid-cols-2 gap-12">
                    <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-violet-600 rounded-xl flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-white">Our Mission</h3>
                        </div>
                        <p class="text-gray-300 leading-relaxed">
                            To be the world's leading online destination for exclusive fashion, empowering visionary designers and inspiring individuals through a masterfully curated style experience that transcends trends.
                        </p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-cyan-500 rounded-xl flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-white">Our Core Values</h3>
                        </div>
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-400 mr-3 flex-shrink-0 mt-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300"><strong class="text-white">Curation:</strong> We are dedicated to a standard of exceptional style and quality.</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-400 mr-3 flex-shrink-0 mt-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300"><strong class="text-white">Empowerment:</strong> We champion designers by giving them a global stage.</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-400 mr-3 flex-shrink-0 mt-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300"><strong class="text-white">Authenticity:</strong> We foster trust through genuine style and transparent practices.</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-400 mr-3 flex-shrink-0 mt-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300"><strong class="text-white">Innovation:</strong> We embrace cutting-edge technology to enhance the fashion experience.</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Pillars Section -->
        <div class="mb-32">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-6">What We Stand For</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our core pillars guide everything we do, from product development to customer service
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="group bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0V6a2 2 0 00-2-2H6a2 2 0 00-2 2v0M16 6v12a2 2 0 002 2h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-gray-900 mb-4 text-center">Empower Vendors</h4>
                    <p class="text-gray-600 text-center leading-relaxed">We provide powerful tools and a supportive platform for vendors to grow their online business with minimal hassle.</p>
                </div>

                <div class="group bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-gray-900 mb-4 text-center">Connect Communities</h4>
                    <p class="text-gray-600 text-center leading-relaxed">We bridge the gap between talented creators and customers looking for unique, quality products and experiences.</p>
                </div>

                <div class="group bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-gray-900 mb-4 text-center">Support Local</h4>
                    <p class="text-gray-600 text-center leading-relaxed">We prioritize local businesses and creators, helping to build sustainable economic growth across our communities.</p>
                </div>
            </div>
        </div>

        <!-- Team Section -->
        <div class="mb-32">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-6">Meet Our Team</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Passionate individuals working together to revolutionize Nigerian e-commerce
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="relative mb-6">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                             alt="CEO"
                             class="w-32 h-32 rounded-full mx-auto shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white"></div>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-2">John Adebayo</h4>
                    <p class="text-blue-600 font-medium mb-2">Chief Executive Officer</p>
                    <p class="text-gray-600 text-sm">Visionary leader with 10+ years in tech and e-commerce</p>
                </div>

                <div class="text-center group">
                    <div class="relative mb-6">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                             alt="CTO"
                             class="w-32 h-32 rounded-full mx-auto shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white"></div>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-2">Sarah Okafor</h4>
                    <p class="text-purple-600 font-medium mb-2">Chief Technology Officer</p>
                    <p class="text-gray-600 text-sm">Tech innovator passionate about scalable solutions</p>
                </div>

                <div class="text-center group">
                    <div class="relative mb-6">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                             alt="CMO"
                             class="w-32 h-32 rounded-full mx-auto shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white"></div>
                    </div>
                    <h4 class="text-xl font-bold text-gray-900 mb-2">Michael Eze</h4>
                    <p class="text-indigo-600 font-medium mb-2">Chief Marketing Officer</p>
                    <p class="text-gray-600 text-sm">Brand strategist focused on community building</p>
                </div>
            </div>
        </div>

        <!-- Call to Action Section -->
        <div class="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-3xl p-16 text-center">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10">
                <h2 class="text-4xl font-bold text-white mb-6">Join Our Community</h2>
                <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Whether you're looking to discover your next signature piece or grow your fashion brand, Brandify is the place for you. Join a global community of style enthusiasts and visionary designers.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('products.index') }}"
                       class="inline-flex items-center px-8 py-4 bg-white text-gray-900 font-semibold rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Discover Products
                    </a>
                    <a href="{{ route('vendor.register') }}"
                       class="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0V6a2 2 0 00-2-2H6a2 2 0 00-2 2v0M16 6v12a2 2 0 002 2h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2z"></path>
                        </svg>
                        Become a Vendor
                    </a>
                </div>
            </div>

            <!-- Decorative elements -->
            <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
                <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full"></div>
                <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full"></div>
            </div>
        </div>
    </div>
</div>
@endsection
