<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Log;

abstract class SafeComponent extends Component
{
    /**
     * Safe method to handle component mounting with error catching
     */
    public function mount(...$args)
    {
        try {
            if (method_exists($this, 'safeMount')) {
                return $this->safeMount(...$args);
            }
        } catch (\Exception $e) {
            Log::error('SafeComponent mount error', [
                'component' => get_class($this),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Don't let the error propagate in production
            if (app()->environment('production')) {
                return;
            }
            
            throw $e;
        }
    }
    
    /**
     * Safe method to handle component rendering with error catching
     */
    public function render()
    {
        try {
            if (method_exists($this, 'safeRender')) {
                return $this->safeRender();
            }
            
            // Default render behavior
            $view = str_replace('\\', '.', strtolower(get_class($this)));
            $view = str_replace('app.livewire.', 'livewire.', $view);
            return view($view);
            
        } catch (\Exception $e) {
            Log::error('SafeComponent render error', [
                'component' => get_class($this),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return error view in development, safe fallback in production
            if (app()->environment('production')) {
                return view('errors.component-error');
            }
            
            return view('errors.component-debug', ['error' => $e]);
        }
    }
    
    /**
     * Safe array access helper
     */
    protected function safeArrayGet($array, $key, $default = null)
    {
        if (!is_array($array)) {
            Log::warning('safeArrayGet called with non-array', [
                'component' => get_class($this),
                'key' => $key,
                'array_type' => gettype($array),
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
            return $default;
        }
        
        return $array[$key] ?? $default;
    }
    
    /**
     * Safe array key exists check
     */
    protected function safeArrayKeyExists($key, $array)
    {
        if (!is_array($array)) {
            Log::warning('safeArrayKeyExists called with non-array', [
                'component' => get_class($this),
                'key' => $key,
                'array_type' => gettype($array),
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
            return false;
        }
        
        return array_key_exists($key, $array);
    }
}
