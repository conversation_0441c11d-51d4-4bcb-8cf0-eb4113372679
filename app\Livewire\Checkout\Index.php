<?php

namespace App\Livewire\Checkout;

use App\Helpers\Location;
use App\Models\User;
use App\Services\ShipBubbleService;
use App\Traits\CheckoutValidationRules;
use App\Livewire\Forms\CheckoutForm;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

#[Layout('layouts.app')]
class Index extends Component
{
    use CheckoutValidationRules;

    // CRITICAL FIX: Add missing shippingAddress property
    public $shippingAddress = [
        'address' => '',
        'city' => '',
        'lga' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'NG',
        'phone' => '',
    ];

    // CRITICAL FIX: Add missing customer information properties
    public $first_name = '';
    public $last_name = '';
    public $email = '';
    public $phone = '';
    public $payment_method = '';

    public CheckoutForm $form;
    public $cartItems = [];
    public $subtotal = 0;

    public $states = [];
    public $lgas = [];

    public $shippingRates = [];
    public $selectedShippingRate = null;
    public $shippingCost = 0;
    public $total = 0;

    // FIXED: Add loading states for better user feedback
    public $loadingShippingRates = false;
    public $processingPayment = false;
    public $loadingLgas = false;

    /**
     * CRITICAL FIX: Dynamic validation rules based on checkout stage
     * Only require customer info when actually proceeding to payment
     */
    protected function rules(): array
    {
        // For shipping rate requests, only validate address fields
        if ($this->isRequestingShippingRates()) {
            return [
                'shippingAddress.address' => 'required|string|min:10|max:255',
                'shippingAddress.city' => 'required|string|min:2|max:255',
                'shippingAddress.state' => 'required|string|max:255',
                'shippingAddress.lga' => 'nullable|string|max:255',
                'shippingAddress.phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
            ];
        }

        // For payment processing, validate all fields
        return array_merge(
            // Personal information validation (only required for payment)
            [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => $this->getNigerianPhoneValidationRule(),
                'payment_method' => $this->getPaymentMethodValidationRule(),
            ],
            // Shipping address validation with prefix
            $this->getPrefixedCheckoutValidationRules(
                prefix: 'shippingAddress',
                requirePaymentMethod: false, // Already validated above
                requireShippingOptions: false // Validated separately in proceedToPayment
            )
        );
    }

    /**
     * Determine if we're currently requesting shipping rates vs processing payment
     */
    private function isRequestingShippingRates(): bool
    {
        // If customer info is empty but address is being filled, we're getting rates
        return empty($this->first_name) &&
               empty($this->last_name) &&
               empty($this->email) &&
               !empty($this->shippingAddress['address']);
    }

    /**
     * Check if user has started filling customer information
     */
    private function hasStartedCustomerInfo(): bool
    {
        return !empty($this->first_name) ||
               !empty($this->last_name) ||
               !empty($this->email) ||
               !empty($this->phone) ||
               !empty($this->payment_method);
    }

    /**
     * Get custom validation messages
     */
    protected function messages(): array
    {
        return array_merge(
            $this->getCheckoutValidationMessages(),
            $this->getCheckoutValidationMessages('shippingAddress')
        );
    }

    /**
     * Get validation attributes for better error messages
     */
    protected function validationAttributes(): array
    {
        return array_merge(
            $this->getCheckoutValidationAttributes(),
            $this->getCheckoutValidationAttributes('shippingAddress')
        );
    }



    public function mount()
    {
        \Log::info('Checkout Component Mount Started', [
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString(),
            'request_url' => request()->url()
        ]);

        // SECURITY FIX: Ensure user is authenticated for checkout
        if (!Auth::check()) {
            \Log::warning('Checkout Access Denied - User Not Authenticated', [
                'session_id' => session()->getId(),
                'request_url' => request()->url()
            ]);
            session(['intended_url' => request()->url()]);
            return redirect()->route('login')->with('error', 'Please login to proceed with checkout.');
        }

        try {
            // Ensure shippingAddress is always an array
            if (!is_array($this->shippingAddress)) {
                \Log::warning('shippingAddress was not an array, resetting', [
                    'type' => gettype($this->shippingAddress),
                    'value' => $this->shippingAddress,
                    'user_id' => Auth::id()
                ]);
                $this->shippingAddress = [
                    'address' => '',
                    'city' => '',
                    'lga' => '',
                    'state' => '',
                    'postal_code' => '',
                    'country' => 'NG',
                    'phone' => '',
                ];
            }

            // Add debugging for cart items
            $cartItems = session('cart', []);
            \Log::info('Checkout mount - cart items', [
                'cart_items_type' => gettype($cartItems),
                'cart_items_count' => is_array($cartItems) ? count($cartItems) : 'not_array',
                'cart_items_sample' => is_array($cartItems) ? array_slice($cartItems, 0, 2, true) : $cartItems,
                'user_id' => Auth::id()
            ]);

            $this->cartItems = $cartItems;
            if (empty($this->cartItems)) {
                return redirect()->route('cart.index');
            }

            $this->calculateSubtotal();
        } catch (\Exception $e) {
            \Log::error('Error in checkout mount', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }

        /** @var User $user */
        $user = Auth::user();

        // CRITICAL FIX: Initialize customer information from authenticated user
        $this->first_name = $user->first_name ?? '';
        $this->last_name = $user->last_name ?? '';
        $this->email = $user->email ?? '';
        $this->phone = $user->phone ?? '';

        if ($user && $user->shippingAddress) {
            try {
                // Ensure shippingAddress is an object before calling toArray()
                if (is_object($user->shippingAddress) && method_exists($user->shippingAddress, 'toArray')) {
                    $userShippingData = $user->shippingAddress->toArray();

                    // Safely merge with default structure
                    $this->shippingAddress = array_merge($this->shippingAddress, array_filter($userShippingData, function($value) {
                        return !is_null($value) && $value !== '';
                    }));
                } else {
                    \Log::warning('User shipping address is not a valid object', [
                        'user_id' => $user->id,
                        'shipping_address_type' => gettype($user->shippingAddress),
                        'shipping_address_value' => $user->shippingAddress
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Error processing user shipping address', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }

        // Load states and LGAs
        try {
            $this->states = Location::getStates();
            \Log::info('States loaded in checkout mount', [
                'states_count' => count($this->states),
                'first_few_states' => array_slice(array_keys($this->states), 0, 3)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading states', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            $this->states = [];
        }

        // If user already has a state selected, load the LGAs
        if (!empty($this->shippingAddress['state'])) {
            try {
                $this->lgas = Location::getLgas($this->shippingAddress['state']);
                \Log::info('Initial LGAs loaded in mount', [
                    'state' => $this->shippingAddress['state'],
                    'lgas_count' => count($this->lgas)
                ]);
            } catch (\Exception $e) {
                \Log::error('Error loading initial LGAs', [
                    'state' => $this->shippingAddress['state'],
                    'error' => $e->getMessage()
                ]);
                $this->lgas = [];
            }
        } else {
            $this->lgas = [];
        }

        $this->calculateTotal();
    }



    public function updatedShippingAddressState($state)
    {
        \Log::info('State updated in checkout', ['state' => $state]);

        try {
            // Start loading state
            $this->loadingLgas = true;

            // Clear previous LGA selection
            $this->shippingAddress['lga'] = '';

            // Load LGAs for the selected state
            if (!empty($state)) {
                $this->lgas = Location::getLgas($state);

                \Log::info('LGAs loaded for state', [
                    'state' => $state,
                    'lgas_count' => count($this->lgas),
                    'lgas' => $this->lgas
                ]);

                // Verify the LGAs were actually loaded
                if (empty($this->lgas)) {
                    $this->addError('shippingAddress.state', 'No LGAs found for the selected state. Please try a different state.');
                }
            } else {
                $this->lgas = [];
            }

            // Reset shipping rates when state changes
            $this->resetShipping();

        } catch (\Exception $e) {
            \Log::error('Error updating state', [
                'state' => $state,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            $this->addError('shippingAddress.state', 'Error loading LGAs for this state. Please try again.');
            $this->lgas = [];
        } finally {
            // End loading state
            $this->loadingLgas = false;
        }
    }

    public function updatedShippingAddressLga($lga)
    {
        // Validate LGA belongs to selected state
        if (!empty($lga) && !empty($this->shippingAddress['state'])) {
            $validLgas = Location::getLgas($this->shippingAddress['state']);

            if (!in_array($lga, $validLgas)) {
                $this->addError('shippingAddress.lga', 'Please select a valid LGA for the selected state.');
                $this->shippingAddress['lga'] = '';
                return;
            } else {
                // Clear any previous LGA errors
                $this->resetErrorBag('shippingAddress.lga');
            }
        }

        // Reset shipping rates when LGA changes
        $this->resetShipping();
    }

    /**
     * Manual method to refresh LGAs for the selected state
     */
    public function refreshLgas()
    {
        if (!empty($this->shippingAddress['state'])) {
            $this->loadingLgas = true;
            $this->lgas = Location::getLgas($this->shippingAddress['state']);
            $this->loadingLgas = false;

            \Log::info('LGAs manually refreshed', [
                'state' => $this->shippingAddress['state'],
                'lgas_count' => count($this->lgas)
            ]);
        }
    }

    public function getShippingRates()
    {
        // Set execution time limit to prevent timeouts
        set_time_limit(120); // 2 minutes max

        // CRITICAL FIX: Only validate address fields for shipping rate requests, not full customer info
        $this->validateShippingAddressOnly();

        // Additional business logic validation
        if (empty($this->cartItems)) {
            $this->addError('cart', 'Your cart is empty. Please add items before proceeding.');
            return;
        }

        // Check if address is complete before getting shipping rates
        if (!$this->isAddressComplete()) {
            $missingFields = $this->getMissingAddressFields();
            $this->addError('shipping', 'Please complete the following fields to get shipping rates: ' . implode(', ', $missingFields));
            return;
        }

        // FIXED: Add loading state for better UX
        $this->loadingShippingRates = true;
        $this->resetShipping();
        $this->resetErrorBag('shipping');

        try {
            // Ensure shippingAddress is an array before accessing
            if (!is_array($this->shippingAddress)) {
                \Log::error('shippingAddress is not an array in getShippingRates', [
                    'type' => gettype($this->shippingAddress),
                    'value' => $this->shippingAddress
                ]);
                $this->addError('shipping', 'Invalid shipping address data. Please refresh the page and try again.');
                return;
            }

            $shipBubbleService = new ShipBubbleService();

            // Validate that we have a customer phone number
            $customerPhone = $this->shippingAddress['phone'] ?? Auth::user()->phone ?? null;
            if (empty($customerPhone)) {
                $this->shippingError = 'Phone number is required for shipping calculation. Please update your profile or enter a phone number.';
                return;
            }

            // Prepare shipping address data for Shipbubble with safe array access
            $shippingAddressData = [
                'name' => $this->shippingAddress['name'] ?? Auth::user()->name ?? 'Customer',
                'email' => $this->shippingAddress['email'] ?? Auth::user()->email ?? '<EMAIL>',
                'phone' => $customerPhone,
                'address' => $this->shippingAddress['address'] ?? '',
                'city' => $this->shippingAddress['city'] ?? '',
                'state' => $this->shippingAddress['state'] ?? '',
                'lga' => $this->shippingAddress['lga'] ?? '',
                'postal_code' => $this->shippingAddress['postal_code'] ?? '',
                'country' => $this->shippingAddress['country'] ?? 'NG',
            ];

            // LOGIC FIX: Handle multi-vendor shipping properly
            // Group cart items by vendor to calculate shipping for each vendor
            $vendorGroups = [];
            foreach ($this->cartItems as $productId => $item) {
                $product = \App\Models\Product::find($productId);
                if ($product && $product->vendor) {
                    $vendorId = $product->vendor->id;
                    if (!isset($vendorGroups[$vendorId])) {
                        $vendorGroups[$vendorId] = [
                            'vendor' => $product->vendor,
                            'items' => []
                        ];
                    }
                    $vendorGroups[$vendorId]['items'][$productId] = $item;
                }
            }

            // For now, use the first vendor (TODO: implement proper multi-vendor shipping)
            $vendor = null;
            if (!empty($vendorGroups)) {
                $firstVendorGroup = array_values($vendorGroups)[0];
                $vendor = $firstVendorGroup['vendor'];
            }

            // Get shipping rates from Shipbubble with timeout protection
            $startTime = microtime(true);
            $response = $shipBubbleService->getShippingRates($this->cartItems, $shippingAddressData, $vendor);
            $executionTime = microtime(true) - $startTime;

            // Log execution time for monitoring
            \Log::info('ShipBubble API execution time', [
                'execution_time_seconds' => round($executionTime, 2),
                'vendor_id' => $vendor?->id,
                'response_status' => $response['status'] ?? 'unknown'
            ]);

            if (isset($response['status']) && $response['status'] === 'success' && isset($response['data']['couriers'])) {
                // Format the rates for display with enhanced descriptions
                $this->shippingRates = [];
                foreach ($response['data']['couriers'] as $index => $courier) {
                    $this->shippingRates[$index] = [
                        'courier_id' => $courier['courier_id'],
                        'courier_name' => $courier['courier_name'],
                        'service_code' => $courier['service_code'],
                        'total' => $courier['total'],
                        'currency' => $courier['currency'] ?? 'NGN',
                        'delivery_eta' => $courier['delivery_eta'] ?? 'Standard delivery',
                        'service_type' => $courier['service_type'] ?? 'pickup',
                        // Enhanced descriptive information
                        'description' => $this->generateShippingDescription($courier),
                        'estimated_delivery' => $this->formatDeliveryTime($courier),
                        'features' => $this->getShippingFeatures($courier),
                    ];
                }

                // Store the request token for later use when creating shipment
                if (isset($response['data']['request_token'])) {
                    session(['shipbubble_request_token' => $response['data']['request_token']]);
                }
            } else {
                // Handle different types of errors
                $errorType = $response['type'] ?? 'unknown';
                $errorMessage = $response['message'] ?? 'Could not retrieve shipping rates. Please try again.';

                if ($errorType === 'address_validation_error') {
                    $errorMessage = 'Unable to validate shipping address. Please check your address details and try again.';
                } elseif ($errorType === 'phone_validation_error') {
                    $errorMessage = 'Invalid phone number format. Please check your phone number and try again.';
                }

                $this->addError('shipping', $errorMessage);
                \Log::warning('Shipbubble API returned error', [
                    'response' => $response,
                    'error_type' => $errorType,
                    'execution_time' => $executionTime ?? 0
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Shipbubble rate request failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'address' => $this->shippingAddress,
                'cart_items_count' => count($this->cartItems),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // Provide user-friendly error messages based on error type
            $errorMessage = $this->getShippingErrorMessage($e);
            $this->addError('shipping', $errorMessage);

            // Offer fallback options
            $this->offerShippingFallback();
        } finally {
            // FIXED: Always reset loading state
            $this->loadingShippingRates = false;
        }
    }

    public function selectShippingRate($rateKey)
    {
        if (isset($this->shippingRates[$rateKey])) {
            $this->selectedShippingRate = $this->shippingRates[$rateKey];
            $this->shippingCost = $this->selectedShippingRate['total'] ?? 0;
            $this->calculateTotal();

            // Store selected shipping info for later use in payment
            session([
                'selected_shipping_rate' => $this->selectedShippingRate,
                'shipping_cost' => $this->shippingCost
            ]);
        }
    }

    private function calculateSubtotal()
    {
        $this->subtotal = collect($this->cartItems)->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
    }

    private function calculateTotal()
    {
        $this->total = $this->subtotal + $this->shippingCost;
    }

    /**
     * Comprehensive validation for the checkout process
     */
    private function validateCheckoutProcess()
    {
        $errors = [];

        // Validate cart
        if (empty($this->cartItems)) {
            $errors['cart'] = 'Your cart is empty. Please add items before checkout.';
        }

        // Validate shipping address
        if (empty($this->shippingAddress['address'])) {
            $errors['shippingAddress.address'] = 'Street address is required.';
        }

        if (empty($this->shippingAddress['city'])) {
            $errors['shippingAddress.city'] = 'City is required.';
        }

        if (empty($this->shippingAddress['state'])) {
            $errors['shippingAddress.state'] = 'State is required.';
        } elseif (!Location::stateExists($this->shippingAddress['state'])) {
            $errors['shippingAddress.state'] = 'Please select a valid state.';
        }

        if (empty($this->shippingAddress['lga'])) {
            $errors['shippingAddress.lga'] = 'LGA is required.';
        } elseif (!empty($this->shippingAddress['state']) && !Location::lgaExists($this->shippingAddress['state'], $this->shippingAddress['lga'])) {
            $errors['shippingAddress.lga'] = 'Please select a valid LGA for the selected state.';
        }

        // Validate shipping method
        if (!$this->selectedShippingRate) {
            $errors['shipping'] = 'Please select a shipping method.';
        }

        // Validate totals
        if ($this->total <= 0) {
            $errors['total'] = 'Invalid order total.';
        }

        // Add errors to the error bag
        foreach ($errors as $key => $message) {
            $this->addError($key, $message);
        }

        // Throw exception if there are errors
        if (!empty($errors)) {
            throw new \Exception('Checkout validation failed: ' . implode(', ', $errors));
        }
    }

    /**
     * LOGIC-CRITICAL-002 FIX: Enhanced validation method for Livewire
     * This provides immediate feedback to users as they fill the form
     */
    public function updated($propertyName)
    {
        // CRITICAL FIX: Only validate customer info fields if user is actually filling them out
        $customerInfoProperties = ['first_name', 'last_name', 'email', 'phone', 'payment_method'];
        $addressProperties = [
            'shippingAddress.address', 'shippingAddress.city', 'shippingAddress.state',
            'shippingAddress.lga', 'shippingAddress.phone', 'shippingAddress.postal_code'
        ];

        // Always validate address properties (needed for shipping rates)
        if (in_array($propertyName, $addressProperties)) {
            $this->validateOnly($propertyName);
        }

        // Only validate customer info if user has started filling it out
        if (in_array($propertyName, $customerInfoProperties)) {
            // Only validate if user has started the payment process (has some customer info)
            if ($this->hasStartedCustomerInfo()) {
                $this->validateOnly($propertyName);
            }
        }

        // Custom validation for state
        if ($propertyName === 'shippingAddress.state') {
            if (!empty($this->shippingAddress['state']) && !Location::stateExists($this->shippingAddress['state'])) {
                $this->addError('shippingAddress.state', 'Please select a valid state.');
            } else {
                $this->resetErrorBag('shippingAddress.state');
            }
        }

        // Custom validation for LGA
        if ($propertyName === 'shippingAddress.lga') {
            if (!empty($this->shippingAddress['lga']) && !empty($this->shippingAddress['state'])) {
                if (!Location::lgaExists($this->shippingAddress['state'], $this->shippingAddress['lga'])) {
                    $this->addError('shippingAddress.lga', 'Please select a valid LGA for the selected state.');
                } else {
                    $this->resetErrorBag('shippingAddress.lga');
                }
            }
        }
    }

    private function resetShipping()
    {
        $this->shippingRates = [];
        $this->selectedShippingRate = null;
        $this->shippingCost = 0;
        $this->calculateTotal();
    }

    /**
     * CRITICAL FIX: Validate only shipping address fields for rate requests
     */
    protected function validateShippingAddressOnly()
    {
        $this->validate([
            // Only validate shipping address fields, not customer personal info
            'shippingAddress.address' => 'required|string|min:10|max:255',
            'shippingAddress.city' => 'required|string|min:2|max:255',
            'shippingAddress.state' => 'required|string|max:255',
            'shippingAddress.lga' => 'nullable|string|max:255',
            'shippingAddress.phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
        ], [
            'shippingAddress.address.required' => 'Street address is required for shipping calculation.',
            'shippingAddress.address.min' => 'Please provide a more detailed address (at least 10 characters).',
            'shippingAddress.city.required' => 'City is required for shipping calculation.',
            'shippingAddress.state.required' => 'State is required for shipping calculation.',
            'shippingAddress.phone.required' => 'Phone number is required for delivery.',
            'shippingAddress.phone.regex' => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
        ]);
    }

    /**
     * CRITICAL FIX: Validate all fields including customer info for payment processing
     */
    protected function validateForPayment()
    {
        $this->validate([
            // Customer personal information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
            'payment_method' => 'required|in:paystack,bank_transfer,cash_on_delivery',

            // Shipping address
            'shippingAddress.address' => 'required|string|min:10|max:255',
            'shippingAddress.city' => 'required|string|min:2|max:255',
            'shippingAddress.state' => 'required|string|max:255',
            'shippingAddress.lga' => 'nullable|string|max:255',
            'shippingAddress.phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
        ], [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email address is required.',
            'phone.required' => 'Phone number is required for delivery.',
            'payment_method.required' => 'Please select a payment method.',
            'shippingAddress.address.required' => 'Street address is required.',
            'shippingAddress.city.required' => 'City is required.',
            'shippingAddress.state.required' => 'State is required.',
            'shippingAddress.phone.required' => 'Phone number is required for delivery.',
        ]);
    }

    /**
     * Check if the shipping address is complete enough to get shipping rates
     */
    public function isAddressComplete()
    {
        return !empty($this->shippingAddress['address']) &&
               !empty($this->shippingAddress['city']) &&
               !empty($this->shippingAddress['state']) &&
               !empty($this->shippingAddress['lga']) &&
               !empty($this->shippingAddress['phone']);
    }

    /**
     * Get missing address fields for user guidance
     */
    public function getMissingAddressFields()
    {
        $missing = [];
        if (empty($this->shippingAddress['address'])) $missing[] = 'Street Address';
        if (empty($this->shippingAddress['city'])) $missing[] = 'City';
        if (empty($this->shippingAddress['state'])) $missing[] = 'State';
        if (empty($this->shippingAddress['lga'])) $missing[] = 'LGA';
        if (empty($this->shippingAddress['phone'])) $missing[] = 'Phone Number';
        return $missing;
    }

    /**
     * Refresh cart with current product prices and availability
     */
    public function refreshCart()
    {
        $cart = session('cart', []);
        $updatedCart = [];
        $changes = [];

        foreach ($cart as $productId => $item) {
            $product = \App\Models\Product::find($productId);

            if (!$product || !$product->is_active) {
                $changes[] = "Removed '{$item['name']}' - no longer available";
                continue;
            }

            $currentPrice = $product->getCurrentPrice();
            $oldPrice = (float) $item['price'];

            // Update the cart item with current data
            $updatedItem = $item;
            $updatedItem['price'] = $currentPrice;
            $updatedItem['name'] = $product->name; // Update name in case it changed

            // Check stock and adjust quantity if needed
            $availableStock = $product->getStockLevel();
            if ($item['quantity'] > $availableStock) {
                if ($availableStock > 0) {
                    $updatedItem['quantity'] = $availableStock;
                    $changes[] = "Reduced quantity for '{$product->name}' to {$availableStock} (limited stock)";
                } else {
                    $changes[] = "Removed '{$product->name}' - out of stock";
                    continue;
                }
            }

            // Track price changes
            if (abs($currentPrice - $oldPrice) > 0.01) {
                $changes[] = "Updated price for '{$product->name}' from ₦{$oldPrice} to ₦{$currentPrice}";
            }

            $updatedCart[$productId] = $updatedItem;
        }

        // Update session cart
        session(['cart' => $updatedCart]);
        $this->cartItems = $updatedCart;
        $this->calculateSubtotal();
        $this->calculateTotal();

        // Reset shipping rates since cart changed
        $this->resetShipping();

        // Show changes to user
        if (!empty($changes)) {
            session()->flash('cart_updated', 'Your cart has been updated: ' . implode('; ', $changes));
        } else {
            session()->flash('cart_updated', 'Your cart is up to date.');
        }

        // Clear any existing errors
        $this->resetErrorBag();
    }

    public function proceedToPayment()
    {
        // SECURITY FIX: Ensure user is still authenticated
        if (!Auth::check()) {
            session()->flash('error', 'Session expired. Please login again.');
            return redirect()->route('login');
        }

        // SECURITY FIX: Validate cart is not empty and belongs to current session
        if (empty($this->cartItems)) {
            session()->flash('error', 'Your cart is empty. Please add items before checkout.');
            return redirect()->route('cart.index');
        }

        // FIXED: Add processing state for better UX
        $this->processingPayment = true;

        try {
            // Log the current state for debugging (with security context)
            \Log::info('Proceeding to payment', [
                'user_id' => Auth::id(),
                'session_id' => session()->getId(),
                'has_shipping_rate' => !empty($this->selectedShippingRate),
                'shipping_rates_count' => count($this->shippingRates),
                'cart_items_count' => count($this->cartItems),
                'shipping_address_filled' => !empty($this->shippingAddress['address']) && !empty($this->shippingAddress['city']),
                'ip_address' => request()->ip()
            ]);

            // CRITICAL FIX: Validate all fields including customer info for payment
            $this->validateForPayment();

            // Validate product availability and stock
            $this->validateProductAvailability();

            // Check if user needs to get shipping rates first
            if (empty($this->shippingRates)) {
                $this->addError('shipping', 'Please get shipping rates first by filling in your address completely.');
                return;
            }

            // Validate shipping rate separately with better error messages
            if (!$this->selectedShippingRate) {
                $this->addError('shipping', 'Please select a shipping method before proceeding.');
                return;
            }

            // Validate shipping rate structure
            if (!is_array($this->selectedShippingRate)) {
                $this->addError('shipping', 'Invalid shipping rate selected. Please refresh and try again.');
                return;
            }

            if (!isset($this->selectedShippingRate['courier_id']) || empty($this->selectedShippingRate['courier_id'])) {
                $this->addError('shipping', 'Please select a valid courier for shipping.');
                return;
            }

            if (!isset($this->selectedShippingRate['total']) || !is_numeric($this->selectedShippingRate['total'])) {
                $this->addError('shipping', 'Invalid shipping cost. Please refresh and try again.');
                return;
            }

            if (empty($this->cartItems)) {
                $this->addError('cart', 'Your cart is empty. Please add items before checkout.');
                return;
            }

            if ($this->total <= 0) {
                $this->addError('total', 'Invalid order total. Please refresh and try again.');
                return;
            }

            // Ensure shippingAddress is an array before accessing
            if (!is_array($this->shippingAddress)) {
                \Log::error('shippingAddress is not an array in proceedToPayment', [
                    'type' => gettype($this->shippingAddress),
                    'value' => $this->shippingAddress
                ]);
                $this->addError('shipping', 'Invalid shipping address data. Please refresh the page and try again.');
                return;
            }

            // FIXED: Prepare payment data and submit directly to PaymentController with safe array access
            // Group cart items by vendor to create proper shipping options structure
            $vendorShippingOptions = [];
            foreach ($this->cartItems as $productId => $item) {
                $product = \App\Models\Product::find($productId);
                if ($product && $product->vendor) {
                    $vendorId = $product->vendor->id;
                    // Use the selected shipping rate for all vendors (for now)
                    // Ensure the structure matches what PaymentController expects
                    $vendorShippingOptions[$vendorId] = [
                        'courier_id' => $this->selectedShippingRate['courier_id'] ?? null,
                        'service_code' => $this->selectedShippingRate['service_code'] ?? null,
                        'total' => $this->selectedShippingRate['total'] ?? 0,
                        'courier_name' => $this->selectedShippingRate['courier_name'] ?? null,
                    ];
                }
            }

            // Validate that we have shipping options for all vendors
            if (empty($vendorShippingOptions)) {
                $this->addError('shipping', 'No shipping options found. Please select a shipping method.');
                return;
            }

            // Fallback: if no vendor shipping options found, create a default one
            if (empty($vendorShippingOptions) && $this->selectedShippingRate) {
                // Get the first vendor from cart items as fallback
                foreach ($this->cartItems as $productId => $item) {
                    $product = \App\Models\Product::find($productId);
                    if ($product && $product->vendor) {
                        $vendorShippingOptions[$product->vendor->id] = $this->selectedShippingRate;
                        break; // Only need one vendor for fallback
                    }
                }
            }

            // Log shipping options for debugging
            \Log::info('Checkout shipping options prepared', [
                'vendor_shipping_options' => $vendorShippingOptions,
                'selected_shipping_rate' => $this->selectedShippingRate,
                'cart_items_count' => count($this->cartItems)
            ]);

            // SECURITY FIX: Sanitize and validate payment data
            $user = Auth::user();
            $nameParts = explode(' ', trim($user->name), 2);

            $paymentData = [
                'first_name' => htmlspecialchars(trim($nameParts[0] ?? 'Customer'), ENT_QUOTES, 'UTF-8'),
                'last_name' => htmlspecialchars(trim($nameParts[1] ?? ''), ENT_QUOTES, 'UTF-8'),
                'email' => filter_var($user->email, FILTER_SANITIZE_EMAIL),
                'phone' => preg_replace('/[^0-9+]/', '', $this->shippingAddress['phone'] ?? '08000000000'),
                'street' => htmlspecialchars(trim($this->shippingAddress['address'] ?? ''), ENT_QUOTES, 'UTF-8'),
                'city' => htmlspecialchars(trim($this->shippingAddress['city'] ?? ''), ENT_QUOTES, 'UTF-8'),
                'lga' => htmlspecialchars(trim($this->shippingAddress['lga'] ?? ''), ENT_QUOTES, 'UTF-8'),
                'state' => htmlspecialchars(trim($this->shippingAddress['state'] ?? ''), ENT_QUOTES, 'UTF-8'),
                'postal_code' => preg_replace('/[^0-9A-Za-z\-\s]/', '', $this->shippingAddress['postal_code'] ?? ''),
                'country' => 'NG', // Force to Nigeria for security
                'payment_method' => 'paystack', // Force to paystack for security
                'shipping_options' => $vendorShippingOptions,
                'user_id' => $user->id, // Add user ID for verification
                'session_id' => session()->getId(), // Add session ID for verification
            ];

            // SECURITY FIX: Validate email format
            if (!filter_var($paymentData['email'], FILTER_VALIDATE_EMAIL)) {
                $this->addError('checkout', 'Invalid email address. Please update your profile.');
                return;
            }

            // SECURITY FIX: Validate phone number format
            if (!preg_match('/^0[789][01]\d{8}$/', $paymentData['phone'])) {
                $this->addError('checkout', 'Invalid phone number format. Please use a valid Nigerian phone number.');
                return;
            }

            // Store payment data in session for the redirect
            session(['payment_data' => $paymentData]);

            // FIXED: Add success feedback
            session()->flash('checkout_success', 'Proceeding to payment...');

            // Redirect to a GET route that will handle the payment initialization
            return redirect()->route('payment.initialize');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Let Livewire handle validation errors
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Checkout process failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'cart_items_count' => count($this->cartItems),
                'total' => $this->total
            ]);
            $this->addError('checkout', 'An error occurred during checkout. Please try again.');
        } finally {
            $this->processingPayment = false;
        }
    }

    /**
     * Get user-friendly error message based on exception type
     */
    private function getShippingErrorMessage(\Exception $e)
    {
        $message = $e->getMessage();

        // Check for specific error patterns
        if (str_contains($message, 'phone number')) {
            return 'There seems to be an issue with the phone number format. Please check your contact information and try again.';
        }

        if (str_contains($message, 'address validation')) {
            return 'We couldn\'t validate your shipping address. Please double-check your address details and try again.';
        }

        if (str_contains($message, 'timeout') || str_contains($message, 'execution time')) {
            return 'The shipping calculation is taking longer than expected. Please try again in a moment.';
        }

        if (str_contains($message, 'connection') || str_contains($message, 'network')) {
            return 'We\'re having trouble connecting to our shipping service. Please check your internet connection and try again.';
        }

        if (str_contains($message, 'API key') || str_contains($message, 'authentication')) {
            return 'There\'s a temporary issue with our shipping service. Please try again later or contact support.';
        }

        // Default user-friendly message
        return 'We couldn\'t calculate shipping rates at this time. Please check your address details and try again. If the problem continues, please contact our support team.';
    }

    /**
     * Offer fallback shipping options when API fails
     */
    private function offerShippingFallback()
    {
        // Create fallback shipping rates
        $fallbackRates = [
            'standard' => [
                'courier_name' => 'Standard Delivery',
                'description' => 'Estimated delivery in 3-5 business days',
                'total' => 1500, // ₦15.00
                'delivery_time' => '3-5 business days',
                'courier_id' => 'fallback_standard',
                'is_fallback' => true
            ],
            'express' => [
                'courier_name' => 'Express Delivery',
                'description' => 'Estimated delivery in 1-2 business days',
                'total' => 2500, // ₦25.00
                'delivery_time' => '1-2 business days',
                'courier_id' => 'fallback_express',
                'is_fallback' => true
            ]
        ];

        $this->shippingRates = $fallbackRates;

        // Add informational message
        session()->flash('shipping_fallback', 'We\'re showing estimated shipping rates. Final rates will be confirmed before payment.');
    }

    /**
     * Generate descriptive information for shipping methods
     */
    private function generateShippingDescription($courier)
    {
        $courierName = strtolower($courier['courier_name'] ?? '');
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // Generate description based on courier and service type
        if (str_contains($serviceCode, 'same_day') || str_contains($serviceCode, 'express')) {
            return 'Fast delivery service with same-day or next-day delivery options. Perfect for urgent orders.';
        } elseif (str_contains($serviceCode, 'standard') || str_contains($serviceCode, 'regular')) {
            return 'Reliable standard delivery service with tracking. Most economical option for regular deliveries.';
        } elseif (str_contains($serviceCode, 'premium') || str_contains($serviceCode, 'priority')) {
            return 'Premium delivery service with enhanced handling and faster processing times.';
        } elseif (str_contains($courierName, 'dhl') || str_contains($courierName, 'fedex')) {
            return 'International courier service with global tracking and insurance options.';
        } elseif (str_contains($courierName, 'gig') || str_contains($courierName, 'kwik')) {
            return 'Local delivery service specializing in fast, reliable deliveries within major cities.';
        } else {
            return 'Professional delivery service with tracking and customer support.';
        }
    }

    /**
     * Format delivery time information
     */
    private function formatDeliveryTime($courier)
    {
        $deliveryEta = $courier['delivery_eta'] ?? '';
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // If we have specific ETA, use it
        if (!empty($deliveryEta) && $deliveryEta !== 'Standard delivery') {
            return $deliveryEta;
        }

        // Generate estimated delivery time based on service code
        if (str_contains($serviceCode, 'same_day')) {
            return 'Same day delivery (order before 2 PM)';
        } elseif (str_contains($serviceCode, 'express') || str_contains($serviceCode, 'next_day')) {
            return '1-2 business days';
        } elseif (str_contains($serviceCode, 'standard') || str_contains($serviceCode, 'regular')) {
            return '3-5 business days';
        } elseif (str_contains($serviceCode, 'economy')) {
            return '5-7 business days';
        } else {
            return '2-4 business days';
        }
    }

    /**
     * Get shipping features for display
     */
    private function getShippingFeatures($courier)
    {
        $features = [];
        $courierName = strtolower($courier['courier_name'] ?? '');
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // Add features based on courier and service type
        $features[] = 'Real-time tracking';

        if (str_contains($serviceCode, 'same_day') || str_contains($serviceCode, 'express')) {
            $features[] = 'Priority handling';
            $features[] = 'SMS notifications';
        }

        if (str_contains($serviceCode, 'premium') || str_contains($courierName, 'dhl') || str_contains($courierName, 'fedex')) {
            $features[] = 'Insurance included';
            $features[] = 'Signature required';
        }

        if (str_contains($courierName, 'gig') || str_contains($courierName, 'kwik')) {
            $features[] = 'Local expertise';
            $features[] = 'Flexible delivery';
        }

        $features[] = 'Customer support';

        return $features;
    }

    /**
     * Validate product availability and stock before checkout
     */
    private function validateProductAvailability()
    {
        $errors = [];

        foreach ($this->cartItems as $productId => $item) {
            // SECURITY FIX: Validate product ID format
            if (!is_numeric($productId) || $productId <= 0) {
                \Log::warning('Invalid product ID in cart', [
                    'product_id' => $productId,
                    'user_id' => Auth::id(),
                    'ip_address' => request()->ip()
                ]);
                $errors[] = "Invalid product in cart. Please refresh your cart.";
                continue;
            }

            $product = \App\Models\Product::find($productId);

            if (!$product) {
                $errors[] = "Product '{$item['name']}' is no longer available";
                continue;
            }

            if (!$product->is_active) {
                $errors[] = "Product '{$product->name}' is currently unavailable";
            }

            // Check stock using the proper stock checking method
            if (!$product->hasSufficientStock($item['quantity'])) {
                $availableStock = $product->getStockLevel();
                $errors[] = "Insufficient stock for '{$product->name}'. Only {$availableStock} available";
            }

            // Check vendor subscription status
            if ($product->vendor) {
                $subscriptionService = app(\App\Services\SubscriptionService::class);
                if (!$subscriptionService->canReceiveOrders($product->vendor)) {
                    $errors[] = "Vendor '{$product->vendor->shop_name}' cannot currently receive orders";
                }
            }

            // Validate product price hasn't changed significantly
            $currentPrice = $product->getCurrentPrice(); // Use the method that handles discount logic
            $cartPrice = (float) $item['price'];
            $priceDifference = abs($currentPrice - $cartPrice);
            $priceChangeThreshold = max($cartPrice * 0.05, 1.00); // 5% threshold or ₦1 minimum

            if ($priceDifference > $priceChangeThreshold) {
                $priceChangePercent = round(($priceDifference / $cartPrice) * 100, 1);
                $errors[] = "Price for '{$product->name}' has changed by {$priceChangePercent}% (from ₦{$cartPrice} to ₦{$currentPrice}). Please refresh your cart";
            }
        }

        // If there are errors, add them to the component and provide user-friendly feedback
        if (!empty($errors)) {
            // Group errors by type for better user experience
            $stockErrors = array_filter($errors, fn($error) => str_contains($error, 'Insufficient stock'));
            $vendorErrors = array_filter($errors, fn($error) => str_contains($error, 'cannot currently receive orders'));
            $priceErrors = array_filter($errors, fn($error) => str_contains($error, 'Price for'));
            $availabilityErrors = array_filter($errors, fn($error) => str_contains($error, 'no longer available') || str_contains($error, 'currently unavailable'));

            // Add specific error messages for different types
            if (!empty($stockErrors)) {
                $this->addError('stock', 'Some items in your cart are out of stock or have limited availability.');
            }
            if (!empty($vendorErrors)) {
                $this->addError('vendor', 'Some vendors in your cart cannot currently process orders.');
            }
            if (!empty($priceErrors)) {
                $this->addError('price', 'Some product prices have changed since you added them to your cart.');
            }
            if (!empty($availabilityErrors)) {
                $this->addError('availability', 'Some products in your cart are no longer available.');
            }

            // Add all individual errors for detailed feedback
            foreach ($errors as $index => $error) {
                $this->addError("product_error_{$index}", $error);
            }

            // Don't throw exception, just return to show errors to user
            return;
        }
    }

    public function render()
    {
        return view('livewire.checkout.index');
    }
}
