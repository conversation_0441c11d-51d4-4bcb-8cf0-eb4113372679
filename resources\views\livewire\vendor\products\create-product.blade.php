<div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-gray-900 to-black text-white px-4 py-6 sm:px-6 lg:px-8 rounded-b-2xl shadow-lg mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div class="space-y-1">
                    <h1 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        Create New Product
                    </h1>
                    <p class="text-gray-300 text-sm sm:text-base">
                        Add a new product to your store inventory
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                    <a href="{{ route('vendor.products.index') }}"
                       class="inline-flex items-center justify-center px-4 sm:px-6 py-2.5 border-2 border-white text-white rounded-lg font-medium transition-all duration-300 hover:bg-white hover:text-gray-900 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 -ml-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Back to Products</span>
                    </a>
                    <button type="submit" form="product-form"
                            wire:loading.attr="disabled"
                            class="inline-flex items-center justify-center px-4 sm:px-6 py-2.5 bg-white text-gray-900 rounded-lg font-medium transition-all duration-300 hover:bg-gray-100 hover:shadow-lg hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100">
                        <div wire:loading.remove wire:target="save">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 -ml-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div wire:loading wire:target="save" class="mr-2 -ml-1">
                            <svg class="animate-spin h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                        <span wire:loading.remove wire:target="save">Save Product</span>
                        <span wire:loading wire:target="save">Saving...</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="save" id="product-form" class="space-y-8">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {{-- Main Content --}}
            <div class="lg:col-span-2 space-y-8">
                {{-- Basic Information Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-info-circle text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Product Information</h3>
                                <p class="text-gray-600 text-sm">Basic details about your product</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-2">
                            <label for="name" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Product Name *</label>
                            <input type="text"
                                   wire:model.live.debounce.500ms="name"
                                   id="name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                   placeholder="Enter product name...">
                            @error('name')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="space-y-2">
                            <label for="description" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Description *</label>
                            <textarea wire:model.live="description"
                                      id="description"
                                      rows="5"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 resize-none"
                                      placeholder="Describe your product in detail..."></textarea>
                            @error('description')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Price (₦) *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-lg">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="price"
                                           id="price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                           placeholder="0.00">
                                </div>
                                @error('price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="discount_price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Sale Price (₦)</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 text-lg">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="discount_price"
                                           id="discount_price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                           placeholder="0.00 (Optional)">
                                </div>
                                @error('discount_price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                                @if($product->discount_price && $product->price)
                                    <p class="text-green-600 text-sm flex items-center">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% discount
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Inventory & Status Section --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-boxes text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Inventory & Status</h3>
                                <p class="text-gray-600 text-sm">Manage stock quantity and product availability</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {{-- Stock Quantity --}}
                            <div class="space-y-2">
                                <label for="quantity" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Stock Quantity *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-boxes text-gray-500"></i>
                                    </div>
                                    <input type="number"
                                           wire:model.live="stock"
                                           id="quantity"
                                           min="0"
                                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                           placeholder="Enter available quantity">
                                </div>
                                @error('stock')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                                <p class="text-gray-500 text-sm">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Number of items available for sale
                                </p>
                            </div>

                            {{-- Product Status --}}
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Product Status</label>
                                <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                                    <input type="checkbox"
                                           wire:model.live="is_active"
                                           id="is_active"
                                           class="h-5 w-5 text-black border-gray-300 rounded focus:ring-black focus:ring-2">
                                    <label for="is_active" class="text-sm font-semibold text-gray-700">
                                        Product is Active and Available for Sale
                                    </label>
                                </div>
                                <p class="text-gray-500 text-sm">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Inactive products won't be visible to customers
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Enhanced Variants Section --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-purple-500 rounded-lg">
                                    <i class="fas fa-palette text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">Product Variants</h3>
                                    <p class="text-gray-600 text-sm">Add different options like colors, sizes, materials, etc.</p>
                                </div>
                            </div>
                            <button type="button"
                                    wire:click="$toggle('showVariantHelp')"
                                    class="p-2 text-purple-500 hover:bg-purple-100 rounded-lg transition-all duration-300">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>

                        {{-- Help Section --}}
                        @if($showVariantHelp)
                            <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                    <div class="text-sm text-blue-800">
                                        <p class="font-medium mb-2">When to use variants:</p>
                                        <ul class="list-disc list-inside space-y-1 text-blue-700">
                                            <li>Your product comes in different colors, sizes, or materials</li>
                                            <li>Different options have different prices</li>
                                            <li>You want customers to choose specific options</li>
                                        </ul>
                                        <p class="mt-2 font-medium">Skip variants if your product has no options or variations.</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="p-6 space-y-6">
                        {{-- Variant Toggle --}}
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           wire:model.live="hasVariants"
                                           wire:click="toggleVariants"
                                           class="h-5 w-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                    <label class="ml-3 text-sm font-medium text-gray-900">
                                        This product has variants (colors, sizes, etc.)
                                    </label>
                                </div>
                            </div>
                            @if($hasVariants)
                                <div class="flex items-center space-x-2">
                                    {{-- Quick Setup Buttons --}}
                                    <div class="relative group">
                                        <button type="button"
                                                class="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                                            Quick Setup
                                        </button>
                                        <div class="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                                            <div class="p-2 space-y-1">
                                                <button type="button"
                                                        wire:click="addQuickVariants('sizes')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                                    <i class="fas fa-tshirt mr-2"></i>Clothing Sizes
                                                </button>
                                                <button type="button"
                                                        wire:click="addQuickVariants('colors')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                                    <i class="fas fa-palette mr-2"></i>Basic Colors
                                                </button>
                                                <button type="button"
                                                        wire:click="addQuickVariants('materials')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                                    <i class="fas fa-layer-group mr-2"></i>Materials
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button"
                                            wire:click="addVariant"
                                            class="group inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-xl font-medium hover:bg-purple-600 transition-all duration-300 hover:scale-105">
                                        <i class="fas fa-plus mr-2 transition-transform duration-300 group-hover:rotate-90"></i>
                                        <span>Add Variant</span>
                                    </button>
                                </div>
                            @endif
                        </div>

                        {{-- Variants List --}}
                        @if($hasVariants)
                            <div class="space-y-4">
                                @forelse ($variants as $index => $variant)
                                    <div class="group p-6 border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-300 relative" wire:key="variant-{{ $index }}">
                                        <div class="absolute top-4 right-4 flex items-center space-x-2">
                                            <button type="button"
                                                    wire:click="duplicateVariant({{ $index }})"
                                                    class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-all duration-300"
                                                    title="Duplicate variant">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button type="button"
                                                    wire:click="removeVariant({{ $index }})"
                                                    class="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-300"
                                                    title="Remove variant">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 pr-16">
                                            <div class="space-y-2">
                                                <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Variant Type</label>
                                                <select wire:model.live="variants.{{ $index }}.name"
                                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300">
                                                    <option value="">Select Type</option>
                                                    @foreach($this->getVariantTypes() as $key => $label)
                                                        <option value="{{ $key }}">{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                                @error("variants.{$index}.name")
                                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Variant Value</label>
                                        @if(isset($variants[$index]['name']) && $variants[$index]['name'] === 'Color')
                                            <select wire:model.live="variants.{{ $index }}.value"
                                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300">
                                                <option value="">Select Color</option>
                                                @foreach($colors as $color)
                                                    <option value="{{ $color->name }}">{{ $color->name }}</option>
                                                @endforeach
                                            </select>
                                        @elseif(isset($variants[$index]['name']) && $variants[$index]['name'] === 'Size')
                                            <select wire:model.live="variants.{{ $index }}.value"
                                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300">
                                                <option value="">Select Size</option>
                                                @foreach($sizes as $size)
                                                    <option value="{{ $size->code }}">{{ $size->name }} ({{ $size->code }})</option>
                                                @endforeach
                                            </select>
                                        @else
                                            <input type="text"
                                                   wire:model.live="variants.{{ $index }}.value"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                                                   placeholder="e.g., Red, Large, Cotton">
                                        @endif
                                        @error("variants.{$index}.value")
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">
                                            Price Adjustment (₦)
                                            <span class="text-xs font-normal text-gray-500 ml-1">(optional)</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500">₦</span>
                                            </div>
                                            <input type="number"
                                                   step="0.01"
                                                   wire:model.live="variants.{{ $index }}.price"
                                                   class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                                                   placeholder="0.00">
                                        </div>
                                        <p class="text-xs text-gray-500">
                                            Add extra cost for this variant. Leave empty to use base price.
                                        </p>
                                        @error("variants.{$index}.price")
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-12">
                                <div class="mx-auto w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-palette text-purple-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No variants yet</h3>
                                <p class="text-gray-500 mb-6">Add variants to give customers different options for this product.</p>
                                <button type="button"
                                        wire:click="addVariant"
                                        class="inline-flex items-center px-6 py-3 bg-purple-500 text-white rounded-xl font-medium hover:bg-purple-600 transition-all duration-300">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Your First Variant
                                </button>
                            </div>
                        @endforelse
                            </div>
                        @else
                            <div class="text-center py-8">
                                <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-check text-gray-400 text-xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Single Product</h3>
                                <p class="text-gray-500">This product has no variants. Customers will purchase the base product.</p>
                            </div>
                        @endif
                    </div>
                </div>

                {{-- Specifications --}}
                <div class="p-6 bg-white rounded-lg shadow">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Specifications</h3>
                        <button type="button" wire:click="addSpecification" class="text-sm font-medium text-gray-600 hover:text-gray-900">+ Add Specification</button>
                    </div>
                    <div class="mt-4 space-y-4">
                        @foreach ($specifications as $index => $specification)
                            <div class="flex items-center gap-4" wire:key="spec-{{ $index }}">
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.name" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Material">
                                </div>
                                <div class="flex-1">
                                    <input type="text" wire:model.defer="specifications.{{ $index }}.value" class="block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="e.g., Cotton">
                                </div>
                                <button type="button" wire:click="removeSpecification({{ $index }})" class="text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            {{-- Sidebar --}}
            <div class="space-y-6">
                {{-- Image Upload --}}
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Product Image</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Main product image</p>
                    </div>
                    <div class="p-6">
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-xl transition-all duration-300 hover:border-blue-400 dark:hover:border-blue-500">
                            <div class="space-y-1 text-center">
                                @if ($image)
                                    <img src="{{ $image->temporaryUrl() }}" class="mx-auto h-32 w-32 object-cover rounded-lg shadow-md">
                                @else
                                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                @endif
                                <div class="flex flex-col sm:flex-row justify-center text-sm text-gray-600 dark:text-gray-400">
                                    <label for="image" class="relative cursor-pointer rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload a file</span>
                                        <input id="image" wire:model="image" type="file" class="sr-only" accept="image/*">
                                    </label>
                                    <p class="mt-1 sm:mt-0 sm:pl-1 text-gray-500 dark:text-gray-400">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 2MB</p>
                                @error('image')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Category & Status --}}
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-gray-700 dark:to-gray-800">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Category & Status</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Product category and visibility</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <select wire:model.defer="category_id" id="category_id"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                                <option value="">Select a category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" class="dark:bg-gray-700">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                        
                        <div class="pt-2 border-t border-gray-100 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Active Status</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Make product visible to customers</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" wire:model.defer="is_active" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Shipping Details --}}
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-teal-50 dark:from-gray-700 dark:to-gray-800">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Shipping Details</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Product dimensions and weight</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Weight (kg)
                                </label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">kg</span>
                                    </div>
                                    <input type="number" step="0.01" wire:model.defer="weight" id="weight"
                                        class="focus:ring-green-500 focus:border-green-500 block w-full pl-12 pr-3 py-2.5 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-xl">
                                </div>
                            </div>
                            <div>
                                <label for="length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Length (cm)
                                </label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">cm</span>
                                    </div>
                                    <input type="number" step="0.01" wire:model.defer="length" id="length"
                                        class="focus:ring-green-500 focus:border-green-500 block w-full pl-12 pr-3 py-2.5 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-xl">
                                </div>
                            </div>
                            <div>
                                <label for="width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Width (cm)
                                </label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">cm</span>
                                    </div>
                                    <input type="number" step="0.01" wire:model.defer="width" id="width"
                                        class="focus:ring-green-500 focus:border-green-500 block w-full pl-12 pr-3 py-2.5 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-xl">
                                </div>
                            </div>
                            <div>
                                <label for="height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Height (cm)
                                </label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">cm</span>
                                    </div>
                                    <input type="number" step="0.01" wire:model.defer="height" id="height"
                                        class="focus:ring-green-500 focus:border-green-500 block w-full pl-12 pr-3 py-2.5 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-xl">
                                </div>
                            </div>
                        </div>
                        <p class="mt-3 text-xs text-gray-500 dark:text-gray-400">
                            Accurate measurements help calculate shipping costs correctly.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>