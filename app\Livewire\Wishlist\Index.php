<?php

namespace App\Livewire\Wishlist;

use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    use WithPagination;

    public function addToWishlist($productId)
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'), navigate: true);
        }

        $user = Auth::user();

        if (!$user->wishlist()->where('product_id', $productId)->exists()) {
            $user->wishlist()->create(['product_id' => $productId]);
            $this->dispatch('toast', message: 'Product added to wishlist.', type: 'success');
            $this->dispatch('wishlistUpdated');
        } else {
            $this->dispatch('toast', message: 'Product is already in your wishlist.', type: 'info');
        }
    }

    public function toggleWishlist($productId)
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'), navigate: true);
        }

        $user = Auth::user();
        $wishlistItem = $user->wishlist()->where('product_id', $productId)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            $this->dispatch('toast', message: 'Product removed from wishlist.', type: 'success');
        } else {
            $user->wishlist()->create(['product_id' => $productId]);
            $this->dispatch('toast', message: 'Product added to wishlist.', type: 'success');
        }
        $this->dispatch('wishlistUpdated');
    }

    public function clearWishlist()
    {
        if (!Auth::check()) {
            return;
        }

        Auth::user()->wishlist()->delete();
        $this->dispatch('toast', message: 'Wishlist cleared.', type: 'success');
        $this->dispatch('wishlistUpdated');
    }

        public function removeFromWishlist($productId)
    {
        if (!Auth::check()) {
            return;
        }

        $wishlistItem = Auth::user()->wishlist()->where('product_id', $productId)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            $this->dispatch('toast', message: 'Product removed from wishlist.', type: 'success');
            $this->dispatch('wishlistUpdated');
        }
    }

    public function render()
    {
        $wishlistItems = collect();
        
        if (Auth::check()) {
            $wishlistItems = Auth::user()->wishlist()
                ->with(['product.vendor', 'product.category'])
                ->latest()
                ->paginate(12);
        }

        return view('livewire.wishlist.index', [
            'wishlistItems' => $wishlistItems,
        ]);
    }
}
