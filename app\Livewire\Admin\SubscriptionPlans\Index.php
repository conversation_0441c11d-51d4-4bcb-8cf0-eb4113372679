<?php

namespace App\Livewire\Admin\SubscriptionPlans;

use App\Models\SubscriptionPlan;
use App\Services\PaystackService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public ?SubscriptionPlan $editing = null;
    public bool $showModal = false;

    // Form fields
    public $name, $description, $price, $interval, $duration_days, $features, $is_active, $product_limit, $commission_rate;

    protected $paystackService;

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    public function boot(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'interval' => 'required|string|in:hourly,daily,weekly,monthly,quarterly,annually',
            'duration_days' => 'required|integer|min:1',
            'features' => 'nullable|string',
            'is_active' => 'boolean',
            'product_limit' => 'nullable|integer|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
        ];
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function edit(SubscriptionPlan $plan)
    {
        $this->editing = $plan;
        $this->name = $plan->name;
        $this->description = $plan->description;
        $this->price = $plan->price;
        $this->interval = $plan->interval;
        $this->duration_days = $plan->duration_days;
        $this->features = is_array($plan->features) ? implode('\n', $plan->features) : $plan->features;
        $this->is_active = $plan->is_active;
        $this->product_limit = $plan->product_limit;
        $this->commission_rate = $plan->commission_rate;
        $this->showModal = true;
    }

    public function save()
    {
        $data = $this->validate();
        $data['is_active'] = $this->is_active ?? false;

        if ($this->editing) {
            if ($this->editing->paystack_plan_code) {
                $paystackPlan = $this->paystackService->updatePlan($this->editing->paystack_plan_code, [
                    'name' => $data['name'],
                    'amount' => $data['price'],
                    'description' => $data['description'],
                ]);

                // LOGIC FIX: Robust API response validation
                if (!$paystackPlan || !is_array($paystackPlan) || !isset($paystackPlan['status']) || !$paystackPlan['status']) {
                    $errorMessage = 'Unknown error.';
                    if (is_array($paystackPlan) && isset($paystackPlan['message'])) {
                        $errorMessage = $paystackPlan['message'];
                    }
                    session()->flash('error', 'Could not update plan on Paystack: ' . $errorMessage);
                    return;
                }
            }
            $this->editing->update($data);
            session()->flash('success', 'Subscription plan updated successfully.');
        } else {
            $paystackPlan = $this->paystackService->createPlan([
                'name' => $data['name'],
                'amount' => $data['price'],
                'interval' => $data['interval'],
                'description' => $data['description'],
            ]);

            // LOGIC FIX: Robust API response validation
            if (!$paystackPlan || !is_array($paystackPlan) || !isset($paystackPlan['status']) || !$paystackPlan['status']) {
                $errorMessage = 'Unknown error.';
                if (is_array($paystackPlan) && isset($paystackPlan['message'])) {
                    $errorMessage = $paystackPlan['message'];
                }
                session()->flash('error', 'Could not create plan on Paystack: ' . $errorMessage);
                return;
            }

            $data['paystack_plan_code'] = $paystackPlan['data']['plan_code'];
            SubscriptionPlan::create($data);
            session()->flash('success', 'Subscription plan created successfully.');
        }

        $this->closeModal();
    }

    public function delete(SubscriptionPlan $plan)
    {
        if ($plan->subscriptions()->where('status', 'active')->exists()) {
            session()->flash('error', 'Cannot delete plan with active subscriptions.');
            return;
        }
        $plan->delete();
        session()->flash('success', 'Subscription plan deleted successfully.');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->editing = null;
        $this->reset(['name', 'description', 'price', 'interval', 'duration_days', 'features', 'is_active', 'product_limit', 'commission_rate']);
    }

    public function render()
    {
        $plans = SubscriptionPlan::orderBy('price')->paginate(10);
        return view('livewire.admin.subscription-plans.index', ['plans' => $plans]);
    }
}
