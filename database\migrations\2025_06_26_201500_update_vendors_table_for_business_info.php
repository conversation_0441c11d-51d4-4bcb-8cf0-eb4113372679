<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('vendors', 'business_name')) {
                $table->string('business_name')->after('slug')->nullable();
            }
            if (!Schema::hasColumn('vendors', 'business_address')) {
                $table->string('business_address')->after('business_name')->nullable();
            }
            if (!Schema::hasColumn('vendors', 'paystack_recipient_code')) {
                $table->string('paystack_recipient_code')->after('business_address')->nullable();
            }
            if (!Schema::hasColumn('vendors', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('is_approved');
            }

            // Drop old columns that are no longer in use
            if (Schema::hasColumn('vendors', 'description')) {
                $table->dropColumn('description');
            }
            if (Schema::hasColumn('vendors', 'address')) {
                $table->dropColumn('address');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Revert the changes made in the up() method
            $table->dropColumn('business_name');
            $table->dropColumn('business_address');
            $table->dropColumn('paystack_recipient_code');
            $table->dropColumn('is_featured');

            $table->text('description')->nullable();
            $table->string('address')->nullable();
        });
    }
};
