<div>
    {{-- Search page with proper navbar spacing handled by layout --}}
    <div class="py-6 sm:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-6 sm:mb-8">
                <div class="relative max-w-2xl mx-auto">
                    <input wire:model.live.debounce.300ms="query"
                           placeholder="Search for products..."
                           class="w-full py-3 px-4 pr-12 text-base rounded-full border border-gray-300
                                  focus:outline-none focus:ring-2 focus:ring-black focus:border-black
                                  transition-all duration-200 min-h-[44px]"
                           type="search" />
                    <div class="absolute top-0 right-0 h-full px-4 flex items-center justify-center min-w-[44px]">
                        <i class="text-gray-500 fas fa-search text-lg"></i>
                    </div>
                </div>
            </div>

            <h3 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
                @if($query)
                    Search results for "<span class="font-bold">{{ $query }}</span>"
                @else
                    All Products
                @endif
            </h3>

            {{-- Loading State --}}
            <div wire:loading class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @for($i = 0; $i < 8; $i++)
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 h-[450px] sm:h-[480px] lg:h-[520px]">
                        <div class="skeleton h-48 sm:h-56 lg:h-72"></div>
                        <div class="p-4 space-y-3">
                            <div class="skeleton-text w-3/4"></div>
                            <div class="skeleton-text w-1/2"></div>
                            <div class="skeleton-text w-1/4"></div>
                        </div>
                    </div>
                @endfor
            </div>

            {{-- Product Grid --}}
            <div wire:loading.remove class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @forelse ($products as $product)
                    <x-products.card :product="$product" />
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-lg text-gray-500">No products found for your search.</p>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $products->links() }}
            </div>
        </div>
    </div>
</div>
