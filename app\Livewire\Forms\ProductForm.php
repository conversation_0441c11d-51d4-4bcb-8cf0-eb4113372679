<?php

namespace App\Livewire\Forms;

use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Livewire\Attributes\Validate;
use Livewire\Form;
use Illuminate\Validation\Rule;

class ProductForm extends Form
{
    public ?Product $product = null;

    // Basic product information
    #[Validate('required|string|max:255')]
    public string $name = '';

    #[Validate('required|string|min:10')]
    public string $description = '';

    #[Validate('required|numeric|min:0|max:999999.99')]
    public float $price = 0;

    #[Validate('nullable|numeric|min:0|lt:price')]
    public ?float $discount_price = null;

    #[Validate('required|integer|min:0|max:999999')]
    public int $stock = 0;

    #[Validate('required|exists:categories,id')]
    public ?int $category_id = null;

    #[Validate('required|exists:vendors,id')]
    public ?int $vendor_id = null;

    #[Validate('nullable|exists:brands,id')]
    public ?int $brand_id = null;

    #[Validate('boolean')]
    public bool $is_active = true;

    #[Validate('boolean')]
    public bool $is_featured = false;

    #[Validate('boolean')]
    public bool $is_best_seller = false;

    // Shipping dimensions
    #[Validate('nullable|numeric|min:0|max:999.99')]
    public ?float $weight = null;

    #[Validate('nullable|numeric|min:0|max:999.99')]
    public ?float $length = null;

    #[Validate('nullable|numeric|min:0|max:999.99')]
    public ?float $width = null;

    #[Validate('nullable|numeric|min:0|max:999.99')]
    public ?float $height = null;

    #[Validate('nullable|string|max:255')]
    public ?string $sku = null;

    /**
     * Set the product for editing
     */
    public function setProduct(Product $product): void
    {
        $this->product = $product;
        
        $this->name = $product->name;
        $this->description = $product->description;
        $this->price = $product->price;
        $this->discount_price = $product->discount_price;
        $this->stock = $product->stock;
        $this->category_id = $product->category_id;
        $this->vendor_id = $product->vendor_id;
        $this->brand_id = $product->brand_id;
        $this->is_active = $product->is_active;
        $this->is_featured = $product->is_featured ?? false;
        $this->is_best_seller = $product->is_best_seller ?? false;
        $this->weight = $product->weight;
        $this->length = $product->length;
        $this->width = $product->width;
        $this->height = $product->height;
        $this->sku = $product->sku;
    }

    /**
     * Store a new product
     */
    public function store(): Product
    {
        $this->validate();

        $product = Product::create($this->only([
            'name', 'description', 'price', 'discount_price', 'stock',
            'category_id', 'vendor_id', 'brand_id', 'is_active', 
            'is_featured', 'is_best_seller', 'weight', 'length', 
            'width', 'height', 'sku'
        ]));

        $this->reset();

        return $product;
    }

    /**
     * Update the existing product
     */
    public function update(): void
    {
        $this->validate();

        $this->product->update($this->only([
            'name', 'description', 'price', 'discount_price', 'stock',
            'category_id', 'vendor_id', 'brand_id', 'is_active', 
            'is_featured', 'is_best_seller', 'weight', 'length', 
            'width', 'height', 'sku'
        ]));
    }

    /**
     * Custom validation rules that require runtime logic
     */
    protected function rules(): array
    {
        $rules = [];

        // Add unique SKU validation if SKU is provided
        if ($this->sku) {
            $rules['sku'] = [
                'string',
                'max:255',
                Rule::unique('products', 'sku')->ignore($this->product?->id)
            ];
        }

        return $rules;
    }

    /**
     * Custom validation messages
     */
    protected function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'name.max' => 'Product name cannot exceed 255 characters.',
            'description.required' => 'Product description is required.',
            'description.min' => 'Product description must be at least 10 characters.',
            'price.required' => 'Product price is required.',
            'price.numeric' => 'Product price must be a valid number.',
            'price.min' => 'Product price cannot be negative.',
            'discount_price.lt' => 'Discount price must be less than the regular price.',
            'stock.required' => 'Stock quantity is required.',
            'stock.integer' => 'Stock quantity must be a whole number.',
            'stock.min' => 'Stock quantity cannot be negative.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'Selected category is invalid.',
            'vendor_id.required' => 'Please select a vendor.',
            'vendor_id.exists' => 'Selected vendor is invalid.',
            'brand_id.exists' => 'Selected brand is invalid.',
        ];
    }

    /**
     * Custom attribute names for validation messages
     */
    protected function validationAttributes(): array
    {
        return [
            'category_id' => 'category',
            'vendor_id' => 'vendor',
            'brand_id' => 'brand',
            'is_active' => 'active status',
            'is_featured' => 'featured status',
            'is_best_seller' => 'best seller status',
        ];
    }
}
