<?php

namespace App\Providers;

use App\Services\ViewedProductsService;
use Illuminate\Support\ServiceProvider;

class ViewedProductsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(ViewedProductsService::class, function ($app) {
            return new ViewedProductsService();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
}
