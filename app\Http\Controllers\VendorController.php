<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VendorController extends Controller
{
    /**
     * Display the vendor storefront
     */
    public function show($slug)
    {
        $vendor = Vendor::where('slug', $slug)
            ->where('is_approved', true)
            ->where('status', 'active')
            ->firstOrFail();
            
        $products = $vendor->products()
            ->where('is_active', true)
            ->paginate(12);
            
        return view('vendor.show', compact('vendor', 'products'));
    }
}
