<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <!-- Header with back button -->
        <div class="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add New Product</h1>
            <a href="{{ route('admin.products.index') }}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Products
            </a>
        </div>

        <form wire:submit.prevent="save" class="space-y-8">
            <!-- Main content grid -->
            <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                <!-- Left column - Product Details -->
                <div class="space-y-6 lg:col-span-2">
                    <!-- Product Details Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Product Details</h3>
                            <div class="mt-6 space-y-4">
                                <!-- Product Name -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Product Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="name" wire:model.defer="name"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="Enter product name" required>
                                    @error('name')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Slug -->
                                <div>
                                    <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Slug <span class="text-red-500">*</span>
                                    </label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <input type="text" id="slug" wire:model.defer="slug"
                                               class="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border-gray-300 focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                               placeholder="product-slug" required>
                                    </div>
                                    @error('slug')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Description -->
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Description
                                    </label>
                                    <textarea id="description" wire:model.defer="description" rows="4"
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                              placeholder="Enter product description"></textarea>
                                    @error('description')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- SKU -->
                                <div>
                                    <label for="sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        SKU (Stock Keeping Unit)
                                    </label>
                                    <input type="text" id="sku" wire:model.defer="sku"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="Enter product SKU">
                                    @error('sku')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                    </div>

                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Stock Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Pricing & Stock</h3>
                            <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-3">
                                <!-- Price -->
                                <div>
                                    <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">$</span>
                                        </div>
                                        <input type="number" id="price" wire:model.defer="price" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00" required>
                                    </div>
                                    @error('price')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Discount Price -->
                                <div>
                                    <label for="discount_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Discount Price
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">$</span>
                                        </div>
                                        <input type="number" id="discount_price" wire:model.defer="discount_price" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                    </div>
                                    @error('discount_price')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Stock -->
                                <div>
                                    <label for="stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Stock Quantity <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" id="stock" wire:model.defer="stock" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0" required>
                                    @error('stock')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                    </div>

                            </div>
                        </div>
                    </div>

                    <!-- Shipping Dimensions Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Shipping Dimensions</h3>
                            <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                                <!-- Weight -->
                                <div>
                                    <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Weight (kg)
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <input type="number" id="weight" wire:model.defer="weight" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">kg</span>
                                        </div>
                                    </div>
                                    @error('weight')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Length -->
                                <div>
                                    <label for="length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Length (cm)
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <input type="number" id="length" wire:model.defer="length" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">cm</span>
                                        </div>
                                    </div>
                                    @error('length')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Width -->
                                <div>
                                    <label for="width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Width (cm)
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <input type="number" id="width" wire:model.defer="width" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">cm</span>
                                        </div>
                                    </div>
                                    @error('width')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Height -->
                                <div>
                                    <label for="height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Height (cm)
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <input type="number" id="height" wire:model.defer="height" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 dark:text-gray-400 sm:text-sm">cm</span>
                                        </div>
                                    </div>
                                    @error('height')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                        </div>
                    </div>

                            </div>
                        </div>
                    </div>

                    <!-- Variants Section -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Product Variants</h3>
                                <button type="button" wire:click="addVariant" class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0">
                                    <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    Add Variant
                                </button>
                            </div>

                            <div class="mt-6 space-y-6">
                                @foreach ($variants as $index => $variant)
                                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-4" wire:key="variant-{{ $index }}">
                                        <!-- Variant Name -->
                                        <div>
                                            <label for="variant-name-{{ $index }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Variant Name <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="variant-name-{{ $index }}" wire:model="variants.{{ $index }}.name"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                                   placeholder="e.g., Color" required>
                                            @error('variants.'.$index.'.name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Variant Value -->
                                        <div>
                                            <label for="variant-value-{{ $index }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Variant Value <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="variant-value-{{ $index }}" wire:model="variants.{{ $index }}.value"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                                   placeholder="e.g., Red" required>
                                            @error('variants.'.$index.'.value')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Variant Price -->
                                        <div>
                                            <label for="variant-price-{{ $index }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Price
                                            </label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 dark:text-gray-400 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="variant-price-{{ $index }}" wire:model="variants.{{ $index }}.price" step="0.01" min="0"
                                                       class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                       placeholder="0.00">
                                            </div>
                                            @error('variants.'.$index.'.price')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Remove Button -->
                                        <div class="flex items-end">
                                            <button type="button" wire:click="removeVariant({{ $index }})" 
                                                    class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                    </div>

                    <!-- Specifications Section -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Product Specifications</h3>
                                <button type="button" wire:click="addSpecification" class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0">
                                    <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    Add Specification
                                </button>
                            </div>

                            <div class="mt-6 space-y-6">
                                @foreach ($specifications as $index => $specification)
                                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-3" wire:key="spec-{{ $index }}">
                                        <!-- Specification Name -->
                                        <div>
                                            <label for="spec-name-{{ $index }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Specification Name <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="spec-name-{{ $index }}" wire:model="specifications.{{ $index }}.name"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                                   placeholder="e.g., Material" required>
                                            @error('specifications.'.$index.'.name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Specification Value -->
                                        <div>
                                            <label for="spec-value-{{ $index }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Specification Value <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="spec-value-{{ $index }}" wire:model="specifications.{{ $index }}.value"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                                   placeholder="e.g., Cotton" required>
                                            @error('specifications.'.$index.'.value')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Remove Button -->
                                        <div class="flex items-end">
                                            <button type="button" wire:click="removeSpecification({{ $index }})" 
                                                    class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                    </div>
                </div>

                <!-- Right column - Organization -->
                <div class="space-y-6">
                    <!-- Organization Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Organization</h3>
                            
                            <div class="mt-6 space-y-4">
                                <!-- Vendor -->
                                <div>
                                    <label for="vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Vendor <span class="text-red-500">*</span>
                                    </label>
                                    <select id="vendor_id" wire:model.defer="vendor_id"
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="">Select Vendor</option>
                                        @foreach($vendors as $vendor)
                                            <option value="{{ $vendor->id }}">{{ $vendor->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('vendor_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <select id="category_id" wire:model.defer="category_id"
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('product.category_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Brand -->
                                <div>
                                    <label for="brand_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Brand
                                    </label>
                                    <select id="brand_id" wire:model.defer="brand_id"
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="">Select Brand</option>
                                        @foreach($brands as $brand)
                                            <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('brand_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Status -->
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Status <span class="text-red-500">*</span>
                                    </label>
                                    <select id="status" wire:model.defer="is_active"
                                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                    @error('product.is_active')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Featured -->
                                <div class="relative flex items-start py-2">
                                    <div class="flex items-center h-5">
                                        <input id="is_featured" wire:model.defer="is_featured" type="checkbox" 
                                               class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="is_featured" class="font-medium text-gray-700 dark:text-gray-300">Featured Product</label>
                                        <p class="text-gray-500 dark:text-gray-400">Show this product in featured section</p>
                                    </div>
                                </div>

                                <!-- In Stock -->
                                <div class="relative flex items-start py-2">
                                    <div class="flex items-center h-5">
                                        <input id="in_stock" wire:model.defer="in_stock" type="checkbox" 
                                               class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="in_stock" class="font-medium text-gray-700 dark:text-gray-300">In Stock</label>
                                        <p class="text-gray-500 dark:text-gray-400">Product is available for purchase</p>
                                    </div>
                                </div>

                                <!-- Is Active -->
                                <div class="relative flex items-start py-2">
                                    <div class="flex items-center h-5">
                                        <input id="is_active" wire:model.defer="is_active" type="checkbox" 
                                               class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="is_active" class="font-medium text-gray-700 dark:text-gray-300">Active</label>
                                        <p class="text-gray-500 dark:text-gray-400">Product is visible on the store</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Media</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Main Image</label>
                                <input type="file" wire:model="image" class="mt-1 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-700">
                                @error('image') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Gallery Images</label>
                                <input type="file" wire:model="gallery" multiple class="mt-1 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-700">
                                @error('gallery.*') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <div class="flex items-center space-x-4 mt-4">
                            <div class="flex items-center">
                                <input type="checkbox" wire:model.defer="is_active" id="is_active_checkbox"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active_checkbox" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">Active</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model.defer="is_featured" id="is_featured"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">Featured</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 pt-5 border-t border-gray-200 dark:border-gray-700">
                <div class="flex flex-col-reverse sm:flex-row sm:justify-end space-y-4 sm:space-y-0 sm:space-x-3">
                    <!-- Cancel Button -->
                    <a href="{{ route('admin.products.index') }}" 
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        Cancel
                    </a>
                    
                    <!-- Save as Draft Button -->
                    <button type="button" 
                            wire:click="saveAsDraft"
                            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 1.414l-4 4z" />
                        </svg>
                        Save as Draft
                    </button>
                    
                    <!-- Create Product Button -->
                    <button type="submit" 
                            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-gray-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Create Product
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
