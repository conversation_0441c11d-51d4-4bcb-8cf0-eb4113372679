<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Product</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">Update product information and settings</p>
                </div>
                <div class="flex space-x-3">
                    <button type="button" wire:click="cancel" 
                            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600">
                        Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form wire:submit="save" class="space-y-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Information Card -->
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- Product Name -->
                            <div>
                                <label for="form.name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Product Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="form.name" 
                                       wire:model.blur="form.name"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                       placeholder="Enter product name"
                                       required>
                                @error('form.name') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="form.description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea id="form.description" 
                                          wire:model.blur="form.description" 
                                          rows="4"
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                          placeholder="Enter product description"
                                          required></textarea>
                                @error('form.description') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>

                            <!-- Price and Discount Price -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Price -->
                                <div>
                                    <label for="form.price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">₦</span>
                                        </div>
                                        <input type="number" 
                                               id="form.price" 
                                               wire:model.blur="form.price" 
                                               step="0.01" 
                                               min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00" 
                                               required>
                                    </div>
                                    @error('form.price') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>

                                <!-- Discount Price -->
                                <div>
                                    <label for="form.discount_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Discount Price
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">₦</span>
                                        </div>
                                        <input type="number" 
                                               id="form.discount_price" 
                                               wire:model.blur="form.discount_price" 
                                               step="0.01" 
                                               min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                    </div>
                                    @error('form.discount_price') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>
                            </div>

                            <!-- Stock -->
                            <div>
                                <label for="form.stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Stock Quantity <span class="text-red-500">*</span>
                                </label>
                                <input type="number" 
                                       id="form.stock" 
                                       wire:model.blur="form.stock" 
                                       min="0"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                       placeholder="0" 
                                       required>
                                @error('form.stock') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Information Card -->
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Shipping Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                                <!-- Weight -->
                                <div>
                                    <label for="form.weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Weight (kg)
                                    </label>
                                    <input type="number" 
                                           id="form.weight" 
                                           wire:model.defer="form.weight" 
                                           step="0.01" 
                                           min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('form.weight') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>

                                <!-- Length -->
                                <div>
                                    <label for="form.length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Length (cm)
                                    </label>
                                    <input type="number" 
                                           id="form.length" 
                                           wire:model.defer="form.length" 
                                           step="0.01" 
                                           min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('form.length') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>

                                <!-- Width -->
                                <div>
                                    <label for="form.width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Width (cm)
                                    </label>
                                    <input type="number" 
                                           id="form.width" 
                                           wire:model.defer="form.width" 
                                           step="0.01" 
                                           min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('form.width') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>

                                <!-- Height -->
                                <div>
                                    <label for="form.height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Height (cm)
                                    </label>
                                    <input type="number" 
                                           id="form.height" 
                                           wire:model.defer="form.height" 
                                           step="0.01" 
                                           min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('form.height') 
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Categories and Vendors Card -->
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Organization</h3>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- Vendor -->
                            <div>
                                <label for="form.vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Vendor <span class="text-red-500">*</span>
                                </label>
                                <select id="form.vendor_id" 
                                        wire:model.blur="form.vendor_id"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500">
                                    <option value="">Select a vendor</option>
                                    @foreach($this->vendors as $vendor)
                                        <option value="{{ $vendor['id'] }}">{{ $vendor['shop_name'] }}</option>
                                    @endforeach
                                </select>
                                @error('form.vendor_id') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>

                            <!-- Category -->
                            <div>
                                <label for="form.category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Category <span class="text-red-500">*</span>
                                </label>
                                <select id="form.category_id" 
                                        wire:model.blur="form.category_id"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500">
                                    <option value="">Select a category</option>
                                    @foreach($this->categories as $category)
                                        <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                                    @endforeach
                                </select>
                                @error('form.category_id') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>

                            <!-- Brand -->
                            <div>
                                <label for="form.brand_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Brand
                                </label>
                                <select id="form.brand_id" 
                                        wire:model.defer="form.brand_id"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500">
                                    <option value="">Select a brand (optional)</option>
                                    @foreach($this->brands as $brand)
                                        <option value="{{ $brand['id'] }}">{{ $brand['name'] }}</option>
                                    @endforeach
                                </select>
                                @error('form.brand_id') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>

                            <!-- SKU -->
                            <div>
                                <label for="form.sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    SKU (Stock Keeping Unit)
                                </label>
                                <input type="text" 
                                       id="form.sku" 
                                       wire:model.defer="form.sku"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                       placeholder="Enter product SKU">
                                @error('form.sku') 
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p> 
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Status Toggles Card -->
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Status</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="form.is_active" 
                                       wire:model.defer="form.is_active"
                                       class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                <label for="form.is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Active
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="form.is_featured" 
                                       wire:model.defer="form.is_featured"
                                       class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                <label for="form.is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Featured
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="form.is_best_seller" 
                                       wire:model.defer="form.is_best_seller"
                                       class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                <label for="form.is_best_seller" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Best Seller
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="p-6">
                            <button type="submit" 
                                    wire:loading.attr="disabled"
                                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed">
                                <span wire:loading.remove wire:target="save">Update Product</span>
                                <span wire:loading wire:target="save" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Updating...
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Overlay -->
    <div wire:loading.flex wire:target="save" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <svg class="animate-spin h-6 w-6 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-900">Updating product...</span>
        </div>
    </div>
</div>

@script
<script>
    // Scroll to top on validation errors
    Livewire.on('scroll-to-top', () => {
        window.scrollTo({top: 0, behavior: 'smooth'});
    });

    // Handle successful product update
    Livewire.on('product-updated', () => {
        // You can add custom success handling here
        console.log('Product updated successfully');
    });

    // Handle failed product update
    Livewire.on('product-update-failed', () => {
        // You can add custom error handling here
        console.log('Product update failed');
    });
</script>
@endscript
