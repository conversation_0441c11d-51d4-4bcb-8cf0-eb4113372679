<?php

namespace App\Livewire\Vendor\Dashboard;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class MapWidget extends Component
{
    public $orderData = [];

    public function mount()
    {
        $this->loadOrderData();
    }

    public function loadOrderData()
    {
        $vendor = Auth::user()->vendor;

        // CRITICAL FIX: Handle multi-vendor orders properly by checking order items
        $ordersByState = Order::query()
            ->whereHas('items.product', function ($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            })
            ->selectRaw('shipping_state, COUNT(*) as order_count')
            ->whereNotNull('shipping_state')
            ->groupBy('shipping_state')
            ->get()
            ->pluck('order_count', 'shipping_state')
            ->toArray();

        $this->orderData = $ordersByState;
    }

    public function render()
    {
        return view('livewire.vendor.dashboard.map-widget');
    }
}