<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Profile Management
                </h1>
                <p class="text-gray-300 text-lg">
                    Manage your business information and account settings
                </p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Success Messages --}}
    @if (session()->has('success'))
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-4 rounded-xl shadow-lg mb-6 flex items-center space-x-3">
            <i class="fas fa-check-circle text-xl"></i>
            <span class="font-medium">{{ session('success') }}</span>
        </div>
    @endif

    <form wire:submit.prevent="save" class="space-y-8">
        <!-- Business Information Section -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                    <i class="fas fa-building text-black"></i>
                    <span>Business Information</span>
                </h2>
                <p class="text-gray-600 mt-2">Update your business details and branding</p>
            </div>
            <div class="p-8 space-y-8">
                <!-- Business Logo Section -->
                <div class="space-y-4">
                    <label for="logo" class="block text-lg font-semibold text-gray-900">Business Logo</label>
                    <div class="flex items-center space-x-6">
                        <div class="relative group">
                            @if ($logo)
                                <img src="{{ $logo->temporaryUrl() }}" alt="New Logo Preview" class="h-24 w-24 rounded-2xl object-cover shadow-lg border-4 border-white ring-2 ring-gray-200">
                                <div class="absolute inset-0 bg-black/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                    <i class="fas fa-camera text-white text-xl"></i>
                                </div>
                            @elseif ($logoUrl)
                                <img src="{{ $logoUrl }}" alt="Current Logo" class="h-24 w-24 rounded-2xl object-cover shadow-lg border-4 border-white ring-2 ring-gray-200">
                                <div class="absolute inset-0 bg-black/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                    <i class="fas fa-camera text-white text-xl"></i>
                                </div>
                            @else
                                <div class="h-24 w-24 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center shadow-lg border-4 border-white ring-2 ring-gray-200">
                                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <input type="file" wire:model="logo" id="logo"
                                   class="block w-full text-sm text-gray-600 file:mr-4 file:py-3 file:px-6 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-800 file:transition-all file:duration-300 file:shadow-lg border-2 border-dashed border-gray-300 rounded-xl p-4 hover:border-black transition-colors duration-300"/>
                            <p class="text-sm text-gray-500 mt-2">Upload a high-quality logo (JPG, PNG, GIF - Max 2MB)</p>
                        </div>
                    </div>
                    @error('logo') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                </div>
                <!-- Business Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="space-y-3">
                        <label for="business_name" class="block text-lg font-semibold text-gray-900">Business Name</label>
                        <input type="text" wire:model.defer="business_name" id="business_name"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        @error('business_name') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                    </div>
                    <div class="space-y-3">
                        <label for="phone" class="block text-lg font-semibold text-gray-900">Business Phone</label>
                        <input type="text" wire:model.defer="phone" id="phone"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        @error('phone') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                    </div>
                </div>

                <!-- Business Address -->
                <div class="space-y-3">
                    <label for="business_address" class="block text-lg font-semibold text-gray-900">Business Address</label>
                    <textarea wire:model.defer="business_address" id="business_address" rows="3"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium resize-none"
                              placeholder="Enter your complete business address..."></textarea>
                    @error('business_address') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                </div>

                <!-- Business Description -->
                <div class="space-y-3">
                    <label for="business_description" class="block text-lg font-semibold text-gray-900">Business Description</label>
                    <textarea wire:model.defer="business_description" id="business_description" rows="4"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium resize-none"
                              placeholder="Describe your business, products, and services..."></textarea>
                    @error('business_description') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                </div>
            </div>
        </div>

        <!-- Personal Information Section -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                    <i class="fas fa-user text-black"></i>
                    <span>Personal Information</span>
                </h2>
                <p class="text-gray-600 mt-2">Update your personal account details</p>
            </div>
            <div class="p-8 space-y-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="space-y-3">
                        <label for="name" class="block text-lg font-semibold text-gray-900">Full Name</label>
                        <input type="text" wire:model.defer="name" id="name"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        @error('name') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                    </div>
                    <div class="space-y-3">
                        <label for="email" class="block text-lg font-semibold text-gray-900">Email Address</label>
                        <input type="email" wire:model.defer="email" id="email"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        @error('email') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Password Update Section -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                    <i class="fas fa-lock text-black"></i>
                    <span>Update Password</span>
                </h2>
                <p class="text-gray-600 mt-2">Change your account password for enhanced security</p>
            </div>
            <div class="p-8 space-y-8">
                <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 flex items-start space-x-3">
                    <i class="fas fa-info-circle text-blue-500 mt-1"></i>
                    <div>
                        <p class="text-blue-800 font-medium">Password Update Notice</p>
                        <p class="text-blue-700 text-sm mt-1">Leave these fields blank if you do not wish to change your password. Your new password must be at least 8 characters long.</p>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="space-y-3">
                        <label for="current_password" class="block text-lg font-semibold text-gray-900">Current Password</label>
                        <input type="password" wire:model.defer="current_password" id="current_password"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        @error('current_password') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-3">
                            <label for="password" class="block text-lg font-semibold text-gray-900">New Password</label>
                            <input type="password" wire:model.defer="password" id="password"
                                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                            @error('password') <span class="text-red-500 text-sm font-medium flex items-center space-x-2 mt-2"><i class="fas fa-exclamation-circle"></i><span>{{ $message }}</span></span> @enderror
                        </div>
                        <div class="space-y-3">
                            <label for="password_confirmation" class="block text-lg font-semibold text-gray-900">Confirm New Password</label>
                            <input type="password" wire:model.defer="password_confirmation" id="password_confirmation"
                                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-black focus:ring-4 focus:ring-black/10 transition-all duration-300 text-gray-900 font-medium">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-end space-x-4 pt-6">
            <a href="{{ route('vendor.dashboard') }}"
               class="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300">
                Cancel
            </a>
            <button type="submit"
                    class="bg-black text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-800 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3"
                    wire:loading.attr="disabled">
                <i class="fas fa-save"></i>
                <span wire:loading.remove>Save Changes</span>
                <span wire:loading class="flex items-center space-x-2">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Saving...</span>
                </span>
            </button>
        </div>
    </form>
</div>
