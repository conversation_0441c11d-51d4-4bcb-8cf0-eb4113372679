<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\PaystackService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class PaystackAmountConversionTest extends TestCase
{
    use RefreshDatabase;

    private PaystackService $paystackService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->paystackService = new PaystackService();
    }

    /** @test */
    public function it_converts_decimal_amounts_to_integer_kobo_correctly()
    {
        // Test various decimal amounts that should be converted to integer kobo
        $testCases = [
            ['amount' => 2323.47, 'expected_kobo' => 232347],
            ['amount' => 100.00, 'expected_kobo' => 10000],
            ['amount' => 50.50, 'expected_kobo' => 5050],
            ['amount' => 999.99, 'expected_kobo' => 99999],
            ['amount' => 1.01, 'expected_kobo' => 101],
            ['amount' => 0.50, 'expected_kobo' => 50],
        ];

        foreach ($testCases as $testCase) {
            $originalAmount = $testCase['amount'];
            $expectedKobo = $testCase['expected_kobo'];
            
            // Test the conversion logic used in PaymentController
            $convertedAmount = (int) round($originalAmount * 100);
            
            $this->assertEquals($expectedKobo, $convertedAmount, 
                "Failed to convert {$originalAmount} to {$expectedKobo} kobo. Got {$convertedAmount} instead.");
            
            // Ensure the result is an integer
            $this->assertIsInt($convertedAmount, 
                "Converted amount should be an integer, got " . gettype($convertedAmount));
        }
    }

    /** @test */
    public function it_handles_edge_cases_in_amount_conversion()
    {
        // Test edge cases that might cause precision issues
        $edgeCases = [
            ['amount' => 0.01, 'expected_kobo' => 1],
            ['amount' => 0.99, 'expected_kobo' => 99],
            ['amount' => 1000000.00, 'expected_kobo' => 100000000],
            ['amount' => 123.456, 'expected_kobo' => 12346], // Should round to nearest kobo
            ['amount' => 123.454, 'expected_kobo' => 12345], // Should round down
        ];

        foreach ($edgeCases as $testCase) {
            $originalAmount = $testCase['amount'];
            $expectedKobo = $testCase['expected_kobo'];
            
            $convertedAmount = (int) round($originalAmount * 100);
            
            $this->assertEquals($expectedKobo, $convertedAmount, 
                "Edge case failed: {$originalAmount} should convert to {$expectedKobo} kobo. Got {$convertedAmount} instead.");
        }
    }

    /** @test */
    public function paystack_service_receives_integer_amounts()
    {
        // Mock the HTTP response from Paystack
        Http::fake([
            'api.paystack.co/transaction/initialize' => Http::response([
                'status' => true,
                'message' => 'Authorization URL created',
                'data' => [
                    'authorization_url' => 'https://checkout.paystack.com/test123',
                    'access_code' => 'test_access_code',
                    'reference' => 'test_reference'
                ]
            ])
        ]);

        // Test data with decimal amount
        $testAmount = 2323.47;
        $expectedKoboAmount = 232347;

        $data = [
            'email' => '<EMAIL>',
            'amount' => (int) round($testAmount * 100), // Apply our fix
            'reference' => 'TEST_REF_' . time(),
            'callback_url' => 'https://example.com/callback',
            'metadata' => [
                'test' => true
            ]
        ];

        // Call the service
        $result = $this->paystackService->initializeTransaction($data);

        // Assert the request was made with integer amount
        Http::assertSent(function ($request) use ($expectedKoboAmount) {
            $body = json_decode($request->body(), true);
            
            // Check that amount is an integer
            $this->assertIsInt($body['amount'], 'Amount sent to Paystack should be an integer');
            
            // Check that amount is correct
            $this->assertEquals($expectedKoboAmount, $body['amount'], 
                'Amount sent to Paystack should be ' . $expectedKoboAmount . ' kobo');
            
            return $request->url() === 'https://api.paystack.co/transaction/initialize';
        });

        // Assert successful response
        $this->assertTrue($result['status']);
        $this->assertArrayHasKey('data', $result);
    }

    /** @test */
    public function it_prevents_float_amounts_from_being_sent_to_paystack()
    {
        // This test ensures that we never accidentally send float amounts to Paystack
        $decimalAmount = 2323.47;
        
        // Without our fix, this would be a float
        $incorrectAmount = $decimalAmount * 100; // This is 232347.0 (float)
        $this->assertIsFloat($incorrectAmount, 'Without conversion, amount would be a float');
        
        // With our fix, this should be an integer
        $correctAmount = (int) round($decimalAmount * 100); // This is 232347 (int)
        $this->assertIsInt($correctAmount, 'With conversion, amount should be an integer');
        
        // They should have the same numeric value
        $this->assertEquals($incorrectAmount, $correctAmount, 'Numeric values should be equal');
        
        // But different types
        $this->assertNotSame($incorrectAmount, $correctAmount, 'Types should be different (float vs int)');
    }
}
