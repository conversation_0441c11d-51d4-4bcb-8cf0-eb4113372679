{{-- Enhanced Livewire Product Card with Better Spacing and Visual Feedback --}}

<div class="group relative bg-white rounded-2xl shadow-md overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-[420px] sm:h-[450px] lg:h-[480px] flex flex-col hover:border-gray-200">
    <!-- Product Image Container -->
    <div class="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
        <a href="{{ route('products.show', $product->slug) }}" wire:navigate class="block relative group/image">
            <x-image.responsive
                :src="$product->image_url"
                :alt="$product->name"
                width="400"
                height="300"
                aspect-ratio="4/3"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                class="w-full h-44 sm:h-48 lg:h-56 transform group-hover:scale-110 transition-transform duration-700 ease-out"
                placeholder="images/product-placeholder.svg"
            />

            <!-- Image Overlay on Hover -->
            <div class="absolute inset-0 bg-black/20 opacity-0 group-hover/image:opacity-100 transition-opacity duration-300"></div>
        </a>

        <!-- Sale Badge -->
        @if($product->isOnSale() && $product->discount_price)
            <div class="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-600 text-white px-3 py-1.5 rounded-full text-xs font-bold shadow-lg animate-pulse z-10">
                SALE
            </div>
        @endif

        <!-- Stock Badge -->
        @if($product->stock <= 5 && $product->stock > 0)
            <div class="absolute top-3 left-3 {{ $product->isOnSale() ? 'top-12' : '' }} bg-gradient-to-r from-orange-500 to-yellow-600 text-white px-3 py-1.5 rounded-full text-xs font-bold shadow-lg z-10">
                Only {{ $product->stock }} left
            </div>
        @endif

        <!-- Wishlist Button -->
        @auth
            <button wire:click="toggleWishlist"
                    wire:loading.attr="disabled"
                    wire:target="toggleWishlist"
                    class="absolute top-3 right-3 p-2.5 bg-white/95 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group/wishlist z-10 {{ $loadingWishlist ? 'opacity-50 cursor-not-allowed' : '' }} min-w-[44px] min-h-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    title="{{ $inWishlist ? 'Remove from wishlist' : 'Add to wishlist' }}"
                    aria-label="{{ $inWishlist ? 'Remove ' . $product->name . ' from wishlist' : 'Add ' . $product->name . ' to wishlist' }}"
                    aria-pressed="{{ $inWishlist ? 'true' : 'false' }}">

                <span wire:loading.remove wire:target="toggleWishlist">
                    @if($inWishlist)
                        <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                    @else
                        <svg class="w-5 h-5 text-gray-400 group-hover/wishlist:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    @endif
                </span>

                <span wire:loading wire:target="toggleWishlist" class="flex items-center justify-center">
                    <svg class="animate-spin w-5 h-5 text-red-500" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </span>
            </button>
        @endauth
    </div>

    <!-- Product Content -->
    <div class="flex-1 p-4 sm:p-5 flex flex-col">
        <!-- Brand -->
        @if($product->brand && $product->brand->name)
            <p class="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wide">{{ $product->brand->name }}</p>
        @endif

        <!-- Product Name -->
        <h3 class="text-sm sm:text-base font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-black transition-colors duration-300 leading-tight">
            <a href="{{ route('products.show', $product->slug) }}" wire:navigate class="hover:underline">
                {{ $product->name }}
            </a>
        </h3>

        <!-- Vendor -->
        @if($product->vendor)
            <p class="text-xs text-gray-600 mb-3">
                by <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="font-medium text-gray-800 hover:text-black hover:underline transition-colors">{{ $product->vendor->shop_name ?? $product->vendor->name ?? 'Unknown Vendor' }}</a>
            </p>
        @endif

        <!-- Price -->
        <div class="mb-4" id="product-{{ $product->id }}-price">
            @if($product->isOnSale() && $product->discount_price)
                <div class="flex items-baseline space-x-2">
                    <span class="text-lg sm:text-xl font-bold text-black" aria-label="Sale price">@currency($product->discount_price)</span>
                    <span class="text-sm text-gray-500 line-through" aria-label="Original price">@currency($product->price)</span>
                </div>
                <div class="text-xs text-green-600 font-medium mt-1" aria-label="Savings amount">
                    Save @currency($product->price - $product->discount_price)
                </div>
            @else
                <span class="text-lg sm:text-xl font-bold text-black" aria-label="Price">@currency($product->price)</span>
            @endif
        </div>

        <!-- Action Buttons -->
        <div class="mt-auto flex flex-col gap-3">
            <!-- Add to Cart Button -->
            @if($product->is_active && $product->stock > 0)
                <button wire:click="addToCart"
                        wire:loading.attr="disabled"
                        wire:target="addToCart"
                        class="w-full bg-gradient-to-r from-gray-900 to-black text-white px-4 py-2.5 rounded-xl font-semibold hover:from-black hover:to-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl group/cart text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 {{ $loadingCart ? 'opacity-50 cursor-not-allowed' : '' }} min-h-[44px]"
                        aria-label="Add {{ $product->name }} to cart"
                        aria-describedby="product-{{ $product->id }}-price">

                    <span wire:loading.remove wire:target="addToCart" class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4 group-hover/cart:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        <span>Add to Cart</span>
                    </span>

                    <span wire:loading wire:target="addToCart" class="flex items-center justify-center space-x-2">
                        <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Adding...</span>
                    </span>
                </button>
            @else
                <button disabled class="w-full bg-gray-300 text-gray-500 px-4 py-2.5 rounded-xl font-semibold cursor-not-allowed text-sm">
                    @if(!$product->is_active)
                        Unavailable
                    @else
                        Out of Stock
                    @endif
                </button>
            @endif

            <!-- Quick View Button -->
            <a href="{{ route('products.show', $product->slug) }}"
               wire:navigate
               class="w-full p-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-300 transform hover:scale-105 group/view flex items-center justify-center text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
               title="View Details">
                <svg class="w-4 h-4 mr-2 group-hover/view:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Details
            </a>
        </div>
    </div>
</div>
