<?php
declare(strict_types=1);

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Attributes\On;
use Livewire\Component;

class QuickView extends Component
{
    public ?Product $product = null;
    public bool $showModal = false;
    public int $quantity = 1;

    #[On('show-quick-view')]
    public function show(int $productId): void
    {
        $this->product = Product::with(['variants.color', 'variants.size'])->find($productId);
        $this->showModal = true;
    }

    public function close(): void
    {
        $this->reset('product', 'showModal', 'quantity');
    }

    public function render()
    {
        return view('livewire.product.quick-view');
    }
}
