<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sizes', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., Small, Medium, Large
            $table->string('code')->unique()->nullable(); // e.g., S, M, L
            $table->integer('sort_order')->default(0)->comment('Order for displaying sizes.');
            $table->boolean('is_active')->default(true)->comment('Whether this size is active and available for selection.');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sizes');
    }
};
