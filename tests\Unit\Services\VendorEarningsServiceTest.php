<?php

namespace Tests\Unit\Services;

use App\Services\VendorEarningsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class VendorEarningsServiceTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_calculates_commission_based_on_the_config_value(): void
    {
        // Arrange: Set a custom commission rate for this test.
        config(['brandify.commission_rate' => 0.25]); // 25%
        $service = new VendorEarningsService();
        $orderAmount = 1000.00;

        // Act: Calculate the commission.
        $commission = $service->calculateCommission($orderAmount);

        // Assert: Check if the commission is 25% of the order amount.
        $this->assertEquals(250.00, $commission);
    }

    #[Test]
    public function it_uses_the_default_value_if_config_is_not_set(): void
    {
        // Arrange: Ensure the config key is not set.
        config(['brandify.commission_rate' => null]);
        $service = new VendorEarningsService();
        $orderAmount = 1000.00;

        // Act: Calculate the commission.
        $commission = $service->calculateCommission($orderAmount);

        // Assert: Check if it falls back to the default 2.7%.
        $this->assertEquals(27.00, $commission);
    }
}
