<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Tests\TestCase;

class VendorImageUploadTest extends TestCase
{
    use RefreshDatabase;

    protected $vendor;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles first
        $vendorRole = \App\Models\Role::firstOrCreate(['name' => 'vendor']);

        // Create test user and vendor
        $this->user = User::factory()->create(['role_id' => $vendorRole->id]);
        $this->vendor = Vendor::factory()->create(['user_id' => $this->user->id]);

        // Fake storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function vendor_can_upload_logo_in_profile()
    {
        $this->actingAs($this->user);
        
        $file = UploadedFile::fake()->image('logo.jpg', 300, 300);

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('business_name', 'Test Business')
            ->set('business_address', 'Test Address')
            ->set('business_description', 'Test Description')
            ->set('phone', '1234567890')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('logo', $file)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSessionHas('success');

        // Verify logo was uploaded to media library
        $this->vendor->refresh();
        $this->assertNotNull($this->vendor->getFirstMedia('logo'));
        $this->assertEquals('Test Business Logo', $this->vendor->getFirstMedia('logo')->name);
    }

    /** @test */
    public function vendor_can_upload_logo_in_settings()
    {
        $this->actingAs($this->user);
        
        $file = UploadedFile::fake()->image('logo.png', 200, 200);

        Livewire::test(\App\Livewire\Vendor\Settings\Index::class)
            ->set('shop_name', 'Updated Shop')
            ->set('business_name', 'Updated Business')
            ->set('business_address', 'Updated Address')
            ->set('city', 'Updated City')
            ->set('state', 'Updated State')
            ->set('country', 'Updated Country')
            ->set('phone', '9876543210')
            ->set('logo', $file)
            ->call('updateSettings')
            ->assertHasNoErrors()
            ->assertSessionHas('success');

        // Verify logo was uploaded to media library
        $this->vendor->refresh();
        $this->assertNotNull($this->vendor->getFirstMedia('logo'));
    }

    /** @test */
    public function vendor_can_upload_product_image()
    {
        $this->actingAs($this->user);
        
        $category = Category::factory()->create();
        $file = UploadedFile::fake()->image('product.jpg', 800, 600);

        Livewire::test(\App\Livewire\Vendor\Products\ProductForm::class)
            ->set('product.name', 'Test Product')
            ->set('product.category_id', $category->id)
            ->set('product.description', 'Test Description')
            ->set('product.price', 99.99)
            ->set('image', $file)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSessionHas('success');

        // Verify product was created and image was uploaded
        $product = Product::where('name', 'Test Product')->first();
        $this->assertNotNull($product);
        $this->assertNotNull($product->getFirstMedia('product_images'));
        $this->assertEquals('Test Product', $product->getFirstMedia('product_images')->name);
    }

    /** @test */
    public function image_upload_validates_file_type()
    {
        $this->actingAs($this->user);
        
        $file = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('logo', $file)
            ->call('save')
            ->assertHasErrors(['logo']);
    }

    /** @test */
    public function image_upload_validates_file_size()
    {
        $this->actingAs($this->user);
        
        // Create a file larger than 2MB (2048KB)
        $file = UploadedFile::fake()->image('large.jpg')->size(3000);

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('logo', $file)
            ->call('save')
            ->assertHasErrors(['logo']);
    }

    /** @test */
    public function vendor_can_replace_existing_logo()
    {
        $this->actingAs($this->user);
        
        // Upload initial logo
        $initialFile = UploadedFile::fake()->image('initial.jpg');
        $this->vendor->addMedia($initialFile->getRealPath())
            ->usingName('Initial Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $this->vendor->getMedia('logo'));

        // Upload new logo
        $newFile = UploadedFile::fake()->image('new.jpg');

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('business_name', 'Test Business')
            ->set('business_address', 'Test Address')
            ->set('business_description', 'Test Description')
            ->set('phone', '1234567890')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('logo', $newFile)
            ->call('save')
            ->assertHasNoErrors();

        // Verify old logo was replaced
        $this->vendor->refresh();
        $this->assertCount(1, $this->vendor->getMedia('logo'));
        $this->assertEquals('Test Business Logo', $this->vendor->getFirstMedia('logo')->name);
    }

    /** @test */
    public function product_image_generates_thumbnails()
    {
        $this->actingAs($this->user);
        
        $category = Category::factory()->create();
        $file = UploadedFile::fake()->image('product.jpg', 1200, 800);

        Livewire::test(\App\Livewire\Vendor\Products\ProductForm::class)
            ->set('product.name', 'Test Product')
            ->set('product.category_id', $category->id)
            ->set('product.description', 'Test Description')
            ->set('product.price', 99.99)
            ->set('image', $file)
            ->call('save')
            ->assertHasNoErrors();

        $product = Product::where('name', 'Test Product')->first();
        $media = $product->getFirstMedia('product_images');
        
        $this->assertNotNull($media);
        
        // Check if thumbnail conversion exists
        $this->assertTrue($media->hasGeneratedConversion('thumb'));
        $this->assertTrue($media->hasGeneratedConversion('large'));
    }

    /** @test */
    public function vendor_logo_generates_thumbnail()
    {
        $this->actingAs($this->user);
        
        $file = UploadedFile::fake()->image('logo.jpg', 400, 400);

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('business_name', 'Test Business')
            ->set('business_address', 'Test Address')
            ->set('business_description', 'Test Description')
            ->set('phone', '1234567890')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('logo', $file)
            ->call('save')
            ->assertHasNoErrors();

        $this->vendor->refresh();
        $media = $this->vendor->getFirstMedia('logo');
        
        $this->assertNotNull($media);
        
        // Check if thumbnail conversion exists
        $this->assertTrue($media->hasGeneratedConversion('thumb'));
    }

    /** @test */
    public function image_urls_are_accessible()
    {
        $this->actingAs($this->user);
        
        $file = UploadedFile::fake()->image('logo.jpg');
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $logoUrl = $this->vendor->logo_url;
        
        $this->assertNotEmpty($logoUrl);
        $this->assertStringContainsString('storage', $logoUrl);
    }

    /** @test */
    public function handles_upload_errors_gracefully()
    {
        $this->actingAs($this->user);
        
        // Mock a file that will cause an error
        $file = UploadedFile::fake()->create('invalid.txt', 0);

        Livewire::test(\App\Livewire\Vendor\Profile\Index::class)
            ->set('logo', $file)
            ->call('save')
            ->assertHasErrors(['logo']);
    }
}
