<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\Order;
use App\Models\VendorTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class VendorEarningsService
{

    public function creditSale(Vendor $vendor, Order $order, float $amount, string $description)
    {
        // SECURITY FIX: Add authorization check (skip for system operations)
        if (auth()->check() && !Gate::allows('manage-vendor-finances', $vendor)) {
            throw new \Exception('Unauthorized to modify vendor finances.');
        }

        return $this->createTransaction($vendor, 'sale', $amount, $description, $order);
    }

    public function debitCommission(Vendor $vendor, Order $order, float $saleAmount, string $description)
    {
        // SECURITY FIX: Add authorization check (skip for system operations)
        if (auth()->check() && !Gate::allows('manage-vendor-finances', $vendor)) {
            throw new \Exception('Unauthorized to modify vendor finances.');
        }

        $commissionAmount = $this->calculateCommission($saleAmount);

        return $this->createTransaction($vendor, 'commission', -$commissionAmount, $description, $order);
    }

    public function processWithdrawal(Vendor $vendor, float $amount, string $description, $withdrawal)
    {
        // SECURITY FIX: Add authorization check
        if (!Gate::allows('manage-vendor-finances', $vendor)) {
            throw new \Exception('Unauthorized to process vendor withdrawal.');
        }

        if ($vendor->balance < $amount) {
            throw new \Exception('Insufficient funds for withdrawal.');
        }

        return $this->createTransaction($vendor, 'withdrawal', -$amount, $description, $withdrawal);
    }

    /**
     * CRITICAL FIX F1: Process order earnings for vendor
     * This method was missing and causing payment processing failures
     */
    public function processOrderEarnings(Order $order)
    {
        \Log::info('VendorEarningsService Process Order Earnings Started', [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'vendor_id' => $order->vendor_id,
            'total_amount' => $order->total_amount,
            'status' => $order->status,
            'payment_status' => $order->payment_status,
            'timestamp' => now()->toISOString()
        ]);

        // Skip authorization check for system operations during payment processing
        if (!$order->vendor) {
            \Log::error('Order has no vendor - Cannot process earnings', [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            throw new \Exception('Order must have an associated vendor.');
        }

        $vendor = $order->vendor;

        return DB::transaction(function () use ($vendor, $order) {
            $vendor->lockForUpdate();

            // BUSINESS LOGIC FIX: Idempotency check to prevent double-calculation
            $existingTransactions = VendorTransaction::where('order_id', $order->id)
                ->where('vendor_id', $vendor->id)
                ->whereIn('type', ['sale', 'commission'])
                ->exists();

            if ($existingTransactions) {
                \Log::warning('Attempted duplicate commission calculation prevented', [
                    'order_id' => $order->id,
                    'vendor_id' => $vendor->id,
                    'order_number' => $order->order_number
                ]);

                return [
                    'status' => 'already_processed',
                    'message' => 'Order earnings already processed',
                    'order_id' => $order->id
                ];
            }

            // Calculate vendor earnings (order total minus commission)
            $orderTotal = $order->total;
            $commissionAmount = $this->calculateCommission($orderTotal);
            $vendorEarnings = $orderTotal - $commissionAmount;

            // Credit the sale to vendor (this goes to pending balance due to hold period)
            $saleTransaction = $this->creditSale(
                $vendor,
                $order,
                $vendorEarnings,
                "Sale earnings for order #{$order->order_number}"
            );

            // Create commission transaction (this is a debit)
            $commissionTransaction = $this->debitCommission(
                $vendor,
                $order,
                $orderTotal,
                "Commission for order #{$order->order_number}"
            );

            // AUDIT TRAIL: Log order earnings processing
            \Log::info('Order earnings processed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'vendor_id' => $vendor->id,
                'order_total' => $orderTotal,
                'commission_amount' => $commissionAmount,
                'vendor_earnings' => $vendorEarnings,
                'sale_transaction_id' => $saleTransaction->id,
                'commission_transaction_id' => $commissionTransaction->id,
            ]);

            return [
                'sale_transaction' => $saleTransaction,
                'commission_transaction' => $commissionTransaction,
                'vendor_earnings' => $vendorEarnings,
                'commission_amount' => $commissionAmount,
            ];
        });
    }

    protected function createTransaction(Vendor $vendor, string $type, float $amount, string $description, $reference)
    {
        return DB::transaction(function () use ($vendor, $type, $amount, $description, $reference) {
            $vendor->lockForUpdate();

            // FINANCIAL LOGIC FIX: Update all financial fields properly
            $newBalance = $vendor->balance + $amount;

            // Update financial state based on transaction type
            $updates = ['balance' => $newBalance, 'last_balance_update' => now()];

            if ($type === 'sale') {
                // For sales, add to total earnings and pending balance (7-14 day hold)
                $updates['total_earnings'] = $vendor->total_earnings + $amount;
                $updates['pending_balance'] = $vendor->pending_balance + $amount;
            } elseif ($type === 'withdrawal') {
                // For withdrawals, update withdrawn amount and reduce available balance
                $updates['withdrawn_amount'] = $vendor->withdrawn_amount + abs($amount);
                $updates['available_balance'] = $vendor->available_balance - abs($amount);
            } elseif ($type === 'commission') {
                // CRITICAL FIX F3: Handle commission transactions properly
                // Commission is a debit (negative amount) that reduces pending balance
                // Note: amount is already negative when passed to this method
                $updates['pending_balance'] = $vendor->pending_balance + $amount; // amount is negative, so this reduces pending balance
            }

            $transaction = $vendor->transactions()->create([
                'type' => $type,
                'amount' => $amount,
                'balance_after' => $newBalance,
                'description' => $description,
                'reference_type' => get_class($reference),
                'reference_id' => $reference->id,
            ]);

            $vendor->update($updates);

            // AUDIT TRAIL: Log financial transaction for compliance
            \Log::info('Vendor financial transaction processed', [
                'vendor_id' => $vendor->id,
                'transaction_id' => $transaction->id,
                'type' => $type,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'reference_type' => get_class($reference),
                'reference_id' => $reference->id,
            ]);

            return $transaction;
        });
    }

    public function calculateCommission(float $amount, ?float $rate = null): float
    {
        $commissionRate = $rate ?? config('brandify.commission_rate') ?? 0.027;

        return round($amount * $commissionRate, 2);
    }

    public function creditVendor(Vendor $vendor, float $amount, string $description, $reference = null): VendorTransaction
    {
        // SECURITY FIX: Add authorization check (skip for system operations)
        if (auth()->check() && !Gate::allows('manage-vendor-finances', $vendor)) {
            throw new \Exception('Unauthorized to credit vendor account.');
        }

        // Use the centralized transaction method for consistency
        return $this->createTransaction($vendor, 'credit', $amount, $description, $reference);
    }

    /**
     * FINANCIAL LOGIC FIX: Move pending balance to available balance after hold period
     * This should be called by a scheduled job daily
     */
    public function processPendingBalances()
    {
        $holdPeriodDays = config('brandify.earnings_hold_period', 7); // Default 7 days

        $vendors = Vendor::where('pending_balance', '>', 0)->get();

        foreach ($vendors as $vendor) {
            // Find transactions older than hold period that contributed to pending balance
            $eligibleTransactions = $vendor->transactions()
                ->where('type', 'sale')
                ->where('created_at', '<=', now()->subDays($holdPeriodDays))
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                          ->from('vendor_transactions as vt2')
                          ->whereColumn('vt2.reference_id', 'vendor_transactions.reference_id')
                          ->where('vt2.type', 'balance_release');
                })
                ->get();

            $amountToRelease = $eligibleTransactions->sum('amount');

            if ($amountToRelease > 0) {
                DB::transaction(function () use ($vendor, $amountToRelease) {
                    $vendor->lockForUpdate();

                    $vendor->update([
                        'pending_balance' => max(0, $vendor->pending_balance - $amountToRelease),
                        'available_balance' => $vendor->available_balance + $amountToRelease,
                        'last_balance_update' => now(),
                    ]);

                    // Create audit transaction
                    $vendor->transactions()->create([
                        'type' => 'balance_release',
                        'amount' => $amountToRelease,
                        'balance_after' => $vendor->balance,
                        'description' => "Released {$amountToRelease} from pending to available balance",
                        'reference_type' => 'App\Models\Vendor',
                        'reference_id' => $vendor->id,
                    ]);
                });

                \Log::info('Pending balance released to available', [
                    'vendor_id' => $vendor->id,
                    'amount_released' => $amountToRelease,
                    'new_pending_balance' => $vendor->pending_balance,
                    'new_available_balance' => $vendor->available_balance,
                ]);
            }
        }
    }

    /**
     * FINANCIAL INTEGRITY: Reconcile vendor balances to ensure accuracy
     */
    public function reconcileVendorBalance(Vendor $vendor): array
    {
        $transactions = $vendor->transactions()->orderBy('created_at')->get();

        $calculatedBalance = 0;
        $calculatedTotalEarnings = 0;
        $calculatedWithdrawn = 0;

        foreach ($transactions as $transaction) {
            $calculatedBalance += $transaction->amount;

            if ($transaction->type === 'sale') {
                $calculatedTotalEarnings += $transaction->amount;
            } elseif ($transaction->type === 'withdrawal') {
                $calculatedWithdrawn += abs($transaction->amount);
            }
        }

        $discrepancies = [];

        if (abs($vendor->balance - $calculatedBalance) > 0.01) {
            $discrepancies['balance'] = [
                'stored' => $vendor->balance,
                'calculated' => $calculatedBalance,
                'difference' => $vendor->balance - $calculatedBalance,
            ];
        }

        if (abs($vendor->total_earnings - $calculatedTotalEarnings) > 0.01) {
            $discrepancies['total_earnings'] = [
                'stored' => $vendor->total_earnings,
                'calculated' => $calculatedTotalEarnings,
                'difference' => $vendor->total_earnings - $calculatedTotalEarnings,
            ];
        }

        if (abs($vendor->withdrawn_amount - $calculatedWithdrawn) > 0.01) {
            $discrepancies['withdrawn_amount'] = [
                'stored' => $vendor->withdrawn_amount,
                'calculated' => $calculatedWithdrawn,
                'difference' => $vendor->withdrawn_amount - $calculatedWithdrawn,
            ];
        }

        return [
            'vendor_id' => $vendor->id,
            'discrepancies' => $discrepancies,
            'is_balanced' => empty($discrepancies),
            'checked_at' => now(),
        ];
    }
}
