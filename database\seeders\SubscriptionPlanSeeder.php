<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        SubscriptionPlan::updateOrCreate(
            ['name' => 'Free Plan'],
            [
                'description' => 'Free trial plan for new vendors with limited features',
                'price' => 0,
                'interval' => 'monthly',
                'duration_days' => 30,
                'product_limit' => 10, // Limited to 10 products
                'commission_rate' => 2.7, // Same commission rate
                'features' => json_encode([
                    '10 product listings',
                    'Basic order management',
                    'Basic support',
                    'Limited analytics'
                ]),
                'is_active' => true,
            ]
        );

        SubscriptionPlan::updateOrCreate(
            ['name' => 'Monthly Plan'],
            [
                'description' => 'Monthly subscription for vendors with full access to platform features',
                'price' => 9000.00, // ₦9,000 (stored as decimal for display, converted to kobo for Paystack)
                'interval' => 'monthly',
                'duration_days' => 30,
                'product_limit' => null, // Unlimited
                'commission_rate' => 2.7, // 2.7% commission
                'features' => json_encode([
                    'Unlimited product listings',
                    'Order management',
                    'Earnings tracking',
                    'Customer support',
                    'Analytics dashboard',
                    'Shipping integration',
                    'Payment processing'
                ]),
                'paystack_plan_code' => null, // Will be set when created via Paystack
                'is_active' => true,
            ]
        );

        SubscriptionPlan::updateOrCreate(
            ['name' => 'Bi-Annual Plan'],
            [
                'description' => 'Six-month subscription for vendors with discounted pricing',
                'price' => 8700.00, // ₦8,700 per month (3% discount)
                'interval' => 'bi-annually',
                'duration_days' => 180,
                'product_limit' => null, // Unlimited
                'commission_rate' => 2.7, // 2.7% commission
                'features' => json_encode([
                    'Unlimited product listings',
                    'Order management',
                    'Earnings tracking',
                    'Customer support',
                    'Analytics dashboard',
                    'Shipping integration',
                    'Payment processing',
                    '3% discount vs monthly plan',
                    'Billed every 6 months'
                ]),
                'paystack_plan_code' => null,
                'is_active' => true,
            ]
        );

        SubscriptionPlan::updateOrCreate(
            ['name' => 'Annual Plan'],
            [
                'description' => 'Yearly subscription for vendors with maximum savings',
                'price' => 8500.00, // ₦8,500 per month (5.5% discount)
                'interval' => 'annually',
                'duration_days' => 365,
                'product_limit' => null, // Unlimited
                'commission_rate' => 2.7, // 2.7% commission
                'features' => json_encode([
                    'Unlimited product listings',
                    'Order management',
                    'Earnings tracking',
                    'Customer support',
                    'Analytics dashboard',
                    'Shipping integration',
                    'Payment processing',
                    '5.5% discount vs monthly plan',
                    'Priority support',
                    'Free setup assistance',
                    'Billed annually'
                ]),
                'paystack_plan_code' => null,
                'is_active' => true,
            ]
        );
    }
}
