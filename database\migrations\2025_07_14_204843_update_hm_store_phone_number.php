<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Vendor;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update H&M Store phone number to correct format for ShipBubble API
        $vendor = Vendor::where('business_name', 'H&M Store Inc.')->first();
        if ($vendor) {
            $vendor->phone = '***********';
            $vendor->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert H&M Store phone number
        $vendor = Vendor::where('business_name', 'H&M Store Inc.')->first();
        if ($vendor) {
            $vendor->phone = '+2348234567890';
            $vendor->save();
        }
    }
};
