<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * PERFORMANCE FIX: Add strategic database indexes for improved query performance
     */
    public function up(): void
    {
        // Products table indexes for better search and filtering performance
        Schema::table('products', function (Blueprint $table) {
            // Index for vendor queries (most common filter)
            $table->index('vendor_id', 'idx_products_vendor_id');
            
            // Index for category filtering
            $table->index('category_id', 'idx_products_category_id');
            
            // Index for brand filtering
            $table->index('brand_id', 'idx_products_brand_id');
            
            // Index for active products (frequently filtered)
            $table->index('is_active', 'idx_products_is_active');
            
            // Composite index for active products by vendor (very common query)
            $table->index(['vendor_id', 'is_active'], 'idx_products_vendor_active');
            
            // Index for price range queries
            $table->index('price', 'idx_products_price');
            
            // Index for stock level queries
            $table->index('stock', 'idx_products_stock');
            
            // Index for created_at (for sorting by latest)
            $table->index('created_at', 'idx_products_created_at');
            
            // Composite index for search optimization
            $table->index(['is_active', 'created_at'], 'idx_products_active_created');
        });

        // Orders table indexes for admin and vendor dashboards
        Schema::table('orders', function (Blueprint $table) {
            // Index for user orders
            $table->index('user_id', 'idx_orders_user_id');
            
            // Index for order status filtering
            $table->index('status', 'idx_orders_status');
            
            // Index for payment status filtering
            $table->index('payment_status', 'idx_orders_payment_status');
            
            // Index for created_at (for sorting and date filtering)
            $table->index('created_at', 'idx_orders_created_at');
            
            // Composite index for paid orders by date (for revenue calculations)
            $table->index(['payment_status', 'created_at'], 'idx_orders_payment_date');
            
            // Composite index for user orders by status
            $table->index(['user_id', 'status'], 'idx_orders_user_status');
            
            // Index for order number searches
            $table->index('order_number', 'idx_orders_order_number');
        });

        // Order items table indexes for vendor earnings and product performance
        Schema::table('order_items', function (Blueprint $table) {
            // Index for order relationship
            $table->index('order_id', 'idx_order_items_order_id');
            
            // Index for product relationship
            $table->index('product_id', 'idx_order_items_product_id');
            
            // Composite index for product sales analysis
            $table->index(['product_id', 'created_at'], 'idx_order_items_product_date');
        });

        // Vendors table indexes for admin management
        Schema::table('vendors', function (Blueprint $table) {
            // Index for approval status filtering
            $table->index('is_approved', 'idx_vendors_is_approved');

            // Index for created_at (for sorting)
            $table->index('created_at', 'idx_vendors_created_at');

            // Note: vendors table doesn't have 'is_active' column, so we skip those indexes
        });

        // Users table indexes for authentication and admin management
        Schema::table('users', function (Blueprint $table) {
            // Index for role-based queries
            $table->index('role_id', 'idx_users_role_id');
            
            // Index for email verification status
            $table->index('email_verified_at', 'idx_users_email_verified');
            
            // Index for created_at (for user registration analytics)
            $table->index('created_at', 'idx_users_created_at');
        });

        // Categories table indexes for product filtering
        Schema::table('categories', function (Blueprint $table) {
            // Index for parent category queries
            $table->index('parent_id', 'idx_categories_parent_id');
            
            // Index for active categories
            $table->index('is_active', 'idx_categories_is_active');
            
            // Index for slug-based lookups
            $table->index('slug', 'idx_categories_slug');
        });

        // Brands table indexes for product filtering
        Schema::table('brands', function (Blueprint $table) {
            // Index for active brands
            $table->index('is_active', 'idx_brands_is_active');
            
            // Index for featured brands
            $table->index('is_featured', 'idx_brands_is_featured');
            
            // Index for slug-based lookups
            $table->index('slug', 'idx_brands_slug');
        });

        // Vendor transactions table indexes for earnings calculations
        if (Schema::hasTable('vendor_transactions')) {
            Schema::table('vendor_transactions', function (Blueprint $table) {
                // Index for vendor earnings queries
                $table->index('vendor_id', 'idx_vendor_transactions_vendor_id');

                // Index for transaction type filtering
                $table->index('type', 'idx_vendor_transactions_type');

                // Index for created_at (for date-based calculations)
                $table->index('created_at', 'idx_vendor_transactions_created_at');

                // Composite index for vendor earnings by type and date
                $table->index(['vendor_id', 'type', 'created_at'], 'idx_vendor_transactions_vendor_type_date');

                // Note: vendor_transactions table doesn't have 'status' column, so we skip that index
            });
        }

        // Commissions table indexes for financial calculations
        if (Schema::hasTable('commissions')) {
            Schema::table('commissions', function (Blueprint $table) {
                // Index for vendor commissions
                $table->index('vendor_id', 'idx_commissions_vendor_id');
                
                // Index for order relationship
                $table->index('order_id', 'idx_commissions_order_id');
                
                // Index for created_at (for commission analytics)
                $table->index('created_at', 'idx_commissions_created_at');
                
                // Composite index for vendor commission calculations
                $table->index(['vendor_id', 'created_at'], 'idx_commissions_vendor_date');
            });
        }

        // Subscriptions table indexes for vendor subscription management
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                // Index for user subscriptions (subscriptions table has user_id, not vendor_id)
                $table->index('user_id', 'idx_subscriptions_user_id');

                // Index for subscription plan
                $table->index('subscription_plan_id', 'idx_subscriptions_plan_id');

                // Index for is_active status (subscriptions table has is_active, not status)
                $table->index('is_active', 'idx_subscriptions_is_active');

                // Index for end_date (subscriptions table has end_date, not ends_at)
                $table->index('end_date', 'idx_subscriptions_end_date');

                // Composite index for active subscriptions
                $table->index(['user_id', 'is_active', 'end_date'], 'idx_subscriptions_user_active_end');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes in reverse order
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->dropIndex('idx_subscriptions_user_active_end');
                $table->dropIndex('idx_subscriptions_end_date');
                $table->dropIndex('idx_subscriptions_is_active');
                $table->dropIndex('idx_subscriptions_plan_id');
                $table->dropIndex('idx_subscriptions_user_id');
            });
        }

        if (Schema::hasTable('commissions')) {
            Schema::table('commissions', function (Blueprint $table) {
                $table->dropIndex('idx_commissions_vendor_date');
                $table->dropIndex('idx_commissions_created_at');
                $table->dropIndex('idx_commissions_order_id');
                $table->dropIndex('idx_commissions_vendor_id');
            });
        }

        if (Schema::hasTable('vendor_transactions')) {
            Schema::table('vendor_transactions', function (Blueprint $table) {
                $table->dropIndex('idx_vendor_transactions_vendor_type_date');
                $table->dropIndex('idx_vendor_transactions_created_at');
                $table->dropIndex('idx_vendor_transactions_type');
                $table->dropIndex('idx_vendor_transactions_vendor_id');
            });
        }

        Schema::table('brands', function (Blueprint $table) {
            $table->dropIndex('idx_brands_slug');
            $table->dropIndex('idx_brands_is_featured');
            $table->dropIndex('idx_brands_is_active');
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex('idx_categories_slug');
            $table->dropIndex('idx_categories_is_active');
            $table->dropIndex('idx_categories_parent_id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_created_at');
            $table->dropIndex('idx_users_email_verified');
            $table->dropIndex('idx_users_role_id');
        });

        Schema::table('vendors', function (Blueprint $table) {
            $table->dropIndex('idx_vendors_created_at');
            $table->dropIndex('idx_vendors_is_approved');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('idx_order_items_product_date');
            $table->dropIndex('idx_order_items_product_id');
            $table->dropIndex('idx_order_items_order_id');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_order_number');
            $table->dropIndex('idx_orders_user_status');
            $table->dropIndex('idx_orders_payment_date');
            $table->dropIndex('idx_orders_created_at');
            $table->dropIndex('idx_orders_payment_status');
            $table->dropIndex('idx_orders_status');
            $table->dropIndex('idx_orders_user_id');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_active_created');
            $table->dropIndex('idx_products_created_at');
            $table->dropIndex('idx_products_stock');
            $table->dropIndex('idx_products_price');
            $table->dropIndex('idx_products_vendor_active');
            $table->dropIndex('idx_products_is_active');
            $table->dropIndex('idx_products_brand_id');
            $table->dropIndex('idx_products_category_id');
            $table->dropIndex('idx_products_vendor_id');
        });
    }
};
