<?php

namespace App\Livewire\Admin\Categories;

use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public ?Category $category = null;
    public bool $showModal = false;

    public string $name = '';
    public string $slug = '';
    public ?int $parent_id = null;

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:categories,slug,' . ($this->category?->id ?? ''),
            'parent_id' => 'nullable|exists:categories,id',
        ];
    }

    public function updatedName($value)
    {
        $this->slug = Str::slug($value);
    }

    public function create()
    {
        $this->resetValidation();
        $this->reset('name', 'slug', 'parent_id', 'category');
        $this->category = new Category();
        $this->showModal = true;
    }

    public function edit(Category $category)
    {
        $this->resetValidation();
        $this->category = $category;
        $this->name = $category->name;
        $this->slug = $category->slug;
        $this->parent_id = $category->parent_id;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'parent_id' => $this->parent_id,
        ];

        // Separate create and update logic for clarity
        if ($this->category && $this->category->exists) {
            $this->category->update($data);
            session()->flash('success', 'Category updated successfully.');
        } else {
            $newCategory = Category::create($data);
            $this->category = $newCategory; // Update the component's category instance
            session()->flash('success', 'Category created successfully.');
        }

        $this->showModal = false;
        $this->reset('name', 'slug', 'parent_id');
    }

    public function delete(Category $category)
    {
        if ($category->children()->count() > 0 || $category->products()->count() > 0) {
            session()->flash('error', 'Cannot delete category with children or products.');
            return;
        }

        $category->delete();
        session()->flash('success', 'Category deleted successfully.');
    }

    public function render()
    {
        return view('livewire.admin.categories.index', [
            'categories' => Category::with('children.children')->whereNull('parent_id')->orderBy('name')->get(),
            'allCategories' => Category::orderBy('name')->get(),
        ]);
    }
}
