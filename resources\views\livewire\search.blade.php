<div>
    {{-- Mobile-optimized search component --}}
    <div class="relative">
        <form action="{{ route('products.search') }}" method="GET" wire:submit.prevent="performSearch">
            <div class="relative">
                <input
                    wire:model.live.debounce.300ms="query"
                    class="py-3 pr-12 pl-4 w-full text-base md:text-sm rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-200"
                    type="search"
                    name="query"
                    placeholder="Search products..."
                    autocomplete="off">
                <button type="submit" class="absolute top-0 right-0 h-full px-4 flex items-center justify-center min-w-[44px] min-h-[44px] rounded-r-full hover:bg-gray-50 transition-colors">
                    <i class="text-gray-500 fas fa-search text-lg"></i>
                </button>
            </div>
        </form>

        @if(!empty($query) && $results->isNotEmpty())
            <div class="absolute z-50 mt-2 w-full max-h-80 bg-white rounded-lg border shadow-lg overflow-y-auto">
                <ul>
                    @foreach($results as $product)
                        <li class="border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                            <a href="{{ route('products.show', $product->slug) }}" class="flex items-center p-4 min-h-[60px]">
                                <img src="{{ $product->thumbnail_url }}" alt="{{ $product->name }}" class="w-12 h-12 object-cover rounded-md flex-shrink-0">
                                <div class="ml-3 flex-1">
                                    <p class="font-semibold text-gray-800 text-sm">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $product->formatted_price }}</p>
                                </div>
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        @elseif(!empty($query))
            <div class="absolute z-50 p-4 mt-2 w-full bg-white rounded-lg border shadow-lg">
                <p class="text-gray-600 text-sm">No results found for "{{ $query }}".</p>
            </div>
        @endif
    </div>
</div>
