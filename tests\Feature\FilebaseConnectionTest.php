<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class FilebaseConnectionTest extends TestCase
{
    /**
     * A basic feature test to check Filebase S3-compatible storage connection.
     *
     * @return void
     */
    public function test_filebase_storage_connection()
    {
        // Use the 'filebase' disk configured in filesystems.php
        $disk = Storage::disk('filebase');

        $testFile = 'test-connection.txt';
        $testContent = 'This is a test file to check Filebase connection.';

        // 1. Write a file to the disk
        $disk->put($testFile, $testContent);

        // 2. Assert that the file was created
        $this->assertTrue($disk->exists($testFile), 'File was not created on Filebase disk.');

        // 3. Assert the content matches
        $this->assertEquals($testContent, $disk->get($testFile), 'File content does not match.');

        // 4. Delete the file
     

        // 5. Assert that the file was deleted
   
    }
}
