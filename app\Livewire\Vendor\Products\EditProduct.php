<?php

namespace App\Livewire\Vendor\Products;

use App\Livewire\Shared\ProductForm as BaseProductForm;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Color;
use App\Models\Product;
use App\Models\Size;
use App\Models\Vendor;

use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.vendor')]
class EditProduct extends BaseProductForm
{
    // CRITICAL FIX: Individual properties for proper Livewire binding
    public $name;
    public $description;
    public $price;
    public $discount_price;
    public $stock;
    public $sku;
    public $category_id;
    public $brand_id;
    public $is_active;
    public $weight;
    public $length;
    public $width;
    public $height;

    // Vendor-specific properties
    public $colors;
    public $sizes;
    public $vendor;
    public $vendorBrand;

    // Dropdown data properties
    public $categories;
    public $brands;

    // UI state properties
    public $isLoading = false;

    public function mount(Product $product)
    {
        if (!$product->exists) {
            return redirect()->route('vendor.products.index')->with('error', 'Product not found for editing.');
        }

        // PRODUCT EDIT FIX: Load the product with all necessary relationships
        $this->product = $product->load(['variants', 'specifications', 'category', 'brand']);

        // CRITICAL FIX: Populate individual properties for proper Livewire binding
        $this->name = $this->product->name;
        $this->description = $this->product->description;
        $this->price = $this->product->price;
        $this->discount_price = $this->product->discount_price;
        $this->stock = $this->product->stock;
        $this->sku = $this->product->sku;
        $this->category_id = $this->product->category_id;
        $this->brand_id = $this->product->brand_id;
        $this->is_active = $this->product->is_active;
        $this->weight = $this->product->weight;
        $this->length = $this->product->length;
        $this->width = $this->product->width;
        $this->height = $this->product->height;

        // Debug: Log product data to ensure it's loaded correctly
        \Log::info('EditProduct mount - Product data loaded', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'product_price' => $this->product->price,
            'product_description' => $this->product->description,
            'product_stock' => $this->product->stock,
            'product_sku' => $this->product->sku,
            'product_category_id' => $this->product->category_id,
            'product_brand_id' => $this->product->brand_id,
            'variants_count' => $this->product->variants->count(),
            'specifications_count' => $this->product->specifications->count()
        ]);
        
        // Get vendor and vendor's brand
        $user = Auth::user();
        if (!$user || !$user->vendor) {
            abort(403, 'Access denied. Vendor account required.');
        }
        $this->vendor = $user->vendor;
        $this->vendorBrand = $this->vendor->brand;

        // Ensure the product belongs to this vendor
        if ($this->product->vendor_id !== $this->vendor->id) {
            abort(403, 'Access denied. You can only edit your own products.');
        }
        
        // Load variants with their images
        foreach ($this->product->variants as $variant) {
            $this->variants[] = [
                'id' => $variant->id,
                'name' => $variant->name,
                'value' => $variant->value,
                'price' => $variant->price,
                'image' => null,
                'existing_image_url' => $variant->image_url // Use the accessor for consistency
            ];
        }
        
        // Load specifications
        foreach ($this->product->specifications as $specification) {
            $this->specifications[] = [
                'id' => $specification->id,
                'name' => $specification->name,
                'value' => $specification->value
            ];
        }
        
        // Set up dropdown data
        $this->categories = Category::all();
        $this->brands = Brand::all();
        $this->colors = Color::orderBy('name')->get();
        $this->sizes = Size::orderBy('name')->get();

        // Initialize variant state based on existing variants
        $this->hasVariants = count($this->variants) > 0;

        // Don't automatically add variants for edit mode - let user decide
        if (empty($this->specifications)) {
            $this->addSpecification();
        }

        // Call the base initialization method
        $this->initializeProductForm();

        // PRODUCT EDIT FIX: Force Livewire to recognize all product properties
        $this->ensureProductPropertiesAccessible();
    }

    /**
     * PRODUCT EDIT FIX: Ensure all product properties are accessible to Livewire
     */
    private function ensureProductPropertiesAccessible()
    {
        // Force all product attributes to be accessible
        if ($this->product) {
            // Make sure all attributes are in the attributes array
            $attributes = [
                'name' => $this->product->name,
                'description' => $this->product->description,
                'price' => $this->product->price,
                'discount_price' => $this->product->discount_price,
                'stock' => $this->product->stock,
                'sku' => $this->product->sku,
                'category_id' => $this->product->category_id,
                'brand_id' => $this->product->brand_id,
                'is_active' => $this->product->is_active,
                'is_featured' => $this->product->is_featured,
            ];

            // Set attributes explicitly
            foreach ($attributes as $key => $value) {
                $this->product->setAttribute($key, $value);
            }

            // Log the final state for debugging
            \Log::info('Product properties after ensuring accessibility', [
                'product_attributes' => $this->product->getAttributes(),
                'product_name' => $this->product->name,
                'product_price' => $this->product->price,
                'product_stock' => $this->product->stock,
            ]);
        }
    }

    /**
     * CRITICAL FIX: Update product model with individual properties
     */
    private function updateProductFromProperties()
    {
        $this->product->name = $this->name;
        $this->product->description = $this->description;
        $this->product->price = $this->price;
        $this->product->discount_price = $this->discount_price;
        $this->product->stock = $this->stock;
        $this->product->sku = $this->sku;
        $this->product->category_id = $this->category_id;
        $this->product->brand_id = $this->brand_id;
        $this->product->is_active = $this->is_active;
        $this->product->weight = $this->weight;
        $this->product->length = $this->length;
        $this->product->width = $this->width;
        $this->product->height = $this->height;
    }

    /**
     * PRODUCT EDIT FIX: Ensure product is properly hydrated before rendering
     */
    public function hydrate()
    {
        if ($this->product && $this->product->exists) {
            $this->ensureProductPropertiesAccessible();
        }
    }

    /**
     * Get the validation rules for Livewire
     *
     * @return array
     */
    protected function rules()
    {
        return array_merge($this->getProductRules(), [
            'product.category_id' => 'required|exists:categories,id',
            'variants.*.image' => 'nullable|image|max:2048',
        ]);
    }

    /**
     * Get the validation rules for the product form
     *
     * @return array
     */
    protected function getProductRules()
    {
        return array_merge(parent::getProductRules(), [
            'product.category_id' => 'required|exists:categories,id',
            'variants.*.image' => 'nullable|image|max:2048',
        ]);
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    protected function messages()
    {
        return array_merge(parent::getMessages(), [
            'product.category_id.required' => 'Please select a category.',
            'product.category_id.exists' => 'Please select a valid category.',
        ]);
    }

    /**
     * Get custom validation messages
     * 
     * @return array
     */
    protected function getCustomMessages()
    {
        return [
            'product.category_id.required' => 'Please select a category.',
        ];
    }

    /**
     * Save the product and related data
     */
    public function save()
    {
        // Set loading state
        $this->isLoading = true;

        try {
            // Explicit authorization check
            $this->authorize('update', $this->product);

            $this->validate();

            // CRITICAL FIX: Update product with individual properties
            $this->updateProductFromProperties();

            // Set vendor and brand
            $this->product->vendor_id = $this->vendor->id;
            if ($this->vendorBrand) {
                $this->product->brand_id = $this->vendorBrand->id;
            }

            // Save the product and handle media
            $this->saveProduct();

            // Save variants and specifications
            $this->saveVariants($this->product);
            $this->saveSpecifications($this->product);

            session()->flash('success', 'Product updated successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Re-throw validation exceptions so Livewire can handle them
            $this->isLoading = false;
            throw $e;
        } catch (\Exception $e) {
            $this->isLoading = false;
            \Log::error('Error saving product', [
                'product_id' => $this->product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'An error occurred while saving the product. Please try again.');
            return;
        } finally {
            $this->isLoading = false;
        }
    }

    /**
     * Handle variant image upload for a specific variant
     * 
     * @param int $variantIndex The index of the variant in the variants array
     * @param string $imageField The name of the image field (default: 'image')
     * @return string|false The uploaded image path or false on failure
     */


    /**
     * Delete a variant's image
     * 
     * @param string $imagePath
     * @return bool
     */


    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.vendor.products.edit-product');
    }
}