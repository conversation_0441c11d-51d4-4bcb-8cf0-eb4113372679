<?php

namespace App\Livewire\Admin\Colors;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\Color;
use Livewire\WithPagination;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $showCreateModal = false;
    public $showEditModal = false;
    public $editingColor = null;

    // Form fields
    public $name = '';
    public $hex_code = '';
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'hex_code' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
        'is_active' => 'boolean',
    ];

    protected $messages = [
        'hex_code.regex' => 'The hex code must be a valid 6-digit hex color (e.g., #FF0000).',
    ];

    public function render()
    {
        $colors = Color::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('hex_code', 'like', '%' . $this->search . '%');
            })
            ->orderBy('name')
            ->paginate(20);

        return view('livewire.admin.colors.index', compact('colors'));
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function create()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        $this->validate();

        Color::create([
            'name' => $this->name,
            'hex_code' => $this->hex_code,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        $this->dispatch('toast', message: 'Color created successfully!', type: 'success');
    }

    public function edit($colorId)
    {
        $this->editingColor = Color::findOrFail($colorId);
        $this->name = $this->editingColor->name;
        $this->hex_code = $this->editingColor->hex_code;
        $this->is_active = $this->editingColor->is_active;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->validate();

        $this->editingColor->update([
            'name' => $this->name,
            'hex_code' => $this->hex_code,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        $this->dispatch('toast', message: 'Color updated successfully!', type: 'success');
    }

    public function delete($colorId)
    {
        $color = Color::findOrFail($colorId);
        
        // Check if color is used in any variants
        if ($color->variants()->exists()) {
            $this->dispatch('toast', message: 'Cannot delete color that is used in product variants.', type: 'error');
            return;
        }

        $color->delete();
        $this->dispatch('toast', message: 'Color deleted successfully!', type: 'success');
    }

    public function toggleStatus($colorId)
    {
        $color = Color::findOrFail($colorId);
        $color->update(['is_active' => !$color->is_active]);
        
        $status = $color->is_active ? 'activated' : 'deactivated';
        $this->dispatch('toast', message: "Color {$status} successfully!", type: 'success');
    }

    private function resetForm()
    {
        $this->name = '';
        $this->hex_code = '';
        $this->is_active = true;
        $this->editingColor = null;
        $this->resetValidation();
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->resetForm();
    }
}
