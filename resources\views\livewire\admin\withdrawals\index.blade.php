<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Withdrawal Requests</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Manage vendor withdrawal requests and bank transfers</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="bg-blue-50 dark:bg-blue-900/20 px-4 py-2 rounded-lg">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
                            Total Requests: {{ $withdrawals->total() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Card View (Hidden on Desktop) -->
        <div class="block lg:hidden space-y-4 mb-6">
            @forelse ($withdrawals as $withdrawal)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden">
                    <div class="p-4 space-y-4">
                        <!-- Withdrawal Header -->
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-semibold text-gray-900 dark:text-white text-sm">
                                    {{ $withdrawal->vendor->business_name ?? $withdrawal->vendor->user->name ?? 'Unknown Vendor' }}
                                </p>
                                @if($withdrawal->vendor->user)
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $withdrawal->vendor->user->email }}</p>
                                @endif
                            </div>
                            <div class="text-right">
                                @if($withdrawal->status === 'pending')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                @elseif($withdrawal->status === 'approved')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                @elseif($withdrawal->status === 'rejected')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Rejected
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Amount -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                            <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Amount</p>
                            <p class="text-lg font-bold text-gray-900 dark:text-white">₦{{ number_format($withdrawal->amount, 2) }}</p>
                        </div>

                        <!-- Bank Details -->
                        @if(!empty($withdrawal->details))
                            <div class="space-y-2">
                                <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Bank Details</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-1">
                                    <p class="text-sm"><span class="font-medium">Bank:</span> {{ $withdrawal->details['bank_name'] ?? 'N/A' }}</p>
                                    <p class="text-sm"><span class="font-medium">Account:</span> {{ $withdrawal->details['account_number'] ?? 'N/A' }}</p>
                                    <p class="text-sm"><span class="font-medium">Name:</span> {{ $withdrawal->details['account_name'] ?? 'N/A' }}</p>
                                </div>
                            </div>
                        @endif

                        <!-- Dates -->
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                            <span>Requested: {{ $withdrawal->created_at->format('M d, Y') }}</span>
                            @if($withdrawal->processed_at)
                                <span>Processed: {{ $withdrawal->processed_at->format('M d, Y') }}</span>
                            @endif
                        </div>

                        <!-- Actions -->
                        @if($withdrawal->status === 'pending')
                            <div class="flex space-x-2 pt-3 border-t border-gray-100 dark:border-gray-600">
                                <button wire:click="approve({{ $withdrawal->id }})"
                                        class="flex-1 inline-flex justify-center items-center px-3 py-2 bg-green-600 text-white text-xs font-medium rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-check mr-1"></i>
                                    Approve
                                </button>
                                <button wire:click="reject({{ $withdrawal->id }})"
                                        class="flex-1 inline-flex justify-center items-center px-3 py-2 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-times mr-1"></i>
                                    Reject
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No withdrawal requests</h3>
                    <p class="text-gray-500 dark:text-gray-400">Withdrawal requests will appear here when vendors submit them.</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on Mobile) -->
        <div class="hidden lg:block bg-white dark:bg-gray-800 shadow-lg rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700/50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendor</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Bank Details</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Requested</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Processed</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($withdrawals as $withdrawal)
                            <tr wire:key="{{ $withdrawal->id }}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    <div>
                                        <div class="font-medium">{{ $withdrawal->vendor->business_name ?? $withdrawal->vendor->user->name ?? 'Unknown Vendor' }}</div>
                                        @if($withdrawal->vendor->user)
                                            <div class="text-xs text-gray-500">{{ $withdrawal->vendor->user->email }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">₦{{ number_format($withdrawal->amount, 2) }}</td>
                                <td class="px-6 py-4 text-sm">
                                    @if(!empty($withdrawal->details))
                                        <div class="space-y-1">
                                            <div><span class="font-medium text-gray-700">Bank:</span> {{ $withdrawal->details['bank_name'] ?? 'N/A' }}</div>
                                            <div><span class="font-medium text-gray-700">Account:</span> {{ $withdrawal->details['account_number'] ?? 'N/A' }}</div>
                                            <div><span class="font-medium text-gray-700">Name:</span> {{ $withdrawal->details['account_name'] ?? 'N/A' }}</div>
                                        </div>
                                        <div class="mt-3">
                                            <x-button small wire:click="resolveAccountName({{ $withdrawal->id }})"
                                                label="Verify Account"
                                                class="text-xs" />
                                            @if(isset($resolvedAccountNames[$withdrawal->id]))
                                                <div class="text-xs mt-2 p-2 rounded-md {{ $resolvedAccountNames[$withdrawal->id] === 'Unable to resolve' ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700' }}">
                                                    <strong>Verified:</strong> {{ $resolvedAccountNames[$withdrawal->id] }}
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-500 italic">No bank details provided</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <x-badge :label="ucfirst($withdrawal->status)" 
                                        :color="$withdrawal->status === 'completed' ? 'positive' : ($withdrawal->status === 'pending' ? 'warning' : 'negative')" />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $withdrawal->created_at->format('d M, Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $withdrawal->processed_at ? $withdrawal->processed_at->format('d M, Y') : 'N/A' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    @if ($withdrawal->status === 'pending')
                                        <div class="flex flex-col space-y-2">
                                            @if(isset($resolvedAccountNames[$withdrawal->id]) && $resolvedAccountNames[$withdrawal->id] !== 'Unable to resolve' && $resolvedAccountNames[$withdrawal->id] !== 'Missing details')
                                                <x-button positive wire:click="updateStatus({{ $withdrawal->id }}, 'approved')"
                                                    label="Approve"
                                                    class="text-xs"
                                                    wire:loading.attr="disabled"
                                                    wire:target="updateStatus({{ $withdrawal->id }}, 'approved')" />
                                            @else
                                                <div class="text-xs text-amber-600 bg-amber-50 p-2 rounded-md">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                    Verify account details first
                                                </div>
                                            @endif
                                            <x-button negative wire:click="updateStatus({{ $withdrawal->id }}, 'rejected')"
                                                label="Reject"
                                                class="text-xs"
                                                wire:loading.attr="disabled"
                                                wire:target="updateStatus({{ $withdrawal->id }}, 'rejected')" />
                                        </div>
                                    @else
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Processed
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">No withdrawal requests found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="mt-6">
            {{ $withdrawals->links() }}
        </div>
    </div>
</div>
