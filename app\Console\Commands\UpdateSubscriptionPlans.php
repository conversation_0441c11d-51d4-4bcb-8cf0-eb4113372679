<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SubscriptionService;

class UpdateSubscriptionPlans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:update-plans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update subscription plans with correct pricing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating subscription plans...');
        
        $subscriptionService = app(SubscriptionService::class);
        
        // Update all plans with correct pricing
        $subscriptionService->ensureAllPlans();
        
        $this->info('✅ Subscription plans updated successfully!');
        $this->info('✅ Monthly Plan: ₦9,000');
        $this->info('✅ Bi-Annual Plan: ₦8,700/month');
        $this->info('✅ Annual Plan: ₦8,500/month');
        
        return 0;
    }
}
