@php
// CRITICAL FIX: Filter navigation links to only show routes that exist
$navLinks = [
    ['route' => 'admin.dashboard', 'label' => 'Dashboard', 'icon' => 'fa-chart-line', 'active' => 'admin.dashboard'],
    ['route' => 'admin.users.index', 'label' => 'Users', 'icon' => 'fa-users', 'active' => 'admin.users.*'],
    ['route' => 'admin.vendors.index', 'label' => 'Vendors', 'icon' => 'fa-store', 'active' => 'admin.vendors.*'],
    ['route' => 'admin.brands.index', 'label' => 'Brands', 'icon' => 'fa-award', 'active' => 'admin.brands.*'],
    ['route' => 'admin.categories.index', 'label' => 'Categories', 'icon' => 'fa-tags', 'active' => 'admin.categories.*'],
    ['route' => 'admin.colors.index', 'label' => 'Colors', 'icon' => 'fa-palette', 'active' => 'admin.colors.*'],
    ['route' => 'admin.sizes.index', 'label' => 'Sizes', 'icon' => 'fa-ruler', 'active' => 'admin.sizes.*'],
    ['route' => 'admin.products.index', 'label' => 'Products', 'icon' => 'fa-box', 'active' => 'admin.products.*'],
    ['route' => 'admin.products.best-sellers', 'label' => 'Best Sellers', 'icon' => 'fa-star', 'active' => 'admin.products.best-sellers'],
    ['route' => 'admin.orders.index', 'label' => 'Orders', 'icon' => 'fa-shopping-cart', 'active' => 'admin.orders.*'],
    ['route' => 'admin.payments.index', 'label' => 'Payments', 'icon' => 'fa-credit-card', 'active' => 'admin.payments.*'],
    ['route' => 'admin.commissions.index', 'label' => 'Commissions', 'icon' => 'fa-percentage', 'active' => 'admin.commissions.*'],
    ['route' => 'admin.withdrawals.index', 'label' => 'Withdrawals', 'icon' => 'fa-money-bill-wave', 'active' => 'admin.withdrawals.*'],
    ['route' => 'admin.subscriptions.index', 'label' => 'Subscriptions', 'icon' => 'fa-calendar-check', 'active' => 'admin.subscriptions.*'],
    ['route' => 'admin.subscription-plans.index', 'label' => 'Subscription Plans', 'icon' => 'fa-list-alt', 'active' => 'admin.subscription-plans.*'],
    ['route' => 'admin.settings.index', 'label' => 'Settings', 'icon' => 'fa-cog', 'active' => 'admin.settings.*'],
];

// Filter out routes that don't exist to prevent errors
$navLinks = array_filter($navLinks, function($link) {
    try {
        return \Illuminate\Support\Facades\Route::has($link['route']);
    } catch (\Exception $e) {
        return false;
    }
});
@endphp

<nav class="flex-1 space-y-1 px-4 pb-4" aria-label="Admin navigation">
    @foreach ($navLinks as $link)
        {{-- CRITICAL FIX NA2: Added wire:navigate for consistent SPA-like navigation --}}
        <a href="{{ route($link['route']) }}"
           wire:navigate
           aria-label="{{ $link['label'] }}"
           aria-current="{{ request()->routeIs($link['active'] ?? $link['route']) ? 'page' : 'false' }}"
           class="{{ request()->routeIs($link['active'] ?? $link['route']) ? 'bg-white/10 text-white border-r-4 border-white shadow-lg' : 'text-gray-300 hover:bg-white/5 hover:text-white' }} group flex items-center rounded-xl px-4 py-4 text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg min-h-[56px]">
            <i class="fas {{ $link['icon'] }} mr-4 text-lg {{ request()->routeIs($link['active'] ?? $link['route']) ? 'text-white' : 'text-gray-400 group-hover:text-gray-300' }} flex-shrink-0"></i>
            <span class="font-semibold">{{ $link['label'] }}</span>
        </a>
    @endforeach

    <div class="pt-4 space-y-1">
        <a href="{{ route('home') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center rounded-md px-4 py-4 text-sm font-medium min-h-[56px]">
            <i class="fas fa-home mr-3 text-lg flex-shrink-0 text-gray-400 group-hover:text-gray-300"></i>
            Back to Site
        </a>
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex w-full items-center rounded-md px-4 py-4 text-sm font-medium min-h-[56px]">
                <i class="fas fa-sign-out-alt mr-3 text-lg flex-shrink-0 text-gray-400 group-hover:text-gray-300"></i>
                Logout
            </button>
        </form>
    </div>
</nav>
