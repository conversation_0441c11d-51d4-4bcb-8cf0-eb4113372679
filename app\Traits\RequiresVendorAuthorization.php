<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

/**
 * Vendor authorization trait for Livewire components
 * 
 * This trait provides consistent vendor authorization checks
 * to resolve LOGIC-HIGH-002: Vendor authorization enforcement gap
 * 
 * Usage:
 * - Vendor Livewire Components
 * - Vendor Controllers
 * - Any component requiring vendor access
 */
trait RequiresVendorAuthorization
{
    /**
     * Boot method to automatically enforce vendor authorization
     * This is called automatically when the trait is used
     */
    public function bootRequiresVendorAuthorization()
    {
        $this->enforceVendorAuthorization();
    }

    /**
     * Enforce vendor authorization with comprehensive checks
     * 
     * @param bool $requireApproval Whether vendor must be approved
     * @param bool $requireSubscription Whether vendor must have active subscription
     * @return void
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function enforceVendorAuthorization(bool $requireApproval = true, bool $requireSubscription = false): void
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            abort(401, 'Authentication required. Please login to access vendor features.');
        }

        $user = Auth::user();

        // Check if user has vendor role
        if (!$user->hasRole('vendor')) {
            abort(403, 'Access denied. Vendor account required.');
        }

        // Check if user has an associated vendor record
        if (!$user->vendor) {
            abort(403, 'Access denied. Vendor profile not found. Please complete vendor registration.');
        }

        $vendor = $user->vendor;

        // Check vendor approval status if required
        if ($requireApproval && !$vendor->is_approved) {
            abort(403, 'Access denied. Vendor account must be approved. Please contact support.');
        }

        // Check vendor subscription status if required
        if ($requireSubscription) {
            $subscriptionService = app(\App\Services\SubscriptionService::class);
            if (!$subscriptionService->hasActiveSubscription($vendor)) {
                abort(403, 'Access denied. Active subscription required. Please renew your subscription.');
            }
        }

        // Additional security: Check if vendor account is not suspended
        if (isset($vendor->status) && $vendor->status === 'suspended') {
            abort(403, 'Access denied. Vendor account is suspended. Please contact support.');
        }
    }

    /**
     * Check if current user is an authorized vendor
     * 
     * @param bool $requireApproval Whether vendor must be approved
     * @param bool $requireSubscription Whether vendor must have active subscription
     * @return bool
     */
    protected function isAuthorizedVendor(bool $requireApproval = true, bool $requireSubscription = false): bool
    {
        try {
            $this->enforceVendorAuthorization($requireApproval, $requireSubscription);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get the current vendor or throw exception if not authorized
     * 
     * @param bool $requireApproval Whether vendor must be approved
     * @param bool $requireSubscription Whether vendor must have active subscription
     * @return \App\Models\Vendor
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function getAuthorizedVendor(bool $requireApproval = true, bool $requireSubscription = false): \App\Models\Vendor
    {
        $this->enforceVendorAuthorization($requireApproval, $requireSubscription);
        return Auth::user()->vendor;
    }

    /**
     * Check if current vendor owns the specified resource
     * 
     * @param mixed $resource Resource with vendor_id property
     * @return bool
     */
    protected function vendorOwnsResource($resource): bool
    {
        if (!Auth::check() || !Auth::user()->vendor) {
            return false;
        }

        $vendorId = Auth::user()->vendor->id;

        // Handle different resource types
        if (is_object($resource)) {
            return isset($resource->vendor_id) && $resource->vendor_id === $vendorId;
        }

        if (is_array($resource)) {
            return isset($resource['vendor_id']) && $resource['vendor_id'] === $vendorId;
        }

        return false;
    }

    /**
     * Enforce that current vendor owns the specified resource
     * 
     * @param mixed $resource Resource with vendor_id property
     * @param string $resourceName Name of resource for error message
     * @return void
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function enforceVendorOwnership($resource, string $resourceName = 'resource'): void
    {
        if (!$this->vendorOwnsResource($resource)) {
            abort(403, "Access denied. You don't have permission to access this {$resourceName}.");
        }
    }

    /**
     * Get vendor authorization status information
     * 
     * @return array
     */
    protected function getVendorAuthorizationStatus(): array
    {
        if (!Auth::check()) {
            return [
                'authenticated' => false,
                'has_vendor_role' => false,
                'has_vendor_profile' => false,
                'is_approved' => false,
                'has_active_subscription' => false,
                'status' => 'unauthenticated'
            ];
        }

        $user = Auth::user();
        $hasVendorRole = $user->hasRole('vendor');
        $hasVendorProfile = (bool) $user->vendor;
        $isApproved = $hasVendorProfile && $user->vendor->is_approved;
        
        $hasActiveSubscription = false;
        if ($hasVendorProfile) {
            $subscriptionService = app(\App\Services\SubscriptionService::class);
            $hasActiveSubscription = $subscriptionService->hasActiveSubscription($user->vendor);
        }

        $status = 'unauthorized';
        if ($hasVendorRole && $hasVendorProfile && $isApproved) {
            $status = $hasActiveSubscription ? 'fully_authorized' : 'subscription_required';
        } elseif ($hasVendorRole && $hasVendorProfile) {
            $status = 'approval_required';
        } elseif ($hasVendorRole) {
            $status = 'profile_required';
        }

        return [
            'authenticated' => true,
            'has_vendor_role' => $hasVendorRole,
            'has_vendor_profile' => $hasVendorProfile,
            'is_approved' => $isApproved,
            'has_active_subscription' => $hasActiveSubscription,
            'status' => $status
        ];
    }

    /**
     * Redirect to appropriate page based on vendor authorization status
     * 
     * @return \Illuminate\Http\RedirectResponse|null
     */
    protected function redirectBasedOnVendorStatus(): ?\Illuminate\Http\RedirectResponse
    {
        $status = $this->getVendorAuthorizationStatus();

        if (!$status['authenticated']) {
            return redirect()->route('login')->with('error', 'Please login to access vendor features.');
        }

        if (!$status['has_vendor_role']) {
            return redirect()->route('home')->with('error', 'Vendor account required.');
        }

        if (!$status['has_vendor_profile']) {
            return redirect()->route('vendor.register')->with('error', 'Please complete vendor registration.');
        }

        if (!$status['is_approved']) {
            return redirect()->route('vendor.pending')->with('info', 'Your vendor account is pending approval.');
        }

        if (!$status['has_active_subscription']) {
            return redirect()->route('vendor.subscription.index')->with('warning', 'Active subscription required.');
        }

        return null; // No redirect needed, vendor is fully authorized
    }

    /**
     * Log vendor authorization attempt for security monitoring
     * 
     * @param string $action Action being attempted
     * @param bool $success Whether authorization was successful
     * @param string|null $reason Reason for failure if applicable
     * @return void
     */
    protected function logVendorAuthorizationAttempt(string $action, bool $success, ?string $reason = null): void
    {
        $logData = [
            'action' => $action,
            'success' => $success,
            'user_id' => Auth::id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ];

        if (!$success && $reason) {
            $logData['failure_reason'] = $reason;
        }

        if (Auth::check() && Auth::user()->vendor) {
            $logData['vendor_id'] = Auth::user()->vendor->id;
        }

        \Log::info('Vendor authorization attempt', $logData);
    }
}
