<!-- Recommended Products -->
<div class="bg-white rounded-lg shadow-md mb-6">
    <div class="p-6 border-b border-gray-200">
        <h5 class="font-bold text-lg">Recommended For You</h5>
    </div>
    <div class="p-6">
        @php
            $recommendedProducts = App\Models\Product::inRandomOrder()->where('is_active', true)->limit(4)->get();
        @endphp

        @if($recommendedProducts->isNotEmpty())
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($recommendedProducts as $product)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden flex flex-col group">
                        <div class="relative">
                            <img src="{{ $product->image_url ?? 'https://via.placeholder.com/300x300?text=Product' }}"
                                 alt="{{ $product->name }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        </div>
                        <div class="p-4 flex flex-col flex-grow">
                            <p class="text-gray-500 text-sm mb-1">{{ $product->category->name ?? 'Uncategorized' }}</p>
                            <h6 class="font-bold mb-2 flex-grow">{{ Str::limit($product->name, 40) }}</h6>
                            <p class="font-bold text-lg mb-3">₦{{ number_format($product->price, 2) }}</p>
                            <a href="{{ route('products.show', $product->slug) }}" class="mt-auto w-full inline-flex items-center justify-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                                <i class="fas fa-eye mr-2"></i> View
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-box-open fa-4x text-gray-300 mb-4"></i>
                <h6 class="text-lg font-semibold text-gray-700">No products available right now</h6>
                <p class="text-gray-500">Please check back later for new arrivals!</p>
            </div>
        @endif
    </div>
</div>
