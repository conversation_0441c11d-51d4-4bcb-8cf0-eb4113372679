<?php

use App\Models\ProductVariant;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, ensure the media table exists before proceeding
        if (!Schema::hasTable('media')) {
            Log::warning('Spatie media library table not found. Skipping variant image migration.');
            return;
        }

        ProductVariant::whereNotNull('image_path')->chunk(100, function ($variants) {
            foreach ($variants as $variant) {
                try {
                    if (Storage::disk('public')->exists($variant->image_path)) {
                        $fullPath = Storage::disk('public')->path($variant->image_path);
                        $variant->addMedia($fullPath)
                                ->preservingOriginal()
                                ->toMediaCollection('variant_image');
                    }
                } catch (\Exception $e) {
                    Log::error("Could not migrate image for variant {$variant->id}: " . $e->getMessage());
                }
            }
        });

        // Now, drop the old column
        Schema::table('product_variants', function (Blueprint $table) {
            $table->dropColumn('image_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('media_library', function (Blueprint $table) {
            //
        });
    }
};
