{"app/Console/Commands/CheckVendorBrandData.php": {"summary": "This file defines an Artisan command `check:vendor-brand-data`. The command is a diagnostic tool that inspects and reports on the relationships between `Vendors`, `Brands`, and `Products`. It fetches and displays information about which vendors are linked to which brands, and which products belong to which vendor and brand, helping to identify any data inconsistencies. It is a read-only command and does not modify any data.", "dependencies": ["App\\Models\\Brand", "App\\Models\\Product", "App\\Models\\Vendor"]}, "app/Console/Commands/FixVendorBrandLinks.php": {"summary": "This file defines an Artisan command `fix:vendor-brand-links`. This is a data-fix command that attempts to associate `Brand` records with corresponding `Vendor` records. It iterates through all vendors and looks for a brand with the same name as the vendor's `shop_name`. If a match is found, it directly updates the `brands` table to set the `vendor_id`. After linking vendors to brands, it performs a second step to update the `brand_id` on all products, ensuring they are correctly associated with the newly linked brand of their respective vendor. This command performs direct database writes and could have significant impact on data integrity.", "dependencies": ["App\\Models\\Brand", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\DB"]}, "app/Console/Commands/ProcessPendingBalances.php": {"summary": "This file defines an Artisan command `vendor:process-pending-balances`. Its purpose is to trigger the processing of pending vendor earnings. The command instantiates a `VendorEarningsService` and calls the `processPendingBalances` method on it. This suggests that the core logic for calculating and releasing funds to vendors after a holding period is encapsulated within the `VendorEarningsService`. This command acts as a scheduled entry point to that business logic.", "dependencies": ["App\\Services\\VendorEarningsService"]}, "app/Console/Commands/SyncProductBrands.php": {"summary": "This file defines the Artisan command `product:sync-brands`. Its purpose is to ensure that every product is associated with the correct brand, based on the product's vendor. It iterates through all products, loading their related vendor and the vendor's brand. If a product's currently assigned `brand_id` does not match its vendor's brand ID, the command updates the product record with the correct ID. This command is crucial for maintaining data consistency between products, vendors, and brands. It includes checks to avoid unnecessary updates and provides informative output about its actions.", "dependencies": ["App\\Models\\Product"]}, "app/Console/Commands/SyncVendorBrands.php": {"summary": "This file defines the Artisan command `vendor:sync-brands`. This command's primary function is to ensure that every vendor has a corresponding brand record. It iterates through all vendors and performs one of three actions: 1) It links an existing, unlinked brand to a vendor if the names match. 2) It creates a new brand for a vendor if one doesn't exist, copying details like name and slug from the vendor. 3) It updates the vendor's existing brand details to ensure they remain synchronized with the vendor's information. This command actively creates and updates database records to maintain data consistency.", "dependencies": ["App\\Models\\Brand", "App\\Models\\Vendor"]}, "app/Console/Commands/TestAdminAccess.php": {"summary": "This file defines a scaffolded Artisan command named `app:test-admin-access`. The command is non-functional as its `handle` method is empty and it contains a placeholder description. It appears to be unused boilerplate code that was generated but never implemented.", "dependencies": []}, "app/Facades/Cart.php": {"summary": "This file is explicitly marked as deprecated. A comment at the top of the file states that the `Cart` facade is no longer used and that the application now manages the cart directly through the session. This file appears to be legacy code that has not been removed.", "dependencies": []}, "app/helpers.php": {"summary": "This file provides a set of global helper functions primarily focused on currency formatting and commission calculations, tailored for the Nigerian Nair<PERSON> (₦). The functions include `format_currency` and its variants, `calculate_commission`, `format_commission`, and `get_commission_rate_percentage`. These functions rely on a `brandify` configuration file for the currency symbol and commission rate, and are wrapped in `!function_exists()` checks to prevent conflicts.", "dependencies": ["config/brandify.php"]}, "app/Helpers/array_helpers.php": {"summary": "This file provides a set of global helper functions for safe array operations. It defines `safe_array_key_exists`, `safe_array_get`, and `ensure_array`, which act as wrappers around corresponding static methods in the `App\\Helpers\\ArrayHelper` class. Additionally, it includes a debugging function, `debug_array_key_exists`, which logs a warning if it's called with a non-array value during development. These helpers are designed to prevent common errors when dealing with potentially non-array variables.", "dependencies": ["App\\Helpers\\ArrayHelper", "Illuminate\\Support\\Facades\\Log"]}, "app/Helpers/ArrayHelper.php": {"summary": "This file defines the `ArrayHelper` class, which contains a suite of `public static` methods for robust and safe array handling. It provides the concrete implementation for the global helper functions defined in `array_helpers.php`. Key methods include `safeArrayKeyExists`, `safeGet`, `safeIsset`, and `ensureArray`. These methods prevent common PHP errors by checking if the input is an array before performing operations. For several methods, if a non-array is passed, it logs a detailed warning, including a backtrace, to aid in debugging, and then returns a safe default value. This class is a crucial utility for defensive programming throughout the application.", "dependencies": ["Illuminate\\Support\\Facades\\Log"]}, "app/Helpers/Location.php": {"summary": "This file defines a `Location` helper class that contains a hardcoded, comprehensive list of all Nigerian states and their corresponding Local Government Areas (LGAs). It provides several `public static` methods to interact with this data, including `getStates`, `getLgas`, `getStateNames`, `stateExists`, and `lgaExists`. This class serves as a self-contained, in-memory data source for Nigerian geographical information, eliminating the need for database queries for this specific data.", "dependencies": []}, "app/Http/Controllers/Admin/DashboardController.php": {"summary": "This controller's `index` method is responsible for aggregating key metrics and recent activity for the admin dashboard. It efficiently calculates total sales, orders, customers (specifically users who are not vendors), and total vendors. It also fetches the five most recent pending vendor applications and the five latest orders, eager-loading the necessary relationships to optimize performance. The controller is purely for data presentation and does not handle any data modification.", "dependencies": ["App\\Models\\Order", "App\\Models\\User", "App\\Models\\Vendor", "resources/views/admin/dashboard.blade.php"]}, "app/Http/Controllers/Admin/SubscriptionPlanController.php": {"summary": "This controller handles the deletion of subscription plans. The `destroy` method contains a critical safety check that prevents the deletion of a plan if it has active subscribers. This is a good practice as it protects data integrity and prevents orphaned subscription records. This controller implements a necessary validation check before performing a destructive action.", "dependencies": ["App\\Models\\SubscriptionPlan"]}, "app/Http/Controllers/Admin/UserController.php": {"summary": "This controller handles user deletion. The `destroy` method directly deletes a user without any validation or safety checks. This is a critical issue that confirms the memory about minimal validation in admin components. Deleting a user could lead to orphaned records (like orders or vendor profiles) or database errors if relationships are not properly handled. There are no checks to prevent an admin from deleting their own account or to confirm the action, which poses a significant risk to data integrity and application stability.", "dependencies": ["App\\Models\\User"]}, "app/Http/Controllers/Admin/VendorController.php": {"summary": "This controller contains a critical security vulnerability in its `destroy` method. There is no authorization check to ensure that only authenticated administrators can delete a vendor, leaving the application open to unauthorized data deletion. Additionally, the method does not handle the cascading effects of deleting a vendor, which could lead to orphaned data and application instability. This is a high-risk issue that needs to be addressed immediately.", "dependencies": ["App\\Http\\Controllers\\Controller", "App\\Models\\Vendor"]}, "app/Http/Controllers/Api/VendorOrdersController.php": {"summary": "This is a well-architected and production-ready API controller designed to supply data for a vendor's dashboard map. The `getOrdersByState` method is highly optimized, using caching to reduce database load and efficient queries to aggregate order counts and values by state. It is secure, ensuring a vendor can only access their own data. The controller is also robust, gracefully handling different data formats (plain text vs. JSON for addresses), normalizing state names for consistent visualization, and featuring solid error handling that logs issues and returns clean, empty datasets on failure.", "dependencies": ["App\\Models\\Order", "App\\Models\\Product", "Illuminate\\Support\\Facades\\DB", "Illuminate\\Support\\Facades\\Cache", "Illuminate\\Support\\Facades\\Schema", "Carbon\\Carbon"]}, "app/Http/Controllers/Auth/VerifyEmailController.php": {"summary": "This is a standard Laravel single-action controller that handles the email verification process. It correctly uses the `EmailVerificationRequest` to securely validate the request, marks the user's email as verified, and dispatches the `Verified` event. The implementation is secure and follows Laravel's best practices for this feature.", "dependencies": ["Illuminate\\Auth\\Events\\Verified", "Illuminate\\Foundation\\Auth\\EmailVerificationRequest"]}, "app/Http/Controllers/CartController.php": {"summary": "This is a lean controller with a single responsibility: adding a product to the session-based shopping cart. The `add` method uses route-model binding to receive a `Product` object and either adds it to the cart or increments its quantity if it already exists. This controller does not handle other cart functionalities like updating quantities, removing items, or displaying the cart, which are likely managed by a Livewire component.", "dependencies": ["App\\Models\\Product", "Illuminate\\Http\\Request"]}, "app/Http/Controllers/Controller.php": {"summary": "This is <PERSON><PERSON>'s base `Controller` class. It is an empty, abstract class that serves as the foundation for all other controllers in the application. It currently contains no custom methods or traits, indicating that it is being used in its default, unmodified state.", "dependencies": []}, "app/Http/Controllers/HomeController.php": {"summary": "This controller manages two primary responsibilities: displaying the homepage and handling the contact form submission. The `index` method is highly optimized for performance, using caching with a 10-minute duration for all its database queries, including best-selling products, new arrivals, featured categories, and featured brands. It also correctly eager-loads relationships to prevent N+1 issues. Data is then passed to the `welcome-bw` view. The `handleContactForm` method provides robust validation and error handling for the contact form. It uses <PERSON><PERSON>'s validator and a `try-catch` block to manage email sending via the `ContactFormMail` mailable. A potential improvement noted in the code is to move the hardcoded admin email address to a configuration file.", "dependencies": ["App\\Models\\Product", "App\\Models\\Category", "App\\Models\\Vendor", "App\\Models\\Brand", "Illuminate\\Support\\Facades\\Validator", "Illuminate\\Support\\Facades\\Mail", "App\\Mail\\ContactFormMail", "Illuminate\\Support\\Facades\\Cache", "resources/views/welcome-bw.blade.php"]}, "app/Http/Controllers/OrderController.php": {"summary": "This controller handles all customer-facing actions related to their orders. It is well-structured, utilizing dependency injection for the `ViewedProductsService` to display recently viewed items. Key methods include `index` for listing orders, `show` for viewing a specific order, `cancel` for cancelling an order, and `requestReturn` for initiating a return. The controller correctly implements authorization checks in each method to ensure users can only access their own orders. The business logic is sound, for instance, only allowing cancellations for pending or processing orders and returns for completed orders.", "dependencies": ["App\\Models\\Order", "App\\Services\\ViewedProductsService", "resources/views/customer/orders/index.blade.php", "resources/views/customer/orders/show.blade.php"]}, "app/Http/Controllers/PaymentController.php": {"summary": "This is a comprehensive and robust controller that orchestrates the entire payment and order fulfillment lifecycle, primarily integrating with Paystack for payments and ShipBubble for shipping. It handles payment initialization, processes callbacks and webhooks, creates orders, manages inventory, generates shipping labels, and calculates vendor earnings. The controller is well-designed, delegating tasks to injected services (`PaystackService`, `ShipBubbleService`, `VendorEarningsService`). The implementation is highly resilient, using database transactions, redundant verification (callback and webhook), and extensive error handling that includes restoring stock on failure and placing orders on hold. It also contains strong security and privacy measures, with methods for sanitizing input and redacting log data. The logic appears to correctly handle `ShipBubbleService` exceptions, which may resolve a previously noted concern.", "dependencies": ["App\\Models\\Order", "App\\Models\\Commission", "App\\Models\\Product", "App\\Models\\CheckoutSession", "App\\Services\\PaystackService", "App\\Services\\ShipBubbleService", "App\\Services\\VendorEarningsService", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\DB", "Illuminate\\Support\\Facades\\Validator", "Illuminate\\Support\\Str"]}, "app/Http/Controllers/ReviewController.php": {"summary": "This controller securely handles the submission of product reviews. The `store` method includes robust validation for the rating and comment fields. Crucially, it contains two important authorization checks: first, it verifies that the user has actually purchased the product by checking for a completed order, which prevents fake reviews. Second, it ensures that a user cannot review the same product more than once. If these checks pass, it creates the review and associates it with the correct product and user.", "dependencies": ["App\\Models\\Product", "Illuminate\\Support\\Facades\\Auth"]}, "app/Http/Controllers/ShippingController.php": {"summary": "This controller is the central hub for calculating shipping costs. It intelligently groups cart items by vendor and fetches rates for each individual shipment via the `ShipBubbleService`. Its most significant feature is the consolidation of these rates, presenting the user with a single, combined shipping cost per courier, even when purchasing from multiple vendors. This simplifies the checkout process significantly. The controller also enriches the shipping options with user-friendly descriptions, delivery estimates, and features, demonstrating a strong focus on user experience.", "dependencies": ["App\\Services\\ShipBubbleService", "App\\Models\\Product", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\Validator", "Illuminate\\Support\\Facades\\Log"]}, "app/Http/Controllers/VendorController.php": {"summary": "This controller handles the public-facing storefront for a vendor. The `show` method is responsible for fetching an active and approved vendor by their unique slug. It then retrieves a paginated list of the vendor's active products to display on their shop page. The implementation is clean, secure, and follows best practices by using `firstOrFail` to handle cases where a vendor isn't found.", "dependencies": ["App\\Models\\Vendor", "App\\Models\\Product", "resources/views/vendor/show.blade.php"]}, "app/Http/Controllers/Vendor/ProductController.php": {"summary": "This is a comprehensive and well-structured controller that manages the entire product lifecycle for a vendor. It handles full CRUD (Create, Read, Update, Delete) functionality for products, including complex nested resources like variants and specifications. The controller demonstrates strong adherence to best practices, including robust authorization, extensive validation, enforcement of subscription plan limits, and the use of soft deletes to ensure data integrity.", "dependencies": ["App\\Models\\Product", "App\\Models\\Category", "App\\Models\\Brand", "App\\Models\\Color", "App\\Models\\Size", "App\\Models\\ProductVariant", "Illuminate\\Http\\Request", "Illuminate\\Support\\Str", "Illuminate\\Support\\Facades\\Storage", "resources/views/vendor/products/index.blade.php", "resources/views/vendor/products/create.blade.php", "resources/views/vendor/products/edit.blade.php"]}, "app/Http/Controllers/Vendor/SubscriptionController.php": {"summary": "This is a robust and secure controller that manages the entire vendor subscription lifecycle through Paystack. It implements both a user-facing callback for immediate feedback after payment and a comprehensive server-to-server webhook handler for reliable, asynchronous updates. The webhook is well-secured with signature verification and handles multiple critical events, including subscription creation, cancellation, failed payments, and non-renewals, ensuring the application's subscription data stays in sync with Paystack.", "dependencies": ["App\\Models\\SubscriptionPlan", "App\\Models\\VendorSubscription", "App\\Services\\PaystackService", "Illuminate\\Support\\Facades\\Auth", "Carbon\\Carbon"]}, "app/Livewire/Admin/Users/<USER>": {"summary": "This component is a significant finding. It is responsible for listing and deleting users and, unlike the corresponding `Admin/UserController`, it implements crucial safety checks. It correctly prevents an admin from deleting their own account and from deleting the last remaining admin user. The component also uses a confirmation step before deletion and provides clear user feedback. This effectively mitigates the security and data integrity risks that were identified during the controller audit.", "dependencies": ["App\\Models\\User", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/admin/users/index.blade.php"]}, "app/Livewire/Admin/Vendors/Index.php": {"summary": "This component handles listing vendors and allows an admin to manage their status (approve, reject, feature). It includes a proper authorization check to ensure only admins can access it. A critical observation is the complete absence of a delete method, which differs from the `Users` component and suggests the vendor deletion functionality is handled elsewhere.", "dependencies": ["App\\Models\\Vendor", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/admin/vendors/index.blade.php"]}, "app/Livewire/Admin/Vendors/VendorForm.php": {"summary": "This is a comprehensive and well-written component for creating and updating vendors. It features robust validation rules for all fields, handles file uploads for the vendor banner, and securely wraps all database operations in a transaction. However, it is critically missing a method for deleting a vendor, confirming that the insecure `Admin/VendorController@destroy` method is likely used directly.", "dependencies": ["App\\Models\\User", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\DB", "Illuminate\\Support\\Facades\\Hash", "Livewire\\Component", "Livewire\\WithFileUploads", "resources/views/livewire/admin/vendors/vendor-form.blade.php"]}, "app/Livewire/Checkout/Index.php": {"summary": "This is an exceptionally large and complex component that serves as the central hub for the entire checkout process. It is well-architected and demonstrates a very high level of attention to detail, security, and user experience. It features robust, multi-stage validation, resilient service integration with fallbacks, rich user feedback, and secure state management, making it a model for complex Livewire components.", "dependencies": ["App\\Helpers\\Location", "App\\Models\\User", "App\\Services\\ShipBubbleService", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "resources/views/livewire/checkout/index.blade.php"]}, "app/Livewire/Checkout/Success.php": {"summary": "This component securely handles the post-payment success page. It correctly uses a transaction ID to fetch all related orders and includes a crucial authorization check to ensure users can only view their own order details. It also gracefully handles edge cases where the transaction ID is missing or invalid, redirecting with an appropriate error message. The implementation is secure and robust.", "dependencies": ["App\\Models\\Order", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "resources/views/livewire/checkout/success.blade.php"]}, "app/Livewire/Cart/Index.php": {"summary": "This is a well-structured component for the main shopping cart page. It effectively manages the session-based cart, providing all essential features like quantity updates, item removal, and clearing the cart. It also includes a sophisticated product recommendation engine and correctly dispatches events to keep the UI in sync.", "dependencies": ["App\\Models\\Product", "Livewire\\Component", "resources/views/livewire/cart/index.blade.php"]}, "app/Livewire/AddToCartButton.php": {"summary": "This component is implemented with a strong focus on security and reliability. It correctly validates that a product is active and in stock before adding it to the cart, preventing race conditions. It fetches the current price from the database to prevent price manipulation and provides excellent user feedback and UI event dispatching.", "dependencies": ["App\\Models\\Product", "Livewire\\Component", "resources/views/livewire/add-to-cart-button.blade.php"]}, "app/Livewire/ProductCard.php": {"summary": "This is a well-implemented and secure component that serves as a reusable product card. It handles adding products to the cart with proper stock and availability checks, and intelligently redirects to the product detail page if variants exist. It also includes a robust, authentication-gated wishlist feature. The component provides excellent user feedback through loading states and toasts, and correctly uses events to communicate with other parts of the UI.", "dependencies": ["App\\Models\\Product", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "resources/views/livewire/product-card.blade.php"]}, "app/Livewire/Product/Index.php": {"summary": "This is a powerful and flexible component for displaying a filterable and sortable list of products. It correctly uses Livewire's `WithPagination` and `#[Url]` attribute for a great user experience. The filtering and sorting logic is robust and efficiently implemented to prevent common performance issues.", "dependencies": ["App\\Models\\Brand", "App\\Models\\Category", "App\\Models\\Product", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/product/index.blade.php"]}, "app/Livewire/Product/Show.php": {"summary": "This component effectively manages the product detail page. It cleanly separates concerns by using a dedicated `ViewedProductsService` to track user history and display recently viewed items. It also efficiently loads related products, enhancing product discovery.", "dependencies": ["App\\Models\\Product", "App\\Services\\ViewedProductsService", "Livewire\\Component", "resources/views/livewire/product/show.blade.php"]}, "app/Livewire/Product/Options.php": {"summary": "This is a complex but well-implemented component for selecting product variants. It features robust validation for authentication, variant availability, and stock levels. It correctly uses a dedicated CartService, provides clear user feedback, and includes wishlist functionality, creating a comprehensive and secure user experience.", "dependencies": ["App\\Models\\Product", "App\\Models\\ProductVariant", "App\\Services\\Cart\\CartService", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "resources/views/livewire/product/options.blade.php"]}, "app/Livewire/Product/Tabs.php": {"summary": "This is a well-implemented component for managing tabbed content on a product detail page. It includes a secure and validated form for submitting reviews, which correctly requires user authentication. It also efficiently handles the display of paginated reviews and provides excellent user feedback.", "dependencies": ["App\\Models\\Product", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/product/tabs.blade.php"]}, "app/Livewire/Product/Gallery.php": {"summary": "This is a clean and straightforward component for managing a product image gallery. It correctly loads all gallery images, sets a default selected image, and allows the user to switch between them. The implementation is simple and effective.", "dependencies": ["App\\Models\\Product", "Livewire\\Component", "resources/views/livewire/product/gallery.blade.php"]}, "app/Livewire/Products/Index.php": {"summary": "This is a high-quality and performant component for displaying product listings. It effectively uses Livewire's URL-bound properties for a clean user experience with filtering and sorting. It implements performance best practices by eager-loading relationships to prevent N+1 query problems, demonstrating a solid understanding of database optimization. The code is secure and well-organized.", "dependencies": ["App\\Models\\Category", "App\\Models\\Product", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/products/index.blade.php"]}, "app/Livewire/Vendor/Dashboard.php": {"summary": "This is a well-architected and performant dashboard component for vendors. It securely scopes all data to the authenticated user's vendor profile. It makes excellent use of database-level aggregations and eager loading to efficiently calculate and display key metrics like sales, orders, and product performance, demonstrating strong adherence to performance best practices.", "dependencies": ["App\\Models\\Order", "Carbon\\Carbon", "Illuminate\\Support\\Facades\\Auth", "Illuminate\\Support\\Facades\\DB", "Livewire\\Component", "resources/views/livewire/vendor/dashboard.blade.php"]}, "app/Livewire/Product/CategoryProducts.php": {"summary": "This is a well-structured component for displaying a filterable list of products within a specific category. It correctly uses Livewire's `WithPagination` and `queryString` property for a great user experience. The filtering logic is robust, and it efficiently loads products and subcategories.", "dependencies": ["App\\Models\\Category", "App\\Models\\Product", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/product/category-products.blade.php"]}, "app/Livewire/Vendor/Products/Index.php": {"summary": "This is a secure and feature-rich component for vendor product management. It correctly enforces vendor data isolation, ensuring a vendor can only access their own products. The component provides comprehensive functionality, including searching, filtering, sorting, and bulk actions, all while following best practices for performance and code quality.", "dependencies": ["App\\Models\\Product", "Illuminate\\Support\\Facades\\Auth", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/vendor/products/index.blade.php"]}, "app/Livewire/Product/Search.php": {"summary": "This component provides a comprehensive search functionality. It correctly uses parameterized queries to prevent SQL injection. While it includes a redundant manual sanitization method, the core implementation is secure. It effectively uses pagination and URL state management for a good user experience.", "dependencies": ["App\\Models\\Product", "Livewire\\Component", "Livewire\\WithPagination", "resources/views/livewire/product/search.blade.php"]}, "app/Livewire/Vendor/Products/ProductForm.php": {"summary": "This is a powerful and well-engineered reusable form component for managing product data. It securely handles all database operations within a transaction, ensuring data integrity. The component manages complex relationships, including variants, specifications, and media, and correctly scopes all actions to the authenticated vendor. The architecture promotes code reuse and consistency.", "dependencies": ["App\\Models\\Product", "App\\Models\\Variant", "App\\Models\\Specification", "Illuminate\\Support\\Str", "Illuminate\\Support\\Facades\\DB", "Livewire\\Component", "Livewire\\WithFileUploads", "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media", "resources/views/livewire/vendor/products/product-form.blade.php"]}, "app/Livewire/Vendor/Products/CreateProduct.php": {"summary": "This component securely and effectively handles the creation of new products. It correctly extends the base ProductForm and provides the necessary validation rules, demonstrating a well-architected, reusable design. The component includes proper authorization and securely scopes all operations to the authenticated vendor.", "dependencies": ["App\\Livewire\\Shared\\ProductForm", "App\\Models\\Brand", "App\\Models\\Category", "App\\Models\\Color", "App\\Models\\Product", "App\\Models\\Size", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\Auth", "Illuminate\\Support\\Str", "resources/views/livewire/vendor/products/create-product.blade.php"]}, "app/Livewire/Vendor/Products/EditProduct.php": {"summary": "This is a secure and robust component for editing existing products. It correctly implements authorization to ensure vendors can only edit their own products, using both manual checks and Laravel's policy system. The component demonstrates a sophisticated understanding of Livewire's lifecycle by proactively handling potential model hydration issues, ensuring data integrity. By extending the base ProductForm, it adheres to the DRY principle and maintains a consistent, reusable architecture.", "dependencies": ["App\\Livewire\\Shared\\ProductForm", "App\\Models\\Brand", "App\\Models\\Category", "App\\Models\\Color", "App\\Models\\Product", "App\\Models\\Size", "App\\Models\\Vendor", "Illuminate\\Support\\Facades\\Auth", "Illuminate\\Support\\Str", "resources/views/livewire/vendor/products/edit-product.blade.php"]}}