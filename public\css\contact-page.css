/* Custom styles for the Contact Us page */

.contact-icon-wrapper {
    width: 48px;
    height: 48px;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.contact-info-item:hover .contact-icon-wrapper {
    transform: scale(1.1);
    background-color: #e9ecef !important; /* A slightly darker shade for hover */
}

.social-icon {
    width: 40px;
    height: 40px;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-4px);
    background-color: #343a40 !important; /* A slightly darker shade for hover */
}

.faq-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faq-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,.1) !important;
}

.accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #212529;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.25);
}
