<?php

namespace App\Livewire\Admin;

use App\Models\Brand;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;

class BrandEditForm extends Component
{
    use WithFileUploads;

    public Brand $brand;

    // Form fields
    public $name;
    public $description;
    public $is_active;
    public $is_featured;
    public $logo;

    // UI state
    public $saving = false;

    public function mount(Brand $brand)
    {
        $this->brand = $brand;

        // Populate form fields
        $this->name = $brand->name;
        $this->description = $brand->description ?? '';
        $this->is_active = $brand->is_active;
        $this->is_featured = $brand->is_featured;
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:brands,name,' . $this->brand->id,
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Brand name is required.',
            'name.unique' => 'This brand name is already taken.',
            'name.max' => 'Brand name cannot exceed 255 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'logo.image' => 'Logo must be an image file.',
            'logo.mimes' => 'Logo must be a JPG, JPEG, or PNG file.',
            'logo.max' => 'Logo file size cannot exceed 2MB.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            $this->brand->update([
                'name' => $this->name,
                'slug' => Str::slug($this->name),
                'description' => $this->description,
                'is_active' => $this->is_active,
                'is_featured' => $this->is_featured,
            ]);

            // Handle logo upload
            if ($this->logo) {
                $this->brand->clearMediaCollection('logo');
                $this->brand->addMedia($this->logo->getRealPath())
                    ->usingName($this->brand->name . ' Logo')
                    ->usingFileName($this->logo->getClientOriginalName())
                    ->toMediaCollection('logo');
            }

            $this->dispatch('brand-updated');
            session()->flash('success', 'Brand updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update brand: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.admin.brand-edit-form');
    }
}
