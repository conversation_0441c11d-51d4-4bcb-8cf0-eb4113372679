<?php

namespace App\Livewire\Admin\SubscriptionPlans;

use App\Models\SubscriptionPlan;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class SubscriptionPlanForm extends Component
{
    public SubscriptionPlan $subscriptionPlan;
    
    // Form fields
    public $name = '';
    public $description = '';
    public $price = '';
    public $interval = 'monthly';
    public $product_limit = '';
    public $order_limit = '';
    public $storage_limit = '';
    public $status = 'active';
    public $features = [];
    public $newFeature = '';

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'required|string',
        'price' => 'required|numeric|min:0',
        'interval' => 'required|in:monthly,yearly',
        'product_limit' => 'nullable|integer|min:1',
        'order_limit' => 'nullable|integer|min:1',
        'storage_limit' => 'nullable|integer|min:1',
        'status' => 'required|in:active,inactive',
        'features' => 'array',
        'features.*' => 'string|max:255',
    ];

    protected $messages = [
        'name.required' => 'Plan name is required.',
        'description.required' => 'Plan description is required.',
        'price.required' => 'Plan price is required.',
        'price.numeric' => 'Plan price must be a valid number.',
        'price.min' => 'Plan price cannot be negative.',
        'interval.required' => 'Billing interval is required.',
        'interval.in' => 'Billing interval must be monthly or yearly.',
        'product_limit.integer' => 'Product limit must be a whole number.',
        'order_limit.integer' => 'Order limit must be a whole number.',
        'storage_limit.integer' => 'Storage limit must be a whole number.',
        'status.required' => 'Plan status is required.',
        'status.in' => 'Plan status must be active or inactive.',
    ];

    public function mount(SubscriptionPlan $subscriptionPlan = null)
    {
        if ($subscriptionPlan->exists) {
            $this->subscriptionPlan = $subscriptionPlan;
            $this->name = $subscriptionPlan->name;
            $this->description = $subscriptionPlan->description;
            $this->price = $subscriptionPlan->price;
            $this->interval = $subscriptionPlan->interval;
            $this->product_limit = $subscriptionPlan->product_limit;
            $this->order_limit = $subscriptionPlan->order_limit;
            $this->storage_limit = $subscriptionPlan->storage_limit;
            $this->status = $subscriptionPlan->status;
            $this->features = $subscriptionPlan->features ?? [];
        } else {
            $this->subscriptionPlan = new SubscriptionPlan();
        }
    }

    public function addFeature()
    {
        if (!empty($this->newFeature)) {
            $this->features[] = $this->newFeature;
            $this->newFeature = '';
        }
    }

    public function removeFeature($index)
    {
        unset($this->features[$index]);
        $this->features = array_values($this->features);
    }

    public function save()
    {
        if ($this->subscriptionPlan->exists) {
            $this->authorize('update', $this->subscriptionPlan);
        } else {
            $this->authorize('create', SubscriptionPlan::class);
        }

        $this->validate();

        $data = [
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'interval' => $this->interval,
            'product_limit' => $this->product_limit,
            'order_limit' => $this->order_limit,
            'storage_limit' => $this->storage_limit,
            'status' => $this->status,
            'features' => $this->features,
        ];

        if ($this->subscriptionPlan->exists) {
            $this->subscriptionPlan->update($data);
            session()->flash('success', 'Subscription plan updated successfully.');
        } else {
            SubscriptionPlan::create($data);
            session()->flash('success', 'Subscription plan created successfully.');
        }

        return redirect()->route('admin.subscription-plans.index');
    }

    public function render()
    {
        return view('livewire.admin.subscription-plans.subscription-plan-form');
    }
}
