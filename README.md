# Brandifyng - Multi-Vendor E-commerce Platform

Brandifyng is a modern, full-featured multi-vendor e-commerce platform built on the Laravel framework. It provides a seamless and intuitive experience for customers, vendors, and administrators, with a strong emphasis on the fashion niche.

## Key Features

- **Multi-Vendor Architecture**: Allows multiple vendors to register, manage their own storefronts, and sell products.
- **Dynamic Homepage**: Features admin-editable sections for "Trending Now," "Fresh on the Runway," and "Featured Designers."
- **Vendor Storefronts**: Each vendor has a dedicated mini-shop page with their branding, products, and information.
- **Admin Panel**: A comprehensive dashboard for managing users, vendors, products, orders, and site content.
- **Responsive Design**: A mobile-first approach ensures a great user experience on all devices.
- **Secure & Performant**: Built with security best practices and optimized for performance with extensive caching.

## Tech Stack

- **Backend**: Laravel 12.x, PHP 8.2+
- **Frontend**: Blade, Tailwind CSS, Alpine.js
- **Core Components**: Livewire v3
- **Database**: MySQL / MariaDB

## Getting Started

### Prerequisites

- PHP 8.2 or higher
- Composer
- Node.js & npm
- A database server (e.g., MySQL)

### Installation

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-repo/brandifyng.git
    cd brandifyng
    ```

2.  **Install dependencies:**
    ```bash
    composer install
    npm install && npm run build
    ```

3.  **Set up your environment:**
    - Copy the `.env.example` file to `.env`:
      ```bash
      cp .env.example .env
      ```
    - Generate an application key:
      ```bash
      php artisan key:generate
      ```
    - Configure your database credentials and other settings in the `.env` file.

4.  **Run database migrations and seeders:**
    ```bash
    php artisan migrate --seed
    ```
    *This will create the necessary tables and populate the database with sample data, including users, vendors, and products.*

5.  **Set up storage link:**
    ```bash
    php artisan storage:link
    ```

6.  **Run the development server:**
    ```bash
    php artisan serve
    ```

## New Features

### Admin-Editable Best Sellers ("Trending Now")

Administrators can now dynamically manage which products appear on the homepage's "Trending Now" section.

- **Location**: `Admin Panel > Products > Best Sellers`
- **Functionality**: Search for products and toggle their `is_best_seller` status with a single click.

### Enhanced Vendor Storefronts

Each vendor's storefront has been improved to function as a mini-shop, enhancing brand presence.

- **Dynamic Banners**: Vendors can upload their own banners, which are also featured in the homepage slider.
- **Improved Layout**: The storefront page now has a more engaging and brand-focused layout.

### Performance Optimizations

- **Homepage Caching**: All major data queries on the homepage are cached to ensure fast load times.
- **Livewire Optimizations**: Livewire components have been refactored to use cached computed properties, reducing unnecessary database queries.

## Contributing

Please feel free to submit pull requests or open issues to improve the platform.
