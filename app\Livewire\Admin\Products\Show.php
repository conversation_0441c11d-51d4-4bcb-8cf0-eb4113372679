<?php

namespace App\Livewire\Admin\Products;

use App\Models\Product;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Show extends Component
{
    public Product $product;

    public function mount(Product $product)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!auth()->check() || !auth()->user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // Load the product with all necessary relationships
        $this->product = $product->load([
            'vendor.user',
            'category',
            'brand',
            'variants',
            'specifications',
            'reviews.user'
        ]);
    }

    public function toggleFeatured()
    {
        $this->product->is_featured = !$this->product->is_featured;
        $this->product->save();
        
        $status = $this->product->is_featured ? 'featured' : 'unfeatured';
        session()->flash('success', "Product has been {$status} successfully.");
    }

    public function toggleBestSeller()
    {
        $this->product->is_best_seller = !$this->product->is_best_seller;
        $this->product->save();
        
        $status = $this->product->is_best_seller ? 'marked as best seller' : 'removed from best sellers';
        session()->flash('success', "Product has been {$status} successfully.");
    }

    public function toggleActive()
    {
        $this->product->is_active = !$this->product->is_active;
        $this->product->save();
        
        $status = $this->product->is_active ? 'activated' : 'deactivated';
        session()->flash('success', "Product has been {$status} successfully.");
    }

    public function render()
    {
        return view('livewire.admin.products.show');
    }
}
