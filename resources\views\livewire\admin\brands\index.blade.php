<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-3 sm:space-y-0">
            <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage Brands</h1>
            <button wire:click="create" class="w-full sm:w-auto bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                Add New Brand
            </button>
        </div>

        <!-- Mobile Card View (Hidden on Desktop) -->
        <div class="block lg:hidden space-y-4 mb-6">
            @forelse ($brands as $b)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden">
                    <div class="p-4 space-y-3">
                        <!-- Brand Header -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <img src="{{ $b->logo_url }}" alt="{{ $b->name }} logo" class="h-12 w-12 rounded-full object-cover border-2 border-gray-200">
                                <div>
                                    <p class="font-semibold text-gray-900 dark:text-white text-sm">{{ $b->name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $b->products_count }} products</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $b->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $b->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                <button wire:click="toggleFeatured({{ $b->id }})"
                                        class="relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $b->is_featured ? 'bg-blue-600' : 'bg-gray-200' }}">
                                    <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform {{ $b->is_featured ? 'translate-x-5' : 'translate-x-1' }}"></span>
                                </button>
                            </div>
                        </div>

                        <!-- Brand Actions -->
                        <div class="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-600">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $b->is_featured ? 'Featured Brand' : 'Standard Brand' }}
                            </span>
                            <div class="flex items-center space-x-3">
                                <button wire:click="edit({{ $b->id }})"
                                        class="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-edit mr-1"></i>
                                    Edit
                                </button>
                                <button wire:click="delete({{ $b->id }})"
                                        wire:confirm="Are you sure you want to delete {{ $b->name }}?"
                                        class="inline-flex items-center px-3 py-1.5 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <i class="fas fa-tags text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No brands found</h3>
                    <p class="text-gray-500 dark:text-gray-400">Create your first brand to get started.</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on Mobile) -->
        <div class="hidden lg:block bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Logo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Products</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Featured</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse ($brands as $b)
                            <tr wire:key="{{ $b->id }}">
                                <td class="px-6 py-4"><img src="{{ $b->logo_url }}" alt="{{ $b->name }} logo" class="h-10 w-10 rounded-full object-cover"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $b->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $b->products_count }}</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $b->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $b->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <button wire:click="toggleFeatured({{ $b->id }})"
                                            class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $b->is_featured ? 'bg-blue-600' : 'bg-gray-200' }}">
                                        <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {{ $b->is_featured ? 'translate-x-6' : 'translate-x-1' }}"></span>
                                    </button>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <button wire:click="edit({{ $b->id }})"
                                                class="text-blue-600 hover:text-blue-900 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button wire:click="delete({{ $b->id }})"
                                                wire:confirm="Are you sure you want to delete {{ $b->name }}?"
                                                class="text-red-600 hover:text-red-900 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr><td colspan="6" class="text-center py-12">No brands found.</td></tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="mt-6">{{ $brands->links() }}</div>
    </div>

    <!-- Mobile-Optimized Brand Modal -->
    @if($showModal)
        <div class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="$set('showModal', false)"></div>

                <!-- Mobile-Optimized Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-t-2xl sm:rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 w-full max-w-lg">
                    <div class="mb-4 sm:mb-6">
                        <h3 class="text-lg sm:text-xl leading-6 font-medium text-gray-900">
                            {{ $brand?->exists ? 'Edit' : 'Create' }} Brand
                        </h3>
                    </div>

                    <form wire:submit="save" class="space-y-4 sm:space-y-6">
                        <!-- Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Brand Name</label>
                            <input type="text" wire:model.defer="name"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 py-3 px-4 text-base"
                                   placeholder="Enter brand name">
                            @error('name') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea wire:model.defer="description" rows="3"
                                      class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 py-3 px-4 text-base"
                                      placeholder="Enter brand description (optional)"></textarea>
                            @error('description') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror
                        </div>

                        <!-- Logo -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Brand Logo</label>
                                <input type="file" wire:model="logo"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-3 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 border border-gray-300 rounded-lg">
                                @error('logo') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror
                            </div>
                            @if ($logo)
                                <div class="flex justify-center">
                                    <img src="{{ $logo->temporaryUrl() }}" alt="Brand logo preview" class="w-20 h-20 object-cover rounded-full border-2 border-gray-200">
                                </div>
                            @elseif($brand?->exists && $brand->logo_url)
                                <div class="flex justify-center">
                                    <img src="{{ $brand->logo_url }}" alt="{{ $brand->name }} current logo" class="w-20 h-20 object-cover rounded-full border-2 border-gray-200">
                                </div>
                            @endif
                        </div>

                        <!-- Active Toggle -->
                        <div class="flex items-center">
                            <input type="checkbox" wire:model.defer="is_active" id="is_active"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                        </div>

                        <!-- Featured Toggle -->
                        <div class="flex items-center">
                            <input type="checkbox" wire:model.defer="is_featured" id="is_featured"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">Featured</label>
                        </div>

                        <!-- Mobile-Optimized Footer -->
                        <div class="mt-6 space-y-3 sm:mt-8 sm:flex sm:flex-row-reverse sm:space-y-0 sm:space-x-3 sm:space-x-reverse">
                            <button type="submit"
                                    class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-6 py-3 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                                {{ $brand?->exists ? 'Update Brand' : 'Create Brand' }}
                            </button>
                            <button type="button" wire:click="$set('showModal', false)"
                                    class="w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-6 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
