<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing foreign key indexes for performance optimization

        // Users table
        Schema::table('users', function (Blueprint $table) {
            $table->index('role_id');
        });

        // Vendors table
        Schema::table('vendors', function (Blueprint $table) {
            $table->index('user_id');
        });

        // Categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->index('parent_id');
        });

        // Products table
        Schema::table('products', function (Blueprint $table) {
            $table->index('vendor_id');
            $table->index('category_id');
            $table->index('brand_id');
        });

        // Orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->index('user_id');
            $table->index('vendor_id');
        });

        // Order items table
        Schema::table('order_items', function (Blueprint $table) {
            $table->index('order_id');
            $table->index('product_id');
            $table->index('vendor_id');
        });

        // Commissions table
        Schema::table('commissions', function (Blueprint $table) {
            $table->index('order_id');
            $table->index('vendor_id');
        });

        // Subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->index('user_id');
            $table->index('subscription_plan_id');
        });

        // Wishlists table
        Schema::table('wishlists', function (Blueprint $table) {
            $table->index('user_id');
            $table->index('product_id');
        });

        // Payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->index('order_id');
        });

        // Withdrawals table
        Schema::table('withdrawals', function (Blueprint $table) {
            $table->index('vendor_id');
        });

        // Wishlist items table
        Schema::table('wishlist_items', function (Blueprint $table) {
            $table->index('wishlist_id');
            $table->index('product_id');
        });

        // Reviews table
        Schema::table('reviews', function (Blueprint $table) {
            $table->index('user_id');
            $table->index('product_id');
        });

        // Product variants table
        Schema::table('product_variants', function (Blueprint $table) {
            $table->index('product_id');
            $table->index('color_id');
            $table->index('size_id');
        });

        // Vendor subscriptions table
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            $table->index('vendor_id');
            $table->index('subscription_plan_id');
        });

        // Vendor transactions table
        Schema::table('vendor_transactions', function (Blueprint $table) {
            $table->index('vendor_id');
        });

        // Checkout sessions table
        Schema::table('checkout_sessions', function (Blueprint $table) {
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the indexes in reverse order

        Schema::table('checkout_sessions', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
        });

        Schema::table('vendor_transactions', function (Blueprint $table) {
            $table->dropIndex(['vendor_id']);
        });

        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            $table->dropIndex(['vendor_id']);
            $table->dropIndex(['subscription_plan_id']);
        });

        Schema::table('product_variants', function (Blueprint $table) {
            $table->dropIndex(['product_id']);
            $table->dropIndex(['color_id']);
            $table->dropIndex(['size_id']);
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['product_id']);
        });

        Schema::table('wishlist_items', function (Blueprint $table) {
            $table->dropIndex(['wishlist_id']);
            $table->dropIndex(['product_id']);
        });

        Schema::table('withdrawals', function (Blueprint $table) {
            $table->dropIndex(['vendor_id']);
        });

        Schema::table('payments', function (Blueprint $table) {
            $table->dropIndex(['order_id']);
        });

        Schema::table('wishlists', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['product_id']);
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['subscription_plan_id']);
        });

        Schema::table('commissions', function (Blueprint $table) {
            $table->dropIndex(['order_id']);
            $table->dropIndex(['vendor_id']);
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex(['order_id']);
            $table->dropIndex(['product_id']);
            $table->dropIndex(['vendor_id']);
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['vendor_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['vendor_id']);
            $table->dropIndex(['category_id']);
            $table->dropIndex(['brand_id']);
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex(['parent_id']);
        });

        Schema::table('vendors', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role_id']);
        });
    }
};
