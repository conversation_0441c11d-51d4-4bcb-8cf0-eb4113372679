<?php

namespace App\Livewire\Admin\Subscriptions;

use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public ?Subscription $editing = null;
    public $plans = [];

    public $plan_id;
    public $end_date;
    public $status;

    public $showCreateModal = false;
    public $new_plan_id;
    public $new_end_date;
    public $new_status = 'active';

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $this->plans = SubscriptionPlan::all();
    }

    public function edit(Subscription $subscription)
    {
        $this->editing = $subscription;
        $this->plan_id = $subscription->plan_id;
        $this->end_date = $subscription->end_date ? date('Y-m-d', strtotime($subscription->end_date)) : '';
        $this->status = $subscription->status;
    }

    public function save()
    {
        $this->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'end_date' => 'required|date',
            'status' => 'required|in:active,expired,canceled,pending',
        ]);

        if ($this->editing) {
            $this->editing->update([
                'plan_id' => $this->plan_id,
                'end_date' => $this->end_date,
                'status' => $this->status,
            ]);
        }

        $this->cancel();
        session()->flash('success', 'Subscription updated successfully.');
    }

    public function saveSubscription()
    {
        $this->validate([
            'new_plan_id' => 'required|exists:subscription_plans,id',
            'new_end_date' => 'required|date',
            'new_status' => 'required|in:active,expired,canceled,pending',
        ]);

        Subscription::create([
            'plan_id' => $this->new_plan_id,
            'end_date' => $this->new_end_date,
            'status' => $this->new_status,
            // You may want to add 'user_id' here if needed
        ]);

        $this->showCreateModal = false;
        $this->reset('new_plan_id', 'new_end_date', 'new_status');
        session()->flash('success', 'Subscription created successfully.');
    }

    public function cancel()
    {
        $this->reset('editing', 'plan_id', 'end_date', 'status');
        $this->editing = null;
    }

    public function render()
    {
        $subscriptions = Subscription::with(['user', 'plan'])->latest()->paginate(15);
        return view('livewire.admin.subscriptions.index', [
            'subscriptions' => $subscriptions,
        ]);
    }
}
