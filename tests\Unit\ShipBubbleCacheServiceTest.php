<?php

namespace Tests\Unit;

use App\Models\Vendor;
use App\Services\ShipBubbleCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ShipBubbleCacheServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ShipBubbleCacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cacheService = new ShipBubbleCacheService();
        Cache::flush(); // Clear cache before each test
    }

    /** @test */
    public function it_generates_consistent_cache_keys_for_shipping_rates()
    {
        $vendor = Vendor::factory()->create(['id' => 1]);
        
        $cartItems = [
            ['id' => 1, 'quantity' => 2, 'price' => 100, 'weight' => 0.5],
            ['id' => 2, 'quantity' => 1, 'price' => 50, 'weight' => 0.3],
        ];
        
        $shippingAddress = [
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];

        $key1 = $this->cacheService->generateRatesCacheKey($cartItems, $shippingAddress, $vendor);
        $key2 = $this->cacheService->generateRatesCacheKey($cartItems, $shippingAddress, $vendor);

        $this->assertEquals($key1, $key2);
        $this->assertStringContains('shipbubble_rates_1_', $key1);
    }

    /** @test */
    public function it_generates_different_cache_keys_for_different_cart_items()
    {
        $vendor = Vendor::factory()->create(['id' => 1]);
        
        $cartItems1 = [
            ['id' => 1, 'quantity' => 2, 'price' => 100, 'weight' => 0.5],
        ];
        
        $cartItems2 = [
            ['id' => 2, 'quantity' => 1, 'price' => 50, 'weight' => 0.3],
        ];
        
        $shippingAddress = [
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];

        $key1 = $this->cacheService->generateRatesCacheKey($cartItems1, $shippingAddress, $vendor);
        $key2 = $this->cacheService->generateRatesCacheKey($cartItems2, $shippingAddress, $vendor);

        $this->assertNotEquals($key1, $key2);
    }

    /** @test */
    public function it_generates_different_cache_keys_for_different_addresses()
    {
        $vendor = Vendor::factory()->create(['id' => 1]);
        
        $cartItems = [
            ['id' => 1, 'quantity' => 2, 'price' => 100, 'weight' => 0.5],
        ];
        
        $address1 = [
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];
        
        $address2 = [
            'address' => '456 Different Street',
            'city' => 'Abuja',
            'state' => 'FCT',
            'country' => 'NG',
        ];

        $key1 = $this->cacheService->generateRatesCacheKey($cartItems, $address1, $vendor);
        $key2 = $this->cacheService->generateRatesCacheKey($cartItems, $address2, $vendor);

        $this->assertNotEquals($key1, $key2);
    }

    /** @test */
    public function it_caches_and_retrieves_shipping_rates()
    {
        $cacheKey = 'test_rates_key';
        $rates = [
            'status' => 'success',
            'data' => [
                ['service' => 'Standard', 'price' => 1500],
                ['service' => 'Express', 'price' => 2500],
            ],
        ];

        // Cache the rates
        $this->cacheService->cacheShippingRates($cacheKey, $rates);

        // Retrieve the rates
        $cachedRates = $this->cacheService->getCachedShippingRates($cacheKey);

        $this->assertNotNull($cachedRates);
        $this->assertEquals($rates, $cachedRates);
    }

    /** @test */
    public function it_returns_null_for_non_existent_cache_key()
    {
        $cachedRates = $this->cacheService->getCachedShippingRates('non_existent_key');
        $this->assertNull($cachedRates);
    }

    /** @test */
    public function it_caches_and_retrieves_address_validation()
    {
        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];

        $validationResult = [
            'status' => 'success',
            'message' => 'Address validated successfully',
            'data' => ['validated' => true],
        ];

        $cacheKey = $this->cacheService->generateAddressCacheKey($addressData);

        // Cache the validation result
        $this->cacheService->cacheAddressValidation($cacheKey, $validationResult);

        // Retrieve the validation result
        $cachedResult = $this->cacheService->getCachedAddressValidation($cacheKey);

        $this->assertNotNull($cachedResult);
        $this->assertEquals($validationResult, $cachedResult);
    }

    /** @test */
    public function it_generates_consistent_address_cache_keys()
    {
        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];

        $key1 = $this->cacheService->generateAddressCacheKey($addressData);
        $key2 = $this->cacheService->generateAddressCacheKey($addressData);

        $this->assertEquals($key1, $key2);
        $this->assertStringContains('shipbubble_address_', $key1);
    }

    /** @test */
    public function it_generates_different_address_cache_keys_for_different_data()
    {
        $address1 = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'NG',
        ];

        $address2 = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '456 Different Street',
            'city' => 'Abuja',
            'state' => 'FCT',
            'country' => 'NG',
        ];

        $key1 = $this->cacheService->generateAddressCacheKey($address1);
        $key2 = $this->cacheService->generateAddressCacheKey($address2);

        $this->assertNotEquals($key1, $key2);
    }

    /** @test */
    public function it_respects_cache_ttl_settings()
    {
        $this->assertEquals(300, $this->cacheService->getRateTtl()); // 5 minutes
        $this->assertEquals(1800, $this->cacheService->getAddressTtl()); // 30 minutes
    }

    /** @test */
    public function it_checks_if_caching_is_enabled()
    {
        // Caching should be enabled by default in testing
        $this->assertTrue($this->cacheService->isCachingEnabled());
    }

    /** @test */
    public function it_provides_cache_statistics()
    {
        $stats = $this->cacheService->getCacheStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_entries', $stats);
        $this->assertArrayHasKey('rate_entries', $stats);
        $this->assertArrayHasKey('address_entries', $stats);
        $this->assertArrayHasKey('cache_prefix', $stats);
        $this->assertArrayHasKey('rate_ttl_seconds', $stats);
        $this->assertArrayHasKey('address_ttl_seconds', $stats);
    }

    /** @test */
    public function it_handles_cache_errors_gracefully()
    {
        // Mock cache to throw exception
        Cache::shouldReceive('get')
            ->andThrow(new \Exception('Cache error'));

        $cachedRates = $this->cacheService->getCachedShippingRates('test_key');
        $this->assertNull($cachedRates);

        $cachedAddress = $this->cacheService->getCachedAddressValidation('test_key');
        $this->assertNull($cachedAddress);
    }

    /** @test */
    public function it_includes_metadata_in_cached_data()
    {
        $cacheKey = 'test_rates_key';
        $rates = ['status' => 'success', 'data' => []];

        $this->cacheService->cacheShippingRates($cacheKey, $rates);

        // Get raw cached data to check metadata
        $rawCached = Cache::get($cacheKey);

        $this->assertIsArray($rawCached);
        $this->assertArrayHasKey('data', $rawCached);
        $this->assertArrayHasKey('cached_at', $rawCached);
        $this->assertArrayHasKey('expires_at', $rawCached);
        $this->assertEquals($rates, $rawCached['data']);
    }
}
