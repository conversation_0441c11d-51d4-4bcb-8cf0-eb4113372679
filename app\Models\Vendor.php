<?php
namespace App\Models;

use App\Models\VendorTransaction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\HasMedia;
use Spa<PERSON>\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Vendor extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'business_name',
        'business_description',
        'id_document',
        'business_document',
        'paystack_recipient_code',
        'paystack_subaccount_code',
        'business_address',
        'city',
        'state',
        'country',
        'country_code',
        'postal_code',
        'phone',
        'logo',
        'banner',
        'about',
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'balance',
        'total_earnings',
        'pending_balance',
        'available_balance',
        'withdrawn_amount',
        'last_balance_update',
        'is_approved',
        'is_featured',
        'status',
        'has_completed_onboarding',
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'balance' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'withdrawn_amount' => 'decimal:2',
        'last_balance_update' => 'datetime',
    ];



    /**
     * BUSINESS LOGIC FIX: Atomic method to update financial state consistently
     */
    public function updateFinancialState(array $changes): bool
    {
        return \DB::transaction(function () use ($changes) {
            // Lock the vendor record for update
            $vendor = static::where('id', $this->id)->lockForUpdate()->first();

            if (!$vendor) {
                throw new \Exception('Vendor not found for financial update');
            }

            // Validate financial changes
            $allowedFields = ['total_earnings', 'pending_balance', 'available_balance', 'withdrawn_amount'];
            $updateData = array_intersect_key($changes, array_flip($allowedFields));

            // Add timestamp
            $updateData['last_balance_update'] = now();

            // Log the financial state change
            \Log::info('Vendor financial state update', [
                'vendor_id' => $this->id,
                'changes' => $updateData,
                'previous_state' => [
                    'total_earnings' => $vendor->total_earnings,
                    'pending_balance' => $vendor->pending_balance,
                    'available_balance' => $vendor->available_balance,
                    'withdrawn_amount' => $vendor->withdrawn_amount,
                ],
                'timestamp' => now()
            ]);

            // Update the vendor
            $result = $vendor->update($updateData);

            // Refresh the current model instance
            $this->refresh();

            return $result;
        });
    }

    /**
     * BUSINESS LOGIC FIX: Atomic method to add earnings
     */
    public function addEarnings(float $amount, string $source = 'order'): bool
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Earnings amount must be positive');
        }

        return $this->updateFinancialState([
            'total_earnings' => \DB::raw("total_earnings + {$amount}"),
            'pending_balance' => \DB::raw("pending_balance + {$amount}")
        ]);
    }

    /**
     * BUSINESS LOGIC FIX: Atomic method to process withdrawal
     */
    public function processWithdrawal(float $amount): bool
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Withdrawal amount must be positive');
        }

        if ($amount > $this->available_balance) {
            throw new \InvalidArgumentException('Insufficient available balance');
        }

        return $this->updateFinancialState([
            'available_balance' => \DB::raw("available_balance - {$amount}"),
            'withdrawn_amount' => \DB::raw("withdrawn_amount + {$amount}")
        ]);
    }

    /**
     * BUSINESS LOGIC FIX: Atomic method to move pending to available balance
     */
    public function releasePendingBalance(float $amount): bool
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Release amount must be positive');
        }

        if ($amount > $this->pending_balance) {
            throw new \InvalidArgumentException('Insufficient pending balance');
        }

        return $this->updateFinancialState([
            'pending_balance' => \DB::raw("pending_balance - {$amount}"),
            'available_balance' => \DB::raw("available_balance + {$amount}")
        ]);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($vendor) {
            \Log::info('Vendor Creating', [
                'user_id' => $vendor->user_id,
                'business_name' => $vendor->business_name,
                'shop_name' => $vendor->shop_name,
                'timestamp' => now()->toISOString()
            ]);
        });

        static::created(function ($vendor) {
            \Log::info('Vendor Created Successfully', [
                'vendor_id' => $vendor->id,
                'user_id' => $vendor->user_id,
                'business_name' => $vendor->business_name,
                'shop_name' => $vendor->shop_name,
                'is_approved' => $vendor->is_approved
            ]);

            // Automatically create a brand for this vendor
            $vendor->brand()->create([
                'name' => $vendor->shop_name,
                'slug' => $vendor->slug,
                'description' => "Official brand for {$vendor->shop_name}",
                'is_active' => true,
                'is_featured' => $vendor->is_featured ?? false,
            ]);
        });

        static::updating(function ($vendor) {
            \Log::info('Vendor Updating', [
                'vendor_id' => $vendor->id,
                'old_is_approved' => $vendor->getOriginal('is_approved'),
                'new_is_approved' => $vendor->is_approved,
                'old_total_earnings' => $vendor->getOriginal('total_earnings'),
                'new_total_earnings' => $vendor->total_earnings,
                'old_available_balance' => $vendor->getOriginal('available_balance'),
                'new_available_balance' => $vendor->available_balance
            ]);
        });

        static::updated(function ($vendor) {
            \Log::info('Vendor Updated Successfully', [
                'vendor_id' => $vendor->id,
                'business_name' => $vendor->business_name,
                'is_approved' => $vendor->is_approved,
                'total_earnings' => $vendor->total_earnings,
                'available_balance' => $vendor->available_balance
            ]);

            // Update the associated brand when vendor is updated
            if ($vendor->brand) {
                $vendor->brand->update([
                    'name' => $vendor->shop_name,
                    'slug' => $vendor->slug,
                    'is_featured' => $vendor->is_featured ?? false,
                ]);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function transactions()
    {
        return $this->hasMany(VendorTransaction::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }
    
    public function subscription()
    {
        return $this->hasOne(VendorSubscription::class)->latestOfMany()->with('plan');
    }

    public function subscriptions()
    {
        return $this->hasMany(VendorSubscription::class);
    }

    public function brand()
    {
        return $this->hasOne(Brand::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Register media collections for the vendor.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();

        $this->addMediaCollection('banner')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();

        // FIXED: Add document collections for standardized file handling
        $this->addMediaCollection('id_documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'])
            ->singleFile();

        $this->addMediaCollection('business_documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'])
            ->singleFile();
    }

    /**
     * Register media conversions for the vendor.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('logo');
    }

    /**
     * Get the vendor's logo URL.
     *
     * @return string
     */
    public function getLogoUrlAttribute(): string
    {
        // First try to get from media library
        $media = $this->getFirstMedia('logo');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback to database logo if exists
        if (!empty($this->attributes['logo'])) {
            $fileUploadService = app(\App\Services\FileUploadService::class);
            return $fileUploadService->getFileUrl(
                $this->attributes['logo'],
                'public',
                asset('images/default-vendor.png')
            );
        }

        // Final fallback
        return asset('images/default-vendor.svg');
    }

    /**
     * Get the vendor's banner URL.
     *
     * @return string
     */
    public function getBannerUrlAttribute(): string
    {
        // First try to get from media library
        $media = $this->getFirstMedia('banner');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback to database banner if exists
        if (!empty($this->attributes['banner'])) {
            $fileUploadService = app(\App\Services\FileUploadService::class);
            return $fileUploadService->getFileUrl(
                $this->attributes['banner'],
                'public',
                asset('images/default-banner.jpg')
            );
        }

        // Final fallback
        return asset('images/default-banner.jpg');
    }
}
