<?php

namespace App\Livewire\Checkout;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')] // CRITICAL FIX NA1/AL1: Use traditional layout
class Success extends Component
{
    public $orders;
    public $transaction_id;

    public function mount($transaction_id = null)
    {
        $this->transaction_id = $transaction_id ?? request()->query('transaction_id');

        \Log::info('Checkout success page accessed', [
            'transaction_id' => $this->transaction_id,
            'user_id' => Auth::id(),
            'url_params' => request()->all()
        ]);

        if (!$this->transaction_id) {
            \Log::warning('Checkout success: No transaction ID provided', [
                'user_id' => Auth::id(),
                'request_data' => request()->all()
            ]);
            session()->flash('error', 'Transaction ID not found. Please check your order history.');
            return redirect()->route('dashboard');
        }

        $this->orders = Order::where('checkout_transaction_id', $this->transaction_id)
            ->where('user_id', Auth::id())
            ->with('vendor', 'items.product')
            ->get();

        \Log::info('Orders found for transaction', [
            'transaction_id' => $this->transaction_id,
            'orders_count' => $this->orders->count(),
            'user_id' => Auth::id()
        ]);

        if ($this->orders->isEmpty()) {
            \Log::warning('Checkout success: No orders found', [
                'transaction_id' => $this->transaction_id,
                'user_id' => Auth::id()
            ]);
            session()->flash('error', 'Order not found. Please check your order history or contact support.');
            return redirect()->route('dashboard');
        }
    }

    public function render()
    {
        return view('livewire.checkout.success');
    }
}
