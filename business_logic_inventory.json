{"business_logic_inventory": {"form_to_backend_flows": {"product_management": {"admin_product_form": {"component": "app/Livewire/Admin/Products/ProductForm.php", "view": "resources/views/livewire/admin/products/product-form.blade.php", "validation_rules": {"name": "required|string|max:255", "slug": "required|string|max:255|unique:products,slug", "vendor_id": "required|exists:vendors,id", "category_id": "required|exists:categories,id", "description": "required|string", "price": "required|numeric|min:0", "discount_price": "nullable|numeric|min:0|lt:price", "image_url": "nullable|url", "stock": "required|integer|min:0", "is_active": "boolean"}, "model_fields": {"fillable": ["vendor_id", "category_id", "brand_id", "name", "slug", "description", "price", "discount_price", "stock", "sku", "image_url", "weight", "height", "width", "length", "is_active"], "guarded": ["id", "is_featured", "is_best_seller", "created_at", "updated_at", "deleted_at"]}, "data_transformations": ["slug_generation_from_name", "price_decimal_conversion", "stock_integer_validation"]}, "vendor_product_form": {"component": "app/Livewire/Vendor/Products/ProductForm.php", "controller": "app/Http/Controllers/Vendor/ProductController.php", "validation_rules": {"name": "required|string|max:255", "category_id": "required|exists:categories,id", "description": "required|string", "price": "required|numeric|min:0", "discount_price": "nullable|numeric|min:0|lt:price", "stock": "required|integer|min:0", "image": "nullable|image|max:2048", "is_active": "sometimes|boolean", "weight": "nullable|numeric|min:0", "sku": "nullable|string|max:255", "variants": "nullable|array"}, "inconsistencies": ["admin_form_has_image_url_field_vendor_form_has_image_file", "admin_form_requires_vendor_id_vendor_form_auto_assigns"]}, "shared_product_form": {"component": "app/Livewire/Shared/ProductForm.php", "validation_rules": {"product.name": "required|string|max:255", "product.description": "required|string", "product.price": "required|numeric|min:0", "product.discount_price": "nullable|numeric|min:0|lt:product.price", "product.stock": "required|integer|min:0", "product.category_id": "required|exists:categories,id", "product.brand_id": "nullable|exists:brands,id", "product.is_active": "sometimes|boolean", "product.weight": "nullable|numeric|min:0"}, "field_prefix": "product.", "inconsistencies": ["field_naming_inconsistency_with_other_forms"]}}, "user_management": {"contact_form": {"component": "app/Livewire/ContactForm.php", "view": "resources/views/livewire/contact-form.blade.php", "validation_rules": {"name": "required|string|max:255", "email": "required|email|max:255", "subject": "required|string|max:255", "message": "required|string|min:10"}, "backend_processing": "mail_sending_to_admin", "error_handling": "try_catch_with_logging"}, "vendor_onboarding": {"component": "app/Livewire/Vendor/Onboarding/Index.php", "properties": ["vendor", "step", "business_name", "phone", "business_address", "business_description", "id_document", "business_document", "bank_name", "bank_account_name", "bank_account_number"], "methods": ["saveBusinessInfo", "saveDocuments", "savePayoutInfo"], "multi_step_validation": true}, "admin_vendor_form": {"view": "resources/views/livewire/admin/vendors/vendor-form.blade.php", "fields": ["name", "email", "business_name", "phone", "business_address"], "validation_location": "unknown_needs_investigation"}}, "subscription_management": {"subscription_plan_form": {"view": "resources/views/livewire/admin/subscription-plans/subscription-plan-form.blade.php", "validation_location": "unknown_needs_investigation", "fields": ["name", "price", "duration", "features"]}}}, "business_rules": {"commission_calculation": {"rate": "2.7%", "service": "app/Services/CommissionService.php", "enforcement_points": ["order_creation", "vendor_earnings_calculation", "admin_reports"], "validation_method": "calculateCommission", "rounding": "round_to_2_decimals"}, "inventory_management": {"stock_field": "stock", "deprecated_field": "quantity", "validation_rules": "required|integer|min:0", "enforcement_points": ["product_creation", "product_update", "cart_addition", "checkout_validation", "order_processing"], "race_condition_prevention": "database_locking"}, "pricing_logic": {"base_price": "required|numeric|min:0", "discount_price": "nullable|numeric|min:0|lt:price", "currency": "NGN", "display_formatting": "number_format_with_naira_symbol", "calculation_precision": "2_decimal_places"}, "vendor_approval": {"status_field": "is_approved", "workflow": ["pending", "approved", "rejected"], "enforcement_points": ["vendor_dashboard_access", "product_creation", "order_processing", "earnings_calculation"]}, "user_permissions": {"roles": ["admin", "vendor", "customer"], "role_validation": "Role::ALLOWED_ROLES", "middleware": ["admin", "vendor", "approved.vendor"], "enforcement_points": ["route_access", "livewire_components", "api_endpoints"]}}, "data_transformation_points": {"currency_handling": {"input_format": "decimal_string", "storage_format": "decimal_2_places", "display_format": "formatted_with_naira", "api_format": "kobo_for_paystack"}, "date_time_handling": {"user_timezone": "application_default", "database_storage": "UTC", "api_formats": "ISO_8601"}, "address_normalization": {"input_sources": ["checkout_form", "vendor_onboarding"], "storage_format": "separate_fields", "api_integration": "shipbubble_format"}, "product_variants": {"structure": "separate_table", "selection_logic": "color_size_combination", "stock_management": "per_variant", "pricing": "variant_specific_or_inherited"}}, "validation_consistency_matrix": {"product_forms": {"admin_form": {"name": "required|string|max:255", "description": "required|string|min:10", "price": "required|numeric|min:0|max:999999.99", "stock": "required|integer|min:0|max:999999", "validation_source": "ProductValidationRules trait"}, "vendor_controller": {"name": "required|string|max:255", "description": "required|string|min:10", "price": "required|numeric|min:0|max:999999.99", "stock": "required|integer|min:0|max:999999", "validation_source": "ProductValidationRules trait"}, "shared_form": {"product.name": "required|string|max:255", "product.description": "required|string|min:10", "product.price": "required|numeric|min:0|max:999999.99", "product.stock": "required|integer|min:0|max:999999", "validation_source": "ProductValidationRules trait with prefix"}, "resolution_status": "COMPLETED", "trait_implementation": {"trait_file": "app/Traits/ProductValidationRules.php", "methods": ["getProductValidationRules(isUpdate, requireVendor)", "getPrefixedProductValidationRules(prefix, isUpdate, requireVendor)", "getProductValidationMessages(prefix)", "getProductValidationAttributes(prefix)"], "features": ["support_for_prefixed_fields_like_product_dot_name", "separate_image_validation_for_url_vs_file_upload", "comprehensive_error_messages_and_attributes", "update_vs_create_validation_rule_variations"]}, "implementation_details": ["RESOLVED: All forms now use ProductValidationRules trait", "RESOLVED: Consistent validation rules across all contexts", "RESOLVED: Standardized error messages and attributes", "RESOLVED: Admin forms use getProductValidationRules with requireVendor=true", "RESOLVED: Shared forms use getPrefixedProductValidationRules with product prefix", "RESOLVED: Vendor controllers use getProductValidationRules with requireVendor=false"]}, "checkout_validation": {"livewire_component": {"first_name": "required|string|max:255", "last_name": "required|string|max:255", "email": "required|email|max:255", "phone": "required|string|regex:/^0[789][01]\\d{8}$/", "payment_method": "required|in:paystack,bank_transfer,cash_on_delivery", "shippingAddress.address": "required|string|min:10|max:255", "shippingAddress.city": "required|string|min:2|max:255", "shippingAddress.state": "required|string", "shippingAddress.lga": "required|string|min:2", "shippingAddress.phone": "required|string|regex:/^0[789][01]\\d{8}$/", "validation_source": "CheckoutValidationRules trait"}, "payment_controller": {"first_name": "required|string|max:255", "last_name": "required|string|max:255", "email": "required|email", "phone": "required|string|regex:/^0[789][01]\\d{8}$/", "payment_method": "required|in:paystack,bank_transfer,cash_on_delivery"}, "resolution_status": "COMPLETED", "trait_implementation": {"trait_file": "app/Traits/CheckoutValidationRules.php", "methods": ["getCheckoutValidationRules(requirePaymentMethod, requireShippingOptions)", "getPrefixedCheckoutValidationRules(prefix, requirePaymentMethod, requireShippingOptions)", "getCheckoutValidationMessages(prefix)", "getCheckoutValidationAttributes(prefix)"], "features": ["support_for_prefixed_shipping_address_fields", "nigerian_phone_number_validation_regex", "payment_method_validation_with_allowed_values", "comprehensive_error_messages_for_checkout_flow"]}, "implementation_details": ["RESOLVED: Added missing personal info validation in Livewire component", "RESOLVED: Added missing payment method validation in Livewire component", "RESOLVED: Real-time validation implemented with validateOnly() in updated() method", "RESOLVED: Consistent validation rules between Livewire and PaymentController", "RESOLVED: Added first_name, last_name, email, phone validation to Livewire", "RESOLVED: Enhanced user experience with immediate validation feedback"]}, "user_forms": {"user_model": {"fillable": ["name", "email", "password", "email_verified_at", "phone", "address", "city", "state", "country"], "guarded": ["role_id", "remember_token", "id"]}, "contact_form": {"name": "required|string|max:255", "email": "required|email|max:255"}, "vendor_onboarding": {"business_name": "validation_unknown", "phone": "validation_unknown", "business_address": "validation_unknown"}}, "authorization_enforcement": {"vendor_authorization": {"trait_file": "app/Traits/RequiresVendorAuthorization.php", "implementation": "automatic_enforcement_via_boot_method", "validation_checks": ["user_authenticated", "user_has_vendor_role", "vendor_profile_exists", "vendor_is_approved_if_required", "vendor_has_active_subscription_if_required"], "methods": ["bootRequiresVendorAuthorization() - automatic enforcement", "enforceVendorAuthorization(requireApproval, requireSubscription)", "isAuthorizedVendor(requireApproval, requireSubscription)", "getAuthorizedVendor(requireApproval, requireSubscription)", "vendorOwnsResource(resource) - ownership validation", "getVendorAuthorizationStatus() - status reporting"], "security_features": ["comprehensive_vendor_status_validation", "resource_ownership_verification", "security_logging_for_authorization_attempts", "flexible_approval_and_subscription_requirements"], "components_using_trait": ["app/Livewire/Vendor/Dashboard.php"]}, "admin_authorization": {"trait_file": "app/Traits/RequiresAdminAuthorization.php", "implementation": "automatic_enforcement_via_boot_method", "validation_checks": ["user_authenticated", "user_has_admin_role", "admin_account_not_suspended_if_applicable"], "methods": ["bootRequiresAdminAuthorization() - automatic enforcement", "enforceAdminAuthorization()", "isAuthorizedAdmin()", "getAuthorizedAdmin()", "getAdminAuthorizationStatus() - status reporting"], "security_features": ["admin_role_validation", "security_logging_for_admin_actions", "consistent_authorization_across_admin_components"]}}, "cart_stock_validation": {"standardized_method": "Product::hasSufficientStock(quantity)", "implementation_locations": ["app/Livewire/AddToCartButton.php - add to cart validation", "app/Livewire/ProductCard.php - cart addition validation", "app/Livewire/Cart/Index.php - quantity increment/update validation"], "validation_flow": ["extract_product_id_from_cart_key", "fetch_product_model_from_database", "calculate_requested_quantity", "validate_using_hasSufficientStock_method", "show_user_friendly_error_if_insufficient", "update_cart_session_only_if_valid"], "error_handling": {"insufficient_stock": "toast_notification_with_available_stock_count", "product_not_found": "toast_notification_to_refresh_cart", "user_feedback": "immediate_feedback_without_page_reload"}, "helper_methods": {"extractProductIdFromCartKey": {"purpose": "parse_various_cart_key_formats", "supported_formats": ["product_123", "product_123_variant_456", "legacy_numeric"], "implementation": "regex_pattern_matching_with_fallbacks"}}}}}}