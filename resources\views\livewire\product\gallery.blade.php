<div class="flex flex-col space-y-4 sm:space-y-6">
    {{-- Main Product Image --}}
    <div class="relative w-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl sm:rounded-2xl overflow-hidden shadow-lg group">
        <img src="{{ $selectedImage['url'] ?? asset('images/product-placeholder.svg') }}"
             alt="{{ $selectedImage['alt'] ?? $product->name }}"
             class="w-full h-full object-center object-cover aspect-square transition-transform duration-500 hover:scale-105"
             onerror="this.onerror=null; this.src='{{ asset('images/product-placeholder.svg') }}'; this.classList.add('opacity-80');">

        {{-- Sale Badge --}}
        @if($product->isOnSale())
            <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                <span class="flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    SALE
                </span>
            </div>
        @endif

        {{-- Zoom Icon --}}
        <div class="absolute top-4 right-4 bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <i class="fas fa-search-plus text-gray-600"></i>
        </div>
    </div>

    {{-- Thumbnail Gallery (only show if multiple images) --}}
    @if($images->count() > 1)
        <div class="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-3">
            @foreach($images as $image)
                <div wire:click="selectImage('{{ $image['url'] }}')"
                     class="group cursor-pointer rounded-lg sm:rounded-xl overflow-hidden border-2 transition-all duration-300 {{ ($selectedImage['url'] ?? '') === $image['url'] ? 'border-black shadow-lg' : 'border-gray-200 hover:border-gray-400' }}">
                    <img src="{{ $image['thumb'] ?? $image['url'] }}"
                         alt="{{ $image['alt'] ?? $product->name }} thumbnail"
                         class="w-full h-full object-center object-cover aspect-square transition-transform duration-300 group-hover:scale-110"
                         onerror="this.onerror=null; this.src='{{ asset('images/product-placeholder.svg') }}';">
                </div>
            @endforeach
        </div>
    @endif
</div>
