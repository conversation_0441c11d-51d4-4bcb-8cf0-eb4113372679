<?php

namespace App\Console\Commands;

use App\Models\Brand;
use App\Models\Vendor;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixVendorBrandLinks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:vendor-brand-links';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix vendor-brand links using direct database updates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing vendor-brand links...');
        
        // Get all vendors
        $vendors = Vendor::all();
        
        foreach ($vendors as $vendor) {
            // Find brand with matching name
            $brand = Brand::where('name', $vendor->shop_name)->first();
            
            if ($brand) {
                // Direct database update
                DB::table('brands')
                    ->where('id', $brand->id)
                    ->update(['vendor_id' => $vendor->id]);
                
                $this->info("Linked brand '{$brand->name}' (ID: {$brand->id}) to vendor '{$vendor->shop_name}' (ID: {$vendor->id})");
            } else {
                $this->warn("No brand found for vendor '{$vendor->shop_name}'");
            }
        }
        
        // Now update products to use the correct brand_id
        $this->info("\nUpdating product brand associations...");
        
        $products = DB::table('products')
            ->join('vendors', 'products.vendor_id', '=', 'vendors.id')
            ->join('brands', 'brands.vendor_id', '=', 'vendors.id')
            ->select('products.id as product_id', 'brands.id as brand_id', 'products.name as product_name')
            ->get();
        
        foreach ($products as $product) {
            DB::table('products')
                ->where('id', $product->product_id)
                ->update(['brand_id' => $product->brand_id]);
            
            $this->info("Updated product '{$product->product_name}' to use brand ID {$product->brand_id}");
        }
        
        $this->info("\nDone!");
        
        return 0;
    }
}
