<?php

namespace App\Traits;

/**
 * Standardized product validation rules trait
 * 
 * This trait provides consistent validation rules across all product forms
 * to resolve LOGIC-CRITICAL-001: Form-to-backend validation inconsistency
 * 
 * Usage:
 * - Admin Product Forms
 * - Vendor Product Forms  
 * - Shared Product Forms
 * - Product Controllers
 */
trait ProductValidationRules
{
    /**
     * Get standardized product validation rules
     * 
     * @param bool $isUpdate Whether this is for update (makes some fields optional)
     * @param bool $requireVendor Whether vendor_id is required (admin forms)
     * @return array
     */
    protected function getProductValidationRules(bool $isUpdate = false, bool $requireVendor = false): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string|min:10',
            'price' => 'required|numeric|min:0|max:999999.99',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock' => 'required|integer|min:0|max:999999',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'is_active' => 'sometimes|boolean',
            'weight' => 'nullable|numeric|min:0|max:999.99',
            'height' => 'nullable|numeric|min:0|max:999.99',
            'width' => 'nullable|numeric|min:0|max:999.99',
            'length' => 'nullable|numeric|min:0|max:999.99',
            'sku' => 'nullable|string|max:255',
        ];

        // Add vendor requirement for admin forms
        if ($requireVendor) {
            $rules['vendor_id'] = 'required|exists:vendors,id';
        }

        // Make some fields optional for updates
        if ($isUpdate) {
            $rules['name'] = 'sometimes|required|string|max:255';
            $rules['description'] = 'sometimes|required|string|min:10';
            $rules['price'] = 'sometimes|required|numeric|min:0|max:999999.99';
            $rules['stock'] = 'sometimes|required|integer|min:0|max:999999';
            $rules['category_id'] = 'sometimes|required|exists:categories,id';
        }

        return $rules;
    }

    /**
     * Get image validation rules for product forms
     * 
     * @param string $fieldName The name of the image field
     * @param bool $required Whether image is required
     * @return array
     */
    protected function getProductImageValidationRules(string $fieldName = 'image', bool $required = false): array
    {
        $baseRules = [
            'image',
            'max:2048', // 2MB max
            'mimes:jpeg,jpg,png',
            'dimensions:max_width=2048,max_height=2048,min_width=50,min_height=50'
        ];

        if ($required) {
            array_unshift($baseRules, 'required');
        } else {
            array_unshift($baseRules, 'nullable');
        }

        return [$fieldName => implode('|', $baseRules)];
    }

    /**
     * Get image URL validation rules (for admin forms using URLs)
     * 
     * @param string $fieldName The name of the image URL field
     * @param bool $required Whether image URL is required
     * @return array
     */
    protected function getProductImageUrlValidationRules(string $fieldName = 'image_url', bool $required = false): array
    {
        $rule = $required ? 'required|url|max:255' : 'nullable|url|max:255';
        return [$fieldName => $rule];
    }

    /**
     * Get variant validation rules for products with variants
     * 
     * @return array
     */
    protected function getProductVariantValidationRules(): array
    {
        return [
            'variants' => 'nullable|array',
            'variants.*.color_id' => 'required_with:variants|exists:colors,id',
            'variants.*.size_id' => 'required_with:variants|exists:sizes,id',
            'variants.*.price' => 'nullable|numeric|min:0|max:999999.99',
            'variants.*.stock_quantity' => 'required_with:variants|integer|min:0|max:999999',
            'variants.*.sku' => 'nullable|string|max:255',
            'variants.*.is_active' => 'sometimes|boolean',
        ];
    }

    /**
     * Get validation rules for prefixed fields (e.g., 'product.name')
     * Used by shared forms that use object notation
     * 
     * @param string $prefix The field prefix (e.g., 'product')
     * @param bool $isUpdate Whether this is for update
     * @param bool $requireVendor Whether vendor_id is required
     * @return array
     */
    protected function getPrefixedProductValidationRules(string $prefix, bool $isUpdate = false, bool $requireVendor = false): array
    {
        $baseRules = $this->getProductValidationRules($isUpdate, $requireVendor);
        $prefixedRules = [];

        foreach ($baseRules as $field => $rule) {
            $prefixedRules["{$prefix}.{$field}"] = $rule;
        }

        return $prefixedRules;
    }

    /**
     * Get custom validation messages for product fields
     * 
     * @param string $prefix Optional prefix for field names
     * @return array
     */
    protected function getProductValidationMessages(string $prefix = ''): array
    {
        $fieldPrefix = $prefix ? "{$prefix}." : '';
        
        return [
            "{$fieldPrefix}name.required" => 'Product name is required.',
            "{$fieldPrefix}name.max" => 'Product name cannot exceed 255 characters.',
            "{$fieldPrefix}description.required" => 'Product description is required.',
            "{$fieldPrefix}description.min" => 'Product description must be at least 10 characters.',
            "{$fieldPrefix}price.required" => 'Product price is required.',
            "{$fieldPrefix}price.numeric" => 'Product price must be a valid number.',
            "{$fieldPrefix}price.min" => 'Product price cannot be negative.',
            "{$fieldPrefix}price.max" => 'Product price cannot exceed ₦999,999.99.',
            "{$fieldPrefix}discount_price.numeric" => 'Discount price must be a valid number.',
            "{$fieldPrefix}discount_price.min" => 'Discount price cannot be negative.',
            "{$fieldPrefix}discount_price.lt" => 'Discount price must be less than the regular price.',
            "{$fieldPrefix}stock.required" => 'Stock quantity is required.',
            "{$fieldPrefix}stock.integer" => 'Stock quantity must be a whole number.',
            "{$fieldPrefix}stock.min" => 'Stock quantity cannot be negative.',
            "{$fieldPrefix}stock.max" => 'Stock quantity cannot exceed 999,999.',
            "{$fieldPrefix}category_id.required" => 'Product category is required.',
            "{$fieldPrefix}category_id.exists" => 'Selected category does not exist.',
            "{$fieldPrefix}brand_id.exists" => 'Selected brand does not exist.',
            "{$fieldPrefix}vendor_id.required" => 'Vendor selection is required.',
            "{$fieldPrefix}vendor_id.exists" => 'Selected vendor does not exist.',
            "{$fieldPrefix}weight.numeric" => 'Weight must be a valid number.',
            "{$fieldPrefix}weight.min" => 'Weight cannot be negative.',
            "{$fieldPrefix}weight.max" => 'Weight cannot exceed 999.99 kg.',
            "{$fieldPrefix}height.numeric" => 'Height must be a valid number.',
            "{$fieldPrefix}height.min" => 'Height cannot be negative.',
            "{$fieldPrefix}height.max" => 'Height cannot exceed 999.99 cm.',
            "{$fieldPrefix}width.numeric" => 'Width must be a valid number.',
            "{$fieldPrefix}width.min" => 'Width cannot be negative.',
            "{$fieldPrefix}width.max" => 'Width cannot exceed 999.99 cm.',
            "{$fieldPrefix}length.numeric" => 'Length must be a valid number.',
            "{$fieldPrefix}length.min" => 'Length cannot be negative.',
            "{$fieldPrefix}length.max" => 'Length cannot exceed 999.99 cm.',
            "{$fieldPrefix}sku.max" => 'SKU cannot exceed 255 characters.',
        ];
    }

    /**
     * Get validation attributes for better error messages
     * 
     * @param string $prefix Optional prefix for field names
     * @return array
     */
    protected function getProductValidationAttributes(string $prefix = ''): array
    {
        $fieldPrefix = $prefix ? "{$prefix}." : '';
        
        return [
            "{$fieldPrefix}name" => 'product name',
            "{$fieldPrefix}description" => 'product description',
            "{$fieldPrefix}price" => 'product price',
            "{$fieldPrefix}discount_price" => 'discount price',
            "{$fieldPrefix}stock" => 'stock quantity',
            "{$fieldPrefix}category_id" => 'category',
            "{$fieldPrefix}brand_id" => 'brand',
            "{$fieldPrefix}vendor_id" => 'vendor',
            "{$fieldPrefix}weight" => 'weight',
            "{$fieldPrefix}height" => 'height',
            "{$fieldPrefix}width" => 'width',
            "{$fieldPrefix}length" => 'length',
            "{$fieldPrefix}sku" => 'SKU',
            "{$fieldPrefix}is_active" => 'active status',
        ];
    }

    /**
     * Validate product data using standardized rules
     * 
     * @param array $data Data to validate
     * @param bool $isUpdate Whether this is for update
     * @param bool $requireVendor Whether vendor_id is required
     * @param string $prefix Optional field prefix
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validateProductData(array $data, bool $isUpdate = false, bool $requireVendor = false, string $prefix = ''): \Illuminate\Contracts\Validation\Validator
    {
        $rules = $prefix 
            ? $this->getPrefixedProductValidationRules($prefix, $isUpdate, $requireVendor)
            : $this->getProductValidationRules($isUpdate, $requireVendor);

        $messages = $this->getProductValidationMessages($prefix);
        $attributes = $this->getProductValidationAttributes($prefix);

        return \Illuminate\Support\Facades\Validator::make($data, $rules, $messages, $attributes);
    }
}
