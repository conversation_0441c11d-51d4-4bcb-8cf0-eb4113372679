@props([
    'size' => 'md',
    'color' => 'primary',
    'icon' => null,
    'loading' => false,
    'disabled' => false,
])

@php
$sizeClasses = [
    'xs' => 'h-6 w-6 text-xs',
    'sm' => 'h-8 w-8 text-sm',
    'md' => 'h-10 w-10 text-base',
    'lg' => 'h-12 w-12 text-lg',
    'xl' => 'h-14 w-14 text-xl',
];

$colorClasses = [
    'primary' => 'bg-indigo-600 hover:bg-indigo-700 text-white',
    'secondary' => 'bg-gray-600 hover:bg-gray-700 text-white',
    'success' => 'bg-green-600 hover:bg-green-700 text-white',
    'danger' => 'bg-red-600 hover:bg-red-700 text-white',
    'warning' => 'bg-yellow-600 hover:bg-yellow-700 text-white',
    'info' => 'bg-blue-600 hover:bg-blue-700 text-white',
    'light' => 'bg-gray-100 hover:bg-gray-200 text-gray-800',
    'dark' => 'bg-gray-800 hover:bg-gray-900 text-white',
];

$classes = implode(' ', [
    'inline-flex items-center justify-center rounded-full border border-transparent font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200',
    $sizeClasses[$size] ?? $sizeClasses['md'],
    $colorClasses[$color] ?? $colorClasses['primary'],
    $disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
]);
@endphp

<button {{ $attributes->merge(['class' => $classes, 'disabled' => $disabled]) }}>
    @if($loading)
        <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    @elseif($icon)
        <i class="{{ $icon }}"></i>
    @else
        {{ $slot }}
    @endif
</button>
