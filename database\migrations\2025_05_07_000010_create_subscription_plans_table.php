<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->string('interval')->default('monthly'); // monthly, bi-annually, annually
            $table->integer('duration_days');
            $table->integer('product_limit')->nullable(); // null = unlimited
            $table->integer('order_limit')->nullable(); // null = unlimited
            $table->decimal('commission_rate', 5, 2)->default(2.7); // 2.7%
            $table->text('features')->nullable();
            $table->string('paystack_plan_code')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('status')->default('active');
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
