<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

/**
 * Admin authorization trait for Livewire components
 * 
 * This trait provides consistent admin authorization checks
 * to resolve LOGIC-HIGH-002: Admin authorization enforcement gap
 * 
 * Usage:
 * - Admin Livewire Components
 * - Admin Controllers
 * - Any component requiring admin access
 */
trait RequiresAdminAuthorization
{
    /**
     * Boot method to automatically enforce admin authorization
     * This is called automatically when the trait is used
     */
    public function bootRequiresAdminAuthorization()
    {
        $this->enforceAdminAuthorization();
    }

    /**
     * Enforce admin authorization with comprehensive checks
     * 
     * @return void
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function enforceAdminAuthorization(): void
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            abort(401, 'Authentication required. Please login to access admin features.');
        }

        $user = Auth::user();

        // Check if user has admin role
        if (!$user->hasRole('admin')) {
            abort(403, 'Access denied. Administrator privileges required.');
        }

        // Additional security: Check if admin account is not suspended
        if (method_exists($user, 'isSuspended') && $user->isSuspended()) {
            abort(403, 'Access denied. Administrator account is suspended. Please contact support.');
        }
    }

    /**
     * Check if current user is an authorized admin
     * 
     * @return bool
     */
    protected function isAuthorizedAdmin(): bool
    {
        try {
            $this->enforceAdminAuthorization();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get the current admin user or throw exception if not authorized
     * 
     * @return \App\Models\User
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    protected function getAuthorizedAdmin(): \App\Models\User
    {
        $this->enforceAdminAuthorization();
        return Auth::user();
    }

    /**
     * Get admin authorization status information
     * 
     * @return array
     */
    protected function getAdminAuthorizationStatus(): array
    {
        if (!Auth::check()) {
            return [
                'authenticated' => false,
                'has_admin_role' => false,
                'status' => 'unauthenticated'
            ];
        }

        $user = Auth::user();
        $hasAdminRole = $user->hasRole('admin');

        $status = $hasAdminRole ? 'fully_authorized' : 'unauthorized';

        return [
            'authenticated' => true,
            'has_admin_role' => $hasAdminRole,
            'status' => $status
        ];
    }

    /**
     * Redirect to appropriate page based on admin authorization status
     * 
     * @return \Illuminate\Http\RedirectResponse|null
     */
    protected function redirectBasedOnAdminStatus(): ?\Illuminate\Http\RedirectResponse
    {
        $status = $this->getAdminAuthorizationStatus();

        if (!$status['authenticated']) {
            return redirect()->route('login')->with('error', 'Please login to access admin features.');
        }

        if (!$status['has_admin_role']) {
            return redirect()->route('home')->with('error', 'Administrator privileges required.');
        }

        return null; // No redirect needed, admin is fully authorized
    }

    /**
     * Log admin authorization attempt for security monitoring
     * 
     * @param string $action Action being attempted
     * @param bool $success Whether authorization was successful
     * @param string|null $reason Reason for failure if applicable
     * @return void
     */
    protected function logAdminAuthorizationAttempt(string $action, bool $success, ?string $reason = null): void
    {
        $logData = [
            'action' => $action,
            'success' => $success,
            'user_id' => Auth::id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ];

        if (!$success && $reason) {
            $logData['failure_reason'] = $reason;
        }

        \Log::info('Admin authorization attempt', $logData);
    }
}
