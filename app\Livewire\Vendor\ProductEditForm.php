<?php

namespace App\Livewire\Vendor;

use App\Models\Product;
use App\Models\Vendor;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class ProductEditForm extends Component
{
    use WithFileUploads;

    public Product $product;
    public Vendor $vendor;
    
    // Dropdown data (passed from controller)
    public $categories = [];
    public $brands = [];
    public $colors = [];
    public $sizes = [];

    // Form fields
    public $name;
    public $description;
    public $price;
    public $discount_price;
    public $stock;
    public $sku;
    public $category_id;
    public $brand_id;
    public $is_active;
    public $weight;
    public $length;
    public $width;
    public $height;

    // Variants and specifications
    public $variants = [];
    public $specifications = [];
    public $hasVariants = false;

    // UI state
    public $saving = false;

    public function mount(Product $product, Vendor $vendor, $categories = [], $brands = [], $colors = [], $sizes = [])
    {
        $this->product = $product;
        $this->vendor = $vendor;
        
        // Store dropdown data
        $this->categories = $categories;
        $this->brands = $brands;
        $this->colors = $colors;
        $this->sizes = $sizes;

        // Populate form fields from product
        $this->name = $product->name;
        $this->description = $product->description;
        $this->price = $product->price;
        $this->discount_price = $product->discount_price;
        $this->stock = $product->stock;
        $this->sku = $product->sku;
        $this->category_id = $product->category_id;
        $this->brand_id = $product->brand_id;
        $this->is_active = $product->is_active;
        $this->weight = $product->weight;
        $this->length = $product->length;
        $this->width = $product->width;
        $this->height = $product->height;

        // Load variants and specifications
        $this->variants = $product->variants->map(function ($variant) {
            return [
                'id' => $variant->id,
                'color_id' => $variant->color_id,
                'size_id' => $variant->size_id,
                'price' => $variant->price,
                'stock' => $variant->stock,
                'sku' => $variant->sku,
            ];
        })->toArray();

        $this->specifications = $product->specifications->map(function ($spec) {
            return [
                'id' => $spec->id,
                'name' => $spec->name,
                'value' => $spec->value,
            ];
        })->toArray();

        $this->hasVariants = count($this->variants) > 0;

        // Add empty specification if none exist
        if (empty($this->specifications)) {
            $this->addSpecification();
        }
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|min:10',
            'price' => 'required|numeric|min:0|max:999999.99',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock' => 'required|integer|min:0|max:999999',
            'sku' => 'nullable|string|max:100|unique:products,sku,' . $this->product->id,
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'is_active' => 'boolean',
            'weight' => 'nullable|numeric|min:0|max:999.99',
            'length' => 'nullable|numeric|min:0|max:999.99',
            'width' => 'nullable|numeric|min:0|max:999.99',
            'height' => 'nullable|numeric|min:0|max:999.99',
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Product name is required.',
            'description.required' => 'Product description is required.',
            'description.min' => 'Description must be at least 10 characters.',
            'price.required' => 'Price is required.',
            'price.numeric' => 'Price must be a valid number.',
            'discount_price.lt' => 'Discount price must be less than the regular price.',
            'stock.required' => 'Stock quantity is required.',
            'sku.unique' => 'This SKU is already taken.',
            'category_id.required' => 'Please select a category.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function updatedPrice($value)
    {
        $this->validateOnly('price');
    }

    public function updatedStock($value)
    {
        $this->validateOnly('stock');
    }

    public function addSpecification()
    {
        $this->specifications[] = [
            'id' => null,
            'name' => '',
            'value' => '',
        ];
    }

    public function removeSpecification($index)
    {
        unset($this->specifications[$index]);
        $this->specifications = array_values($this->specifications);
    }

    public function addVariant()
    {
        $this->variants[] = [
            'id' => null,
            'color_id' => null,
            'size_id' => null,
            'price' => $this->price,
            'stock' => 0,
            'sku' => '',
        ];
        $this->hasVariants = true;
    }

    public function removeVariant($index)
    {
        unset($this->variants[$index]);
        $this->variants = array_values($this->variants);
        
        if (empty($this->variants)) {
            $this->hasVariants = false;
        }
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            // Update product
            $this->product->update([
                'name' => $this->name,
                'slug' => Str::slug($this->name),
                'description' => $this->description,
                'price' => $this->price,
                'discount_price' => $this->discount_price,
                'stock' => $this->stock,
                'sku' => $this->sku,
                'category_id' => $this->category_id,
                'brand_id' => $this->brand_id,
                'vendor_id' => $this->vendor->id,
                'is_active' => $this->is_active,
                'weight' => $this->weight,
                'length' => $this->length,
                'width' => $this->width,
                'height' => $this->height,
            ]);

            // Update specifications
            $this->product->specifications()->delete();
            foreach ($this->specifications as $spec) {
                if (!empty($spec['name']) && !empty($spec['value'])) {
                    $this->product->specifications()->create([
                        'name' => $spec['name'],
                        'value' => $spec['value'],
                    ]);
                }
            }

            // Update variants if they exist
            if ($this->hasVariants && !empty($this->variants)) {
                $this->product->variants()->delete();
                foreach ($this->variants as $variant) {
                    if ($variant['color_id'] || $variant['size_id']) {
                        $this->product->variants()->create([
                            'color_id' => $variant['color_id'],
                            'size_id' => $variant['size_id'],
                            'price' => $variant['price'] ?? $this->price,
                            'stock' => $variant['stock'] ?? 0,
                            'sku' => $variant['sku'],
                        ]);
                    }
                }
            }

            $this->dispatch('product-updated');
            session()->flash('success', 'Product updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update product: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.vendor.product-edit-form');
    }
}
