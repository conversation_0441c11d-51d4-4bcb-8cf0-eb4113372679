<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;

class SubscriptionPlanController extends Controller
{
    public function destroy(SubscriptionPlan $subscriptionPlan)
    {
        // Check if any vendors are currently subscribed to this plan
        if ($subscriptionPlan->subscriptions()->where('status', 'active')->exists()) {
            return redirect()->route('admin.subscription-plans.index')
                ->with('error', 'Cannot delete subscription plan with active subscriptions.');
        }

        $subscriptionPlan->delete();
        
        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan deleted successfully.');
    }
}
