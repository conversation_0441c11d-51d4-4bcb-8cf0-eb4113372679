{{-- Responsive Image Component with Optimization --}}
@props([
    'src' => null,
    'alt' => '',
    'width' => null,
    'height' => null,
    'sizes' => '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
    'loading' => 'lazy',
    'placeholder' => 'images/product-placeholder.svg',
    'class' => '',
    'objectFit' => 'cover',
    'aspectRatio' => null,
    'fallback' => null
])

@php
    // Default fallback image
    $defaultFallback = $fallback ?? asset($placeholder);
    
    // Build responsive image classes
    $imageClasses = 'transition-all duration-300 ' . $class;
    
    // Add object-fit classes
    $objectFitClasses = [
        'cover' => 'object-cover',
        'contain' => 'object-contain',
        'fill' => 'object-fill',
        'none' => 'object-none',
        'scale-down' => 'object-scale-down'
    ];
    
    if (isset($objectFitClasses[$objectFit])) {
        $imageClasses .= ' ' . $objectFitClasses[$objectFit];
    }
    
    // Add aspect ratio classes if specified
    if ($aspectRatio) {
        $aspectRatioClasses = [
            'square' => 'aspect-square',
            '4/3' => 'aspect-4/3',
            '3/2' => 'aspect-3/2',
            '16/9' => 'aspect-video',
            '2/1' => 'aspect-2/1'
        ];
        
        if (isset($aspectRatioClasses[$aspectRatio])) {
            $imageClasses .= ' ' . $aspectRatioClasses[$aspectRatio];
        }
    }
    
    // Generate srcset for responsive images (if we have a base image)
    $srcset = '';
    if ($src && !str_contains($src, 'placeholder')) {
        // For now, we'll use the same image for all sizes
        // In a production environment, you'd generate different sizes
        $srcset = $src . ' 1x';
    }
@endphp

<div class="relative overflow-hidden bg-gray-100">
    @if($src)
        <img 
            src="{{ $src }}"
            alt="{{ $alt }}"
            @if($width) width="{{ $width }}" @endif
            @if($height) height="{{ $height }}" @endif
            @if($srcset) srcset="{{ $srcset }}" @endif
            sizes="{{ $sizes }}"
            loading="{{ $loading }}"
            class="{{ $imageClasses }}"
            onerror="this.onerror=null; this.src='{{ $defaultFallback }}'; this.classList.add('opacity-80', 'bg-gray-200');"
            onload="this.classList.add('loaded')"
            {{ $attributes->except(['src', 'alt', 'width', 'height', 'class']) }}
        >
    @else
        <img 
            src="{{ $defaultFallback }}"
            alt="{{ $alt ?: 'Placeholder image' }}"
            @if($width) width="{{ $width }}" @endif
            @if($height) height="{{ $height }}" @endif
            loading="{{ $loading }}"
            class="{{ $imageClasses }} opacity-80 bg-gray-200"
            {{ $attributes->except(['src', 'alt', 'width', 'height', 'class']) }}
        >
    @endif
    
    <!-- Loading placeholder -->
    <div class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center image-loading">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
    </div>
</div>

<style>
    .image-loading {
        transition: opacity 0.3s ease;
    }
    
    img.loaded + .image-loading {
        opacity: 0;
        pointer-events: none;
    }
    
    img:not(.loaded) {
        opacity: 0;
    }
    
    img.loaded {
        opacity: 1;
    }
</style>
