<?php

namespace App\Livewire\Forms;

use Livewire\Attributes\Validate;
use Livewire\Form;

class CheckoutForm extends Form
{
    // Customer Information
    #[Validate('required|string|max:255')]
    public string $first_name = '';

    #[Validate('required|string|max:255')]
    public string $last_name = '';

    #[Validate('required|email|max:255')]
    public string $email = '';

    #[Validate('required|string|regex:/^0[789][01]\d{8}$/')]
    public string $phone = '';

    #[Validate('required|in:paystack,bank_transfer')]
    public string $payment_method = '';

    // Shipping Address
    #[Validate('required|string|min:10|max:255')]
    public string $shipping_address = '';

    #[Validate('required|string|min:2|max:255')]
    public string $shipping_city = '';

    #[Validate('required|string|max:255')]
    public string $shipping_state = '';

    #[Validate('nullable|string|max:255')]
    public string $shipping_lga = '';

    #[Validate('nullable|string|max:20')]
    public string $shipping_postal_code = '';

    #[Validate('required|string|max:3')]
    public string $shipping_country = 'NG';

    #[Validate('required|string|regex:/^0[789][01]\d{8}$/')]
    public string $shipping_phone = '';

    /**
     * Get all customer data as array
     */
    public function getCustomerData(): array
    {
        return [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
        ];
    }

    /**
     * Get all shipping data as array
     */
    public function getShippingData(): array
    {
        return [
            'shipping_address' => $this->shipping_address,
            'shipping_city' => $this->shipping_city,
            'shipping_state' => $this->shipping_state,
            'shipping_lga' => $this->shipping_lga,
            'shipping_postal_code' => $this->shipping_postal_code,
            'shipping_country' => $this->shipping_country,
            'shipping_phone' => $this->shipping_phone,
        ];
    }

    /**
     * Get all form data as array
     */
    public function getAllData(): array
    {
        return array_merge(
            $this->getCustomerData(),
            $this->getShippingData(),
            ['payment_method' => $this->payment_method]
        );
    }

    /**
     * Validate only customer information fields
     */
    public function validateCustomerInfo(): void
    {
        $this->validateOnly([
            'first_name',
            'last_name', 
            'email',
            'phone'
        ]);
    }

    /**
     * Validate only shipping address fields
     */
    public function validateShippingAddress(): void
    {
        $this->validateOnly([
            'shipping_address',
            'shipping_city',
            'shipping_state',
            'shipping_lga',
            'shipping_postal_code',
            'shipping_country',
            'shipping_phone'
        ]);
    }

    /**
     * Validate payment method
     */
    public function validatePaymentMethod(): void
    {
        $this->validateOnly('payment_method');
    }

    /**
     * Custom validation messages
     */
    protected function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
            'payment_method.required' => 'Please select a payment method.',
            'payment_method.in' => 'Please select a valid payment method.',
            'shipping_address.required' => 'Shipping address is required.',
            'shipping_address.min' => 'Please provide a more detailed address (at least 10 characters).',
            'shipping_city.required' => 'City is required.',
            'shipping_city.min' => 'City name must be at least 2 characters.',
            'shipping_state.required' => 'State is required.',
            'shipping_country.required' => 'Country is required.',
            'shipping_phone.required' => 'Shipping phone number is required.',
            'shipping_phone.regex' => 'Please enter a valid Nigerian phone number for shipping.',
        ];
    }

    /**
     * Custom attribute names for validation messages
     */
    protected function validationAttributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => 'email address',
            'phone' => 'phone number',
            'payment_method' => 'payment method',
            'shipping_address' => 'shipping address',
            'shipping_city' => 'shipping city',
            'shipping_state' => 'shipping state',
            'shipping_lga' => 'LGA',
            'shipping_postal_code' => 'postal code',
            'shipping_country' => 'shipping country',
            'shipping_phone' => 'shipping phone number',
        ];
    }
}
