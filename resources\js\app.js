/**
 * CRITICAL FIX JS4: Consolidated JavaScript Framework Loading
 * Single entry point for all JavaScript functionality
 */

import './bootstrap';

// Import Alpine.js for consistent bundling
import Alpine from 'alpinejs';

// Make Alpine available globally
window.Alpine = Alpine;

// Start Alpine
Alpine.start();

// Import unified cart manager
import './cart-manager';

// Hero Image Slider - Consolidated implementation
document.addEventListener('DOMContentLoaded', () => {
    // Use the slider-config.js implementation instead of duplicating here
    // This file is loaded separately to avoid conflicts

    // Initialize other global functionality
    initializeGlobalFeatures();
});

function initializeGlobalFeatures() {
    // Handle placeholder links
    document.querySelectorAll('a[href="#"]').forEach(function (anchor) {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            alert('This feature is currently under development and will be available soon.');
        });
    });

    // Initialize any other global features here
}