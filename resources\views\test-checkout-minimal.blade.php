<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Checkout Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-2xl font-bold mb-4">Minimal Checkout Test</h1>
        
        <!-- Test basic form without any custom components -->
        <form class="bg-white p-6 rounded-lg shadow" method="POST" action="#">
            @csrf
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <input type="text" name="address" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>

            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">
                Test Submit
            </button>
        </form>
        
        <!-- Test array operations -->
        @php
            $testArray = ['key1' => 'value1', 'key2' => 'value2'];
            $testString = 'not_an_array';
        @endphp
        
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-bold mb-4">Array Test Results</h2>
            
            <p>Test Array: {{ json_encode($testArray) }}</p>
            <p>Array Key Exists (key1): {{ array_key_exists('key1', $testArray) ? 'true' : 'false' }}</p>
            
            <!-- This should NOT cause an error with our fixes -->
            <p>Safe Array Check: {{ isset($testArray['key1']) ? 'exists' : 'not_exists' }}</p>
        </div>
    </div>
</body>
</html>
