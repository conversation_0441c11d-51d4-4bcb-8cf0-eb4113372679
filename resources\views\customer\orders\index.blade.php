@extends('layouts.customer')

@section('customer-content')
    {{-- Order Stats --}}
    @php
        $allOrders = auth()->user()->orders;
        $totalOrders = $allOrders->count();
        $pendingOrders = $allOrders->where('status', 'pending')->count();
        $completedOrders = $allOrders->where('status', 'completed')->count();
    @endphp
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500">Total Orders</p>
                    <p class="text-3xl font-bold mt-1">{{ $totalOrders }}</p>
                </div>
                <div class="bg-black text-white rounded-full w-10 h-10 flex items-center justify-center">
                    <i class="fas fa-box-open"></i>
                </div>
            </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500">Processing Orders</p>
                    <p class="text-3xl font-bold mt-1">{{ $pendingOrders }}</p>
                </div>
                <div class="bg-black text-white rounded-full w-10 h-10 flex items-center justify-center">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500">Completed Orders</p>
                    <p class="text-3xl font-bold mt-1">{{ $completedOrders }}</p>
                </div>
                <div class="bg-black text-white rounded-full w-10 h-10 flex items-center justify-center">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold">Order History</h2>
            <p class="text-gray-500 mt-1">Track and manage your past and current orders.</p>
        </div>

        @if($orders->isNotEmpty())
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">View</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @php
                            $statusColors = [
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'processing' => 'bg-blue-100 text-blue-800',
                                'shipped' => 'bg-indigo-100 text-indigo-800',
                                'completed' => 'bg-green-100 text-green-800',
                                'cancelled' => 'bg-red-100 text-red-800',
                                'return_requested' => 'bg-purple-100 text-purple-800',
                                'returned' => 'bg-gray-100 text-gray-800',
                            ];
                        @endphp
                        @foreach($orders as $order)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ $order->order_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $order->created_at->format('M d, Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $order->vendor->business_name ?? 'N/A' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusColors[$order->status] ?? 'bg-gray-100 text-gray-800' }}">
                                        {{ str_replace('_', ' ', Str::title($order->status)) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">₦{{ number_format($order->total, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ route('orders.show', $order) }}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">
                                        View Order
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            @if ($orders->hasPages())
                <div class="p-6 border-t border-gray-200">
                    {{ $orders->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-16">
                <i class="fas fa-shopping-bag fa-4x text-gray-300 mb-4"></i>
                <h4 class="font-bold text-xl mt-4">No Orders Found</h4>
                <p class="text-gray-500 mt-2">You haven't placed any orders yet. When you do, they will appear here.</p>
                <a href="{{ route('products.index') }}" class="mt-6 inline-flex items-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    Start Shopping
                </a>
            </div>
        @endif
    </div>
@endsection
