/**
 * CRITICAL FIX RD1: Unified Responsive Design System
 * Uses Tailwind CSS breakpoints consistently across the application
 * Replaces conflicting Bootstrap breakpoints with Tailwind standards
 */

/* Global mobile fixes */
body {
    overflow-x: hidden;
}

/* Touch-friendly enhancements */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
}

/* Ensure all interactive elements are touch-friendly */
button,
a[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"],
.btn {
    touch-action: manipulation;
    min-height: 44px;
}

/* Mobile-first responsive text scaling */
@media (max-width: 639px) {
    html {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Navbar spacing fixes - Using Tailwind breakpoints */
.navbar-spacing-fix {
    padding-top: 3.5rem; /* 56px - matches navbar height h-14 */
}

/* Tailwind sm: 640px */
@media (min-width: 640px) {
    .navbar-spacing-fix {
        padding-top: 4rem; /* 64px - matches larger navbar height h-16 */
    }
}

/* Ensure content is never hidden behind fixed navbar */
main[class*="pt-"] {
    min-height: calc(100vh - 3.5rem);
}

/* Tailwind sm: 640px */
@media (min-width: 640px) {
    main[class*="pt-"] {
        min-height: calc(100vh - 4rem);
    }
}

/* Responsive breakpoint adjustments */
.container, .container-fluid {
    padding-left: var(--bs-gutter-x, 0.75rem);
    padding-right: var(--bs-gutter-x, 0.75rem);
}

/* Chart and map containers */
.chart-container {
    position: relative;
    height: 300px;
    max-height: 300px;
    width: 100%;
    overflow: hidden;
}

.chart-container.small {
    height: 230px;
    max-height: 230px;
}

#nigeria-map-container {
    position: relative;
    height: 380px;
    max-height: 380px;
    overflow: hidden;
    border-bottom: 1px solid #dee2e6;
}

/* Dashboard card styling */
.card {
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 100%;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stats icon styling */
.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bs-light, #f8f9fa);
}

/* Mobile navbar improvements - FIXED RD1: Use Tailwind lg breakpoint */
@media (max-width: 1023px) { /* Tailwind lg: 1024px */
    .navbar-collapse {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 0.5rem;
        z-index: 1050;
    }

    .navbar-nav {
        margin-bottom: 1rem;
    }

    form.d-flex {
        margin: 1rem 0;
        width: 100%;
    }

    .d-flex.align-items-center.gap-4 {
        display: flex;
        justify-content: space-around;
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Vendor sidebar */
    .sidebar {
        margin-left: -280px;
        box-shadow: 5px 0 10px rgba(0,0,0,0.1);
        z-index: 1060;
        position: relative;
    }

    .sidebar.show {
        margin-left: 0;
    }

    .content {
        margin-left: 0;
        padding: 1.5rem 1rem;
    }

    .sidebar-toggle {
        display: block;
    }

    .sidebar-overlay.show {
        display: block;
    }

    /* Chart and map containers on medium screens */
    .chart-container,
    .chart-container.small {
        height: 250px;
    }

    #nigeria-map-container {
        height: 300px;
    }

    .card:hover {
        transform: none;
    }
}

/* Small mobile screens - FIXED RD1: Use Tailwind md breakpoint */
@media (max-width: 767px) { /* Tailwind md: 768px */
    .container.pt-5.mt-5 {
        padding-top: 4.5rem !important;
        margin-top: 3.5rem !important;
    }

    .chart-container,
    .chart-container.small {
        height: 200px;
    }

    #nigeria-map-container {
        height: 250px;
    }

    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    .row > [class*="col-"] {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    h3.fw-bold {
        font-size: 1.4rem;
    }
}

/* Extra small devices - FIXED RD1: Use Tailwind sm breakpoint */
@media (max-width: 639px) { /* Tailwind sm: 640px */
    .table th, .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .card-header {
        padding: 0.75rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .dropdown-menu {
        font-size: 0.875rem;
    }
}

/* Specific fixes for vendor dashboard */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table th, .table td {
    white-space: nowrap;
}

/* Ensure all product cards are consistent height */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

.product-card .card-footer {
    margin-top: auto;
}

/* Fix mobile product grid - FIXED RD1: Use Tailwind md breakpoint */
@media (max-width: 767px) { /* Tailwind md: 768px */
    .product-grid .col-md-4,
    .product-grid .col-lg-3 {
        width: 50%;
    }
}

/* FIXED RD1: Use Tailwind sm breakpoint */
@media (max-width: 639px) { /* Tailwind sm: 640px */
    .product-grid .col-md-4,
    .product-grid .col-lg-3 {
        width: 100%;
    }
}

/* Dashboard specific fixes */
.dashboard-content {
    min-height: calc(100vh - 200px);
    padding-bottom: 2rem;
}

/* Recommended products card fixes */
.product-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 0.5rem;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.product-card .card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Dashboard layout improvements */
.dashboard-sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

/* Ensure proper spacing between dashboard sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.dashboard-section:last-child {
    margin-bottom: 4rem; /* Extra space before footer */
}

/* Fix for recommended products overlapping footer */
.recommended-products-section {
    position: relative;
    z-index: 1;
    margin-bottom: 3rem;
}

/* Mobile dashboard improvements - FIXED RD1: Use Tailwind lg breakpoint */
@media (max-width: 1023px) { /* Tailwind lg: 1024px */
    .dashboard-sidebar {
        position: static;
        margin-bottom: 2rem;
    }

    .dashboard-content {
        min-height: auto;
    }

    .recommended-products-section {
        margin-bottom: 2rem;
    }
}

/* Dashboard sidebar improvements */
.dashboard-sidebar .nav-link {
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.dashboard-sidebar .nav-link:hover:not(.bg-dark) {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.dashboard-sidebar .nav-link.bg-dark {
    background-color: #000 !important;
}

/* Stats cards improvements */
.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Recent orders improvements */
.recent-orders-item {
    transition: background-color 0.2s ease;
}

.recent-orders-item:hover {
    background-color: #f8f9fa;
}

/* Recommended products improvements */
.recommended-products-section .product-card {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.recommended-products-section .product-card:hover {
    border-color: #000;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Dashboard responsive text - FIXED RD1: Use Tailwind md breakpoint */
@media (max-width: 767px) { /* Tailwind md: 768px */
    .dashboard-content h3 {
        font-size: 1.5rem;
    }

    .dashboard-content h4 {
        font-size: 1.25rem;
    }

    .dashboard-content h5 {
        font-size: 1.1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Fix for potential z-index issues */
.dashboard-content {
    position: relative;
    z-index: 1;
}

.footer {
    position: relative;
    z-index: 2;
}
