<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Order #{{ $order->order_number }}</h1>
            <a href="{{ route('admin.orders.index') }}" class="text-sm text-gray-600 dark:text-gray-400 hover:underline">&larr; Back to Orders</a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Order Items</h3>
                    </div>
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($order->items as $item)
                            <div class="p-4 flex items-center space-x-4">
                                <img src="{{ $item->product->featured_image?->getUrl('thumb') ?? asset('images/placeholder-product.png') }}" alt="{{ $item->product->name }}" class="w-16 h-16 rounded object-cover">
                                <div class="flex-grow">
                                    <p class="font-semibold text-gray-900 dark:text-white">{{ $item->product->name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">SKU: {{ $item->product->sku }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold">{{ $item->formatted_price }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Qty: {{ $item->quantity }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="p-6 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg text-right">
                        <p class="text-gray-600 dark:text-gray-300">Subtotal: <span class="font-semibold">{{ $order->formatted_subtotal }}</span></p>
                        <p class="text-gray-600 dark:text-gray-300">Shipping: <span class="font-semibold">{{ $order->formatted_shipping_cost }}</span></p>
                        @if($order->discount_code)
                        <p class="text-gray-600 dark:text-gray-300">Discount ({{ $order->discount_code }}): <span class="font-semibold">-{{ $order->formatted_discount }}</span></p>
                        @endif
                        <p class="text-xl font-bold text-gray-900 dark:text-white mt-2">Total: <span class="font-semibold">{{ $order->formatted_total }}</span></p>
                    </div>
                </div>
            </div>

            <div>
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Customer Details</h3>
                    </div>
                    <div class="p-6 space-y-2">
                        <p class="font-semibold">{{ $order->user->name }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->user->email }}</p>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Shipping Address</h3>
                    </div>
                    <div class="p-6 text-sm text-gray-600 dark:text-gray-400">
                        @php $shippingAddr = $order->formatted_shipping_address; @endphp
                        @if($shippingAddr['name'] || $shippingAddr['address'] || $shippingAddr['city'])
                            @if($shippingAddr['name'])
                                <p class="font-medium text-gray-900 dark:text-white">{{ $shippingAddr['name'] }}</p>
                            @endif
                            @if($shippingAddr['address'])
                                <p>{{ $shippingAddr['address'] }}</p>
                            @endif
                            @if($shippingAddr['city'] || $shippingAddr['state'] || $shippingAddr['postal_code'])
                                <p>
                                    {{ $shippingAddr['city'] }}@if($shippingAddr['city'] && ($shippingAddr['state'] || $shippingAddr['postal_code'])),@endif
                                    {{ $shippingAddr['state'] }} {{ $shippingAddr['postal_code'] }}
                                </p>
                            @endif
                            @if($shippingAddr['lga'])
                                <p class="text-xs text-gray-500">LGA: {{ $shippingAddr['lga'] }}</p>
                            @endif
                            @if($shippingAddr['country'])
                                <p>{{ $shippingAddr['country'] }}</p>
                            @endif
                            @if($shippingAddr['phone'])
                                <p class="mt-2">
                                    <i class="fas fa-phone mr-1"></i>{{ $shippingAddr['phone'] }}
                                </p>
                            @endif
                        @else
                            <p>No shipping address provided.</p>
                        @endif
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold">Update Status</h3>
                    </div>
                    <div class="p-6">
                        @if (session()->has('success'))
                            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                                {{ session('success') }}
                            </div>
                        @endif

                        <div class="space-y-4">
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Order Status</label>
                                <select wire:model.defer="status"
                                        id="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            <button wire:click="updateStatus"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 bg-black hover:bg-gray-800 text-white text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                <i class="fas fa-save mr-2"></i>
                                Update Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
