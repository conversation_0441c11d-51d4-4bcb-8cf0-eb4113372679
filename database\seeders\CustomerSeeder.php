<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customerRole = Role::where('name', 'customer')->first();

        if (!$customerRole) {
            $this->command->error('Customer role not found. Please run RoleSeeder first.');
            return;
        }

        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+2348123456789',
                'address' => '15 Admiralty Way, Lekki Phase 1',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+2348123456790',
                'address' => '42 Ozumba Mbadiwe Avenue',
                'city' => 'Victoria Island',
                'state' => 'Lagos',
                'country' => 'Nigeria',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+2348123456791',
                'address' => '8 Adeola Odeku Street',
                'city' => 'Victoria Island',
                'state' => 'Lagos',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'Sarah Williams',
                'email' => '<EMAIL>',
                'phone' => '+2348123456792',
                'address' => '23 Ikoyi Crescent',
                'city' => 'Ikoyi',
                'state' => 'Lagos',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'phone' => '+2348123456793',
                'address' => '67 Allen Avenue',
                'city' => 'Ikeja',
                'state' => 'Lagos',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'phone' => '+2348123456794',
                'address' => '12 Wuse 2 District',
                'city' => 'Abuja',
                'state' => 'FCT',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'James Wilson',
                'email' => '<EMAIL>',
                'phone' => '+2348123456795',
                'address' => '34 Independence Layout',
                'city' => 'Enugu',
                'state' => 'Enugu',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'Lisa Anderson',
                'email' => '<EMAIL>',
                'phone' => '+2348123456796',
                'address' => '56 Ring Road',
                'city' => 'Ibadan',
                'state' => 'Oyo',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'Robert Taylor',
                'email' => '<EMAIL>',
                'phone' => '+2348123456797',
                'address' => '78 Aba Road',
                'city' => 'Port Harcourt',
                'state' => 'Rivers',
                'country' => 'Nigeria',
            ],
            [
                'name' => 'Michelle Garcia',
                'email' => '<EMAIL>',
                'phone' => '+2348123456798',
                'address' => '90 Ahmadu Bello Way',
                'city' => 'Kaduna',
                'state' => 'Kaduna',
                'country' => 'Nigeria',
            ],
        ];

        foreach ($customers as $customerData) {
            $user = User::updateOrCreate(
                ['email' => $customerData['email']],
                [
                    'name' => $customerData['name'],
                    'password' => Hash::make('password123'), // Default password for demo
                    'phone' => $customerData['phone'],
                    'address' => $customerData['address'],
                    'city' => $customerData['city'],
                    'state' => $customerData['state'],
                    'country' => $customerData['country'],
                    'email_verified_at' => now(),
                    'role_id' => $customerRole->id,
                ]
            );

            $this->command->info("Created customer: {$user->name} ({$user->email})");
        }

        $this->command->info('Created 10 sample customers with Nigerian addresses');
    }
}
