<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Add has_completed_onboarding column if it doesn't exist
            if (!Schema::hasColumn('vendors', 'has_completed_onboarding')) {
                $table->boolean('has_completed_onboarding')->default(false)->after('is_approved');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            if (Schema::hasColumn('vendors', 'has_completed_onboarding')) {
                $table->dropColumn('has_completed_onboarding');
            }
        });
    }
};
