<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

class ComponentFixesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data manually
        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        $this->brand = Brand::create([
            'name' => 'Test Brand',
            'slug' => 'test-brand',
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();

        $this->vendor = Vendor::create([
            'user_id' => $this->user->id,
            'shop_name' => 'Test Shop',
            'slug' => 'test-shop',
            'is_approved' => true,
        ]);

        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
        ]);
    }

    /** @test */
    public function vendor_dashboard_component_renders_without_multiple_root_elements_error()
    {
        $this->actingAs($this->user);
        
        $component = Livewire::test(\App\Livewire\Vendor\Dashboard::class);
        
        $component->assertStatus(200);
        $component->assertSee('Welcome Back');
        $component->assertSee('Total Sales');
        $component->assertSee('Total Orders');
        $component->assertSee('Total Products');
    }

    /** @test */
    public function product_options_component_handles_selected_variant_correctly()
    {
        $component = Livewire::test(\App\Livewire\Product\Options::class, ['product' => $this->product]);
        
        $component->assertStatus(200);
        $component->assertSee($this->product->name);
        $component->assertSee('Add to Cart');
    }

    /** @test */
    public function product_card_component_exists_and_renders()
    {
        $component = Livewire::test(\App\Livewire\ProductCard::class, ['product' => $this->product]);
        
        $component->assertStatus(200);
        $component->assertSee($this->product->name);
        $component->assertSee('Add to Cart');
    }

    /** @test */
    public function home_page_renders_with_reduced_banner_height()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertSee('Discover Your');
        // Check that the banner uses the reduced height classes
        $response->assertSee('h-[400px] md:h-[500px]');
    }

    /** @test */
    public function categories_section_renders_with_reduced_height()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertSee('Shop by Category');
        // Check that category images use the reduced height class
        $response->assertSee('h-48');
    }

    /** @test */
    public function x_products_card_component_can_be_used()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        // The component should render without errors
        $response->assertDontSee('Unable to locate a class or view for component [products.card]');
    }
}
