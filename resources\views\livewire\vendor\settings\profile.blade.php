<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Storefront Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 md:p-8 text-gray-900 dark:text-gray-100">
                    <form wire:submit.prevent="save">
                        <!-- Success Message -->
                        @if (session()->has('success'))
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                                <span class="block sm:inline">{{ session('success') }}</span>
                            </div>
                        @endif

                        <!-- Banner Upload -->
                        <div class="mb-8">
                            <label for="banner" class="block text-lg font-semibold text-gray-800 dark:text-gray-200">Store Banner</label>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">This image will appear at the top of your store page. Recommended size: 1600x400px.</p>
                            <div class="mt-4">
                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">Current Banner:</p>
                                    <img src="{{ $vendor->banner_url }}" alt="Current Banner" class="h-48 w-full object-cover rounded-lg shadow-md">
                                </div>
                                
                                <div x-data="{ newBanner: null }" class="mt-4">
                                    <input type="file" id="banner" wire:model="banner" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" @change="newBanner = URL.createObjectURL($event.target.files[0])">
                                    
                                    <div x-show="newBanner" class="mt-4" x-cloak>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">New Banner Preview:</p>
                                        <img :src="newBanner" class="h-48 w-full object-cover rounded-lg shadow-md">
                                    </div>
                                </div>
                                @error('banner') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <!-- About Us -->
                        <div class="mb-8">
                            <label for="about" class="block text-lg font-semibold text-gray-800 dark:text-gray-200">About Your Store</label>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Tell your customers your story. What makes your brand unique?</p>
                            <textarea id="about" wire:model.defer="about" rows="8" class="mt-4 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                            @error('about') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                        </div>

                        <!-- Social Media Links -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Social Links</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Link your social media profiles to your storefront.</p>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                                <div>
                                    <label for="facebook_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Facebook URL</label>
                                    <input type="url" id="facebook_url" wire:model.defer="facebook_url" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="https://facebook.com/your-page">
                                    @error('facebook_url') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                                </div>
                                <div>
                                    <label for="twitter_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Twitter URL</label>
                                    <input type="url" id="twitter_url" wire:model.defer="twitter_url" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="https://twitter.com/your-handle">
                                    @error('twitter_url') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                                </div>
                                <div>
                                    <label for="instagram_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Instagram URL</label>
                                    <input type="url" id="instagram_url" wire:model.defer="instagram_url" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="https://instagram.com/your-username">
                                    @error('instagram_url') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                            <button type="submit" class="inline-flex items-center px-6 py-3 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                                <span wire:loading.remove wire:target="save">Save Changes</span>
                                <span wire:loading wire:target="save">Saving...</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
