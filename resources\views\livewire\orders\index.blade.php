<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    My Orders
                </h1>
                <p class="text-gray-300 text-lg">Track your purchases and order history</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('dashboard') }}" 
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
                <a href="{{ route('products.index') }}" 
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-shopping-cart transition-transform duration-300 group-hover:bounce"></i>
                    <span>Continue Shopping</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Modern Stats Grid --}}
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-shopping-bag text-white text-xl"></i>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {{ number_format($stats['total']) }}
                </p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-clock text-white text-xl"></i>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
                    {{ number_format($stats['pending']) }}
                </p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-cog text-white text-xl"></i>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Processing</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {{ number_format($stats['processing']) }}
                </p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Completed</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                    {{ number_format($stats['completed']) }}
                </p>
            </div>
        </div>

        <div class="group bg-white p-6 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-times-circle text-white text-xl"></i>
                </div>
            </div>
            <div class="space-y-1">
                <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Cancelled</p>
                <p class="text-3xl font-bold text-gray-900 group-hover:text-red-600 transition-colors duration-300">
                    {{ number_format($stats['cancelled']) }}
                </p>
            </div>
        </div>
    </div>

    {{-- Modern Search and Filter Section --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
        <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 lg:space-x-6">
            <div class="w-full lg:w-1/2">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input wire:model.live.debounce.300ms="search" 
                           type="text" 
                           placeholder="Search by Order ID..." 
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300">
                </div>
            </div>

            <div class="w-full lg:w-1/3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-filter text-gray-400"></i>
                    </div>
                    <select wire:model.live="status" 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 appearance-none bg-white">
                        <option value="all">All Orders</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modern Orders List --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Order History</h3>
                    <p class="text-gray-500 mt-1">{{ $orders->total() }} total orders</p>
                </div>
            </div>
        </div>

        @if($orders->count() > 0)
            <div class="divide-y divide-gray-100">
                @foreach($orders as $order)
                    <div class="p-8 hover:bg-gray-50 transition-all duration-300 group">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white font-bold">
                                    #{{ $order->id }}
                                </div>
                                <div>
                                    <h4 class="text-lg font-bold text-gray-900">Order #{{ $order->order_number ?? $order->id }}</h4>
                                    <p class="text-sm text-gray-500">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-gray-900">@currency($order->total)</p>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                        @switch($order->status)
                                            @case('completed') bg-green-100 text-green-800 @break
                                            @case('processing') bg-blue-100 text-blue-800 @break
                                            @case('pending') bg-yellow-100 text-yellow-800 @break
                                            @case('cancelled') bg-red-100 text-red-800 @break
                                            @default bg-gray-100 text-gray-800
                                        @endswitch
                                    ">
                                        <div class="w-2 h-2 rounded-full mr-2
                                            @switch($order->status)
                                                @case('completed') bg-green-500 @break
                                                @case('processing') bg-blue-500 @break
                                                @case('pending') bg-yellow-500 @break
                                                @case('cancelled') bg-red-500 @break
                                                @default bg-gray-500
                                            @endswitch
                                        "></div>
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </div>
                                <a href="{{ route('orders.show', $order) }}" 
                                   class="group inline-flex items-center px-4 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                                    <span>View Details</span>
                                    <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                                </a>
                            </div>
                        </div>

                        {{-- Order Items Preview --}}
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-box text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $order->items->count() }} {{ Str::plural('item', $order->items->count()) }}</p>
                                    <p class="text-xs text-gray-500">in this order</p>
                                </div>
                            </div>
                            
                            @if($order->items->first() && $order->items->first()->product)
                                <div class="flex items-center space-x-3">
                                    <img src="{{ $order->items->first()->product->image_url }}" 
                                         alt="{{ $order->items->first()->product->name }}" 
                                         class="w-8 h-8 rounded-lg object-cover">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ Str::limit($order->items->first()->product->name, 30) }}</p>
                                        <p class="text-xs text-gray-500">and {{ $order->items->count() - 1 }} more</p>
                                    </div>
                                </div>
                            @endif

                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $order->created_at->diffForHumans() }}</p>
                                    <p class="text-xs text-gray-500">order placed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Pagination --}}
            @if ($orders->hasPages())
                <div class="p-8 border-t border-gray-100 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ $orders->firstItem() }} to {{ $orders->lastItem() }} of {{ $orders->total() }} results
                        </div>
                        <div class="pagination-wrapper">
                            {{ $orders->links() }}
                        </div>
                    </div>
                </div>
            @endif
        @else
            <div class="p-16 text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shopping-bag text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">No orders found</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    @if($search || $status !== 'all')
                        No orders match your current search criteria. Try adjusting your filters.
                    @else
                        You haven't placed any orders yet. Start shopping to see your orders here.
                    @endif
                </p>
                <a href="{{ route('products.index') }}" 
                   class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    <span>Start Shopping</span>
                </a>
            </div>
        @endif
    </div>
</div>
