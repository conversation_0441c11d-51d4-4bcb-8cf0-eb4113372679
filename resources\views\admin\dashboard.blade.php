{{-- CRITICAL FIX NA1/AL1: Use traditional layout instead of component-based layout --}}
@extends('layouts.admin')

@section('title', 'Admin Dashboard')

@section('content')

    {{-- Modern Header --}}
    <div class="mb-6 sm:mb-8">
        <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl shadow-2xl">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div class="space-y-2">
                    <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        Welcome Back, Admin
                    </h1>
                    <p class="text-gray-300 text-sm sm:text-base lg:text-lg">Here's what's happening with your platform today</p>
                </div>
                <div class="flex justify-center sm:justify-end">
                    <div class="text-center">
                        <div class="text-xl sm:text-2xl font-bold text-white">{{ now()->format('d') }}</div>
                        <div class="text-xs sm:text-sm text-gray-300">{{ now()->format('M Y') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modern Stats Grid --}}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <!-- Total Sales -->
        <div class="group bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="space-y-1 sm:space-y-2 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wide">Total Sales</p>
                        <p class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 break-words">₦{{ number_format($totalSales, 2) }}</p>
                        <div class="flex items-center text-xs sm:text-sm">
                            <span class="text-green-600 font-medium">+12.5%</span>
                            <span class="text-gray-500 ml-1">from last month</span>
                        </div>
                    </div>
                    <div class="p-2 sm:p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg sm:rounded-xl flex-shrink-0 ml-2">
                        <i class="fas fa-chart-line text-lg sm:text-2xl text-green-600"></i>
                    </div>
                </div>
            </div>
            <div class="h-1 bg-gradient-to-r from-green-500 to-emerald-500"></div>
        </div>

        <!-- Total Orders -->
        <div class="group bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="space-y-1 sm:space-y-2 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wide">Total Orders</p>
                        <p class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">{{ number_format($totalOrders) }}</p>
                        <div class="flex items-center text-xs sm:text-sm">
                            <span class="text-blue-600 font-medium">+8.2%</span>
                            <span class="text-gray-500 ml-1">from last month</span>
                        </div>
                    </div>
                    <div class="p-2 sm:p-3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg sm:rounded-xl flex-shrink-0 ml-2">
                        <i class="fas fa-shopping-cart text-lg sm:text-2xl text-blue-600"></i>
                    </div>
                </div>
            </div>
            <div class="h-1 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
        </div>

        <!-- Total Customers -->
        <div class="group bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="space-y-1 sm:space-y-2 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wide">Total Customers</p>
                        <p class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">{{ number_format($totalCustomers) }}</p>
                        <div class="flex items-center text-xs sm:text-sm">
                            <span class="text-purple-600 font-medium">+15.3%</span>
                            <span class="text-gray-500 ml-1">from last month</span>
                        </div>
                    </div>
                    <div class="p-2 sm:p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg sm:rounded-xl flex-shrink-0 ml-2">
                        <i class="fas fa-users text-lg sm:text-2xl text-purple-600"></i>
                    </div>
                </div>
            </div>
            <div class="h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
        </div>

        <!-- Total Vendors -->
        <div class="group bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="space-y-1 sm:space-y-2 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wide">Total Vendors</p>
                        <p class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">{{ number_format($totalVendors) }}</p>
                        <div class="flex items-center text-xs sm:text-sm">
                            <span class="text-orange-600 font-medium">+5.7%</span>
                            <span class="text-gray-500 ml-1">from last month</span>
                        </div>
                    </div>
                    <div class="p-2 sm:p-3 bg-gradient-to-br from-orange-50 to-red-50 rounded-lg sm:rounded-xl flex-shrink-0 ml-2">
                        <i class="fas fa-store text-lg sm:text-2xl text-orange-600"></i>
                    </div>
                </div>
            </div>
            <div class="h-1 bg-gradient-to-r from-orange-500 to-red-500"></div>
        </div>
    </div>

    {{-- Modern Content Grid --}}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
        <!-- Pending Vendors -->
        <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-clock text-orange-500 mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        <span class="truncate">Pending Vendor Approvals</span>
                    </h3>
                    <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2 sm:px-2.5 py-0.5 rounded-full flex-shrink-0">
                        {{ $pendingVendors->count() }} pending
                    </span>
                </div>
            </div>
            <div class="p-4 sm:p-6">
                @if($pendingVendors->count() > 0)
                    <div class="space-y-3 sm:space-y-4">
                        @foreach($pendingVendors as $vendor)
                            <div class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl hover:bg-gray-100 transition-colors duration-200">
                                <div class="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-gradient-to-r from-gray-600 to-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-store text-white text-sm"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="font-semibold text-gray-900 text-sm sm:text-base truncate">{{ $vendor->shop_name ?? $vendor->user?->name ?? 'Unknown' }}</p>
                                        <p class="text-xs sm:text-sm text-gray-500 truncate">{{ $vendor->user?->email ?? 'No email' }}</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2 flex-shrink-0">
                                    <a href="{{ route('admin.vendors.edit', $vendor) }}"
                                       class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors duration-200 min-h-[36px] flex items-center">
                                        Review
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6 sm:py-8">
                        <i class="fas fa-check-circle text-3xl sm:text-4xl text-green-500 mb-3 sm:mb-4"></i>
                        <p class="text-gray-500 text-sm sm:text-base">No pending vendor approvals</p>
                    </div>
                @endif
            </div>
        </div>
        <!-- Recent Orders -->
        <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-shopping-cart text-blue-500 mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        <span class="truncate">Recent Orders</span>
                    </h3>
                    <a href="{{ route('admin.orders.index') }}"
                       class="text-blue-600 hover:text-blue-800 text-xs sm:text-sm font-medium transition-colors duration-200 flex-shrink-0 min-h-[36px] flex items-center">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-4 sm:p-6">
                @if($recentOrders->count() > 0)
                    <div class="space-y-3 sm:space-y-4">
                        @foreach($recentOrders as $order)
                            <div class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl hover:bg-gray-100 transition-colors duration-200">
                                <div class="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-receipt text-white text-sm"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="font-semibold text-gray-900 text-sm sm:text-base truncate">#{{ $order->order_number ?? $order->id }}</p>
                                        <p class="text-xs sm:text-sm text-gray-500 truncate">{{ $order->user->name ?? 'Guest' }}</p>
                                    </div>
                                </div>
                                <div class="text-right flex-shrink-0">
                                    <p class="font-bold text-gray-900 text-sm sm:text-base">₦{{ number_format($order->total, 2) }}</p>
                                    <span class="inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($order->status === 'completed') bg-green-100 text-green-800
                                        @elseif($order->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6 sm:py-8">
                        <i class="fas fa-shopping-cart text-3xl sm:text-4xl text-gray-400 mb-3 sm:mb-4"></i>
                        <p class="text-gray-500 text-sm sm:text-base">No recent orders</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="mt-8">
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-bolt text-yellow-500 mr-3"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="{{ route('admin.users.create') }}"
                       class="group flex flex-col items-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-user-plus text-2xl text-blue-600 mb-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Add User</span>
                    </a>
                    <a href="{{ route('admin.vendors.create') }}"
                       class="group flex flex-col items-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl hover:from-green-100 hover:to-emerald-100 transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-store text-2xl text-green-600 mb-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Add Vendor</span>
                    </a>
                    <a href="{{ route('admin.categories.index') }}"
                       class="group flex flex-col items-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl hover:from-purple-100 hover:to-pink-100 transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-tags text-2xl text-purple-600 mb-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Categories</span>
                    </a>
                    <a href="{{ route('admin.orders.index') }}"
                       class="group flex flex-col items-center p-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl hover:from-orange-100 hover:to-red-100 transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-chart-bar text-2xl text-orange-600 mb-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Reports</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

@endsection
