{{-- AUTHENTICATION REBUILD: Consistent branding with mobile-first design --}}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        {{-- Consistent Brand Logo --}}
        <div class="flex justify-center mb-6">
            <a href="{{ route('home') }}" class="flex items-center space-x-3" wire:navigate>
                <div class="flex items-center justify-center w-12 h-12 bg-black rounded-xl">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
                <span class="text-2xl font-bold text-gray-900">Brandify</span>
            </a>
        </div>

        <h2 class="text-center text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            {{ __('Create your account') }}
        </h2>
        <p class="text-center text-sm text-gray-600 mb-8">
            {{ __('Join thousands of shoppers or') }}
            <a href="{{ route('login') }}" class="font-medium text-black hover:text-gray-800 underline" wire:navigate>
                {{ __('sign in to your account') }}
            </a>
        </p>
    </div>

    <div class="mx-4 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-6 shadow-xl rounded-2xl border border-gray-100">
            {{-- Status Messages --}}
            @if (session('status') || session('success') || session('error'))
                <div class="mb-6 rounded-xl @if(session('status') || session('success')) bg-green-50 border border-green-200 @else bg-red-50 border border-red-200 @endif p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if(session('status') || session('success'))
                                <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            @else
                                <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium @if(session('status') || session('success')) text-green-800 @else text-red-800 @endif">
                                {{ session('status') ?? session('success') ?? session('error') }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Registration Form with Mobile-First Design --}}
            <form wire:submit="register" class="space-y-6" wire:loading.class="opacity-75">
                @csrf

                {{-- Name Field --}}
                <div>
                    <label for="name" class="block text-sm font-semibold text-gray-900 mb-2">
                        Full Name
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        wire:model="name"
                        autocomplete="name"
                        placeholder="Enter your full name"
                        required
                        autofocus
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('name') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('name')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Email Field --}}
                <div>
                    <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        wire:model="email"
                        autocomplete="email"
                        placeholder="Enter your email address"
                        required
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('email') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('email')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Password Field --}}
                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-900 mb-2">
                        Password
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        wire:model="password"
                        autocomplete="new-password"
                        placeholder="Enter your password"
                        required
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('password') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('password')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Confirm Password Field --}}
                <div>
                    <label for="password_confirmation" class="block text-sm font-semibold text-gray-900 mb-2">
                        Confirm Password
                    </label>
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        wire:model="password_confirmation"
                        autocomplete="new-password"
                        placeholder="Confirm your password"
                        required
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('password_confirmation') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('password_confirmation')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Submit Button with Proper Touch Targets --}}
                <div class="pt-2">
                    <button
                        type="submit"
                        wire:loading.attr="disabled"
                        class="btn-primary w-full flex items-center justify-center py-3 px-4 border border-transparent text-base font-semibold rounded-xl text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 min-h-[44px]"
                    >
                        <span wire:loading.remove class="flex items-center">
                            <i class="fas fa-user-plus mr-2"></i>
                            {{ __('Create Account') }}
                        </span>
                        <span wire:loading class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ __('Creating account...') }}
                        </span>
                    </button>
                </div>
            </form>

            {{-- Additional Links --}}
            <div class="mt-6 space-y-4 text-center">
                <p class="text-sm text-gray-600 dark:text-gray-300">
                    Already have an account?
                    <a href="{{ route('login') }}" wire:navigate class="font-semibold text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 transition duration-150 ease-in-out">
                        Login
                    </a>
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-300">
                    Want to sell on Brandify?
                    <a href="{{ route('vendor.register') }}" wire:navigate class="font-semibold text-green-600 hover:text-green-500 dark:text-green-400 dark:hover:text-green-300 transition duration-150 ease-in-out">
                        Become a Vendor
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
