<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Shipping Settings
                </h1>
                <p class="text-gray-300 text-lg">Manage your shipping addresses and package configurations</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Critical Shipping Information Alert --}}
    <div class="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white p-8 rounded-2xl shadow-2xl mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex items-start space-x-4">
            <div class="p-3 bg-white bg-opacity-20 rounded-xl flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-2xl"></i>
            </div>
            <div class="space-y-4">
                <div>
                    <h2 class="text-2xl font-bold mb-2">🚨 CRITICAL: Shipping Address Setup Required</h2>
                    <p class="text-orange-100 text-lg">Your shipping address is absolutely essential for successful order fulfillment and customer satisfaction.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-xl font-semibold flex items-center space-x-2">
                            <i class="fas fa-shipping-fast"></i>
                            <span>Why This Matters</span>
                        </h3>
                        <ul class="space-y-2 text-orange-100">
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle mt-1 flex-shrink-0"></i>
                                <span>Ensures accurate shipping cost calculations</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle mt-1 flex-shrink-0"></i>
                                <span>Enables proper delivery time estimates</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle mt-1 flex-shrink-0"></i>
                                <span>Required for ShipBubble integration</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle mt-1 flex-shrink-0"></i>
                                <span>Prevents order fulfillment delays</span>
                            </li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-xl font-semibold flex items-center space-x-2">
                            <i class="fas fa-lightbulb"></i>
                            <span>Recommended Business Model</span>
                        </h3>
                        <div class="bg-black bg-opacity-20 p-4 rounded-xl border border-white border-opacity-20">
                            <p class="text-yellow-300 font-semibold mb-2">💡 Sell Pre-Built Products</p>
                            <p class="text-orange-100 text-sm">We strongly encourage vendors to focus on selling pre-built, ready-to-ship products rather than print-on-demand items. This approach:</p>
                            <ul class="mt-2 space-y-1 text-orange-100 text-sm">
                                <li>• Provides faster delivery times</li>
                                <li>• Ensures better quality control</li>
                                <li>• Improves customer satisfaction</li>
                                <li>• Reduces shipping complications</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {{-- Default Shipping Address --}}
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500">
            <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="p-3 bg-blue-500 rounded-xl shadow-lg">
                            <i class="fas fa-map-marker-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Default Shipping Address</h3>
                            <p class="text-gray-600 text-sm">Your primary shipping location</p>
                        </div>
                    </div>
                    <a href="{{ route('vendor.settings.index') }}#shipping"
                       class="group bg-blue-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-600 transition-all duration-300 hover:scale-105 flex items-center space-x-2">
                        <i class="fas fa-edit transition-transform duration-300 group-hover:scale-110"></i>
                        <span>Edit</span>
                    </a>
                </div>
            </div>
            <div class="p-6">
                @if($defaultAddress)
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mt-1">
                                <i class="fas fa-check text-green-600 text-sm"></i>
                            </div>
                            <address class="not-italic text-gray-700 leading-relaxed">
                                <div class="font-semibold text-gray-900 text-lg mb-2">{{ $defaultAddress->recipient_name ?? 'Default Address' }}</div>
                                <div class="space-y-1">
                                    <div>{{ $defaultAddress->line1 ?? $defaultAddress->address ?? 'Address not set' }}</div>
                                    @if($defaultAddress->line2 ?? false)<div>{{ $defaultAddress->line2 }}</div>@endif
                                    <div>{{ $defaultAddress->city ?? 'City not set' }}, {{ $defaultAddress->state ?? 'State not set' }} {{ $defaultAddress->postal_code ?? '' }}</div>
                                    <div class="font-medium">{{ $defaultAddress->country ?? 'Nigeria' }}</div>
                                </div>
                            </address>
                        </div>
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-map-marker-alt text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Default Address Set</h4>
                        <p class="text-gray-500 mb-6">You haven't configured a default shipping address yet.</p>
                        <a href="{{ route('vendor.settings.index') }}#shipping"
                           class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-xl font-medium hover:bg-blue-600 transition-all duration-300 hover:scale-105">
                            <i class="fas fa-plus mr-2"></i>
                            <span>Add Address</span>
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Default Package Sizes -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Default Packages</h3>
                <a href="{{ route('vendor.settings.index') }}#packages" class="text-sm font-medium text-black hover:text-gray-700">Edit</a>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    @forelse($defaultPackages as $pkg)
                        <div class="flex justify-between items-center text-gray-700">
                            <span>{{ $pkg['name'] }} ({{ $pkg['length'] }}x{{ $pkg['width'] }}x{{ $pkg['height'] }} cm)</span>
                            <span class="font-medium">{{ $pkg['weight'] }} kg</span>
                        </div>
                    @empty
                        <p class="text-gray-500">No default package sizes configured.</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Recent Shipments Section -->
    <div class="mt-6 sm:mt-8 bg-white rounded-xl lg:rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-4 sm:p-6 border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="p-2 sm:p-3 bg-green-500 rounded-lg sm:rounded-xl shadow-lg">
                        <i class="fas fa-shipping-fast text-white text-lg sm:text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg sm:text-xl font-bold text-gray-900">Recent Shipments</h3>
                        <p class="text-gray-600 text-sm">Track your shipped orders</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-2xl font-bold text-green-600">{{ count($recentShipments) }}</p>
                    <p class="text-xs text-gray-500">Total Shipped</p>
                </div>
            </div>
        </div>

        <!-- Mobile Card View (Hidden on Desktop) -->
        <div class="block lg:hidden">
            @forelse($recentShipments as $ship)
                <div class="p-4 border-b border-gray-100 hover:bg-gray-50 transition-all duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900 text-sm">{{ $ship->customer_name }}</p>
                                <p class="text-xs text-gray-500">Order #{{ $ship->order->id }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                {{ $ship->status === 'Delivered' ? 'bg-green-100 text-green-800' :
                                   ($ship->status === 'In Transit' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800') }}">
                                <i class="fas {{ $ship->status === 'Delivered' ? 'fa-check-circle' :
                                              ($ship->status === 'In Transit' ? 'fa-truck' : 'fa-clock') }} mr-1"></i>
                                {{ $ship->status }}
                            </span>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Tracking #:</span>
                            <span class="font-medium text-gray-900">{{ $ship->tracking_number }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Carrier:</span>
                            <span class="font-medium text-gray-900">{{ strtoupper($ship->carrier) }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Shipped Date:</span>
                            <span class="font-medium text-gray-900">{{ $ship->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Items:</span>
                            <span class="font-medium text-gray-900">{{ $ship->item_count }} item(s)</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Order Total:</span>
                            <span class="font-bold text-gray-900">₦{{ number_format($ship->total_amount, 2) }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Address:</span>
                            <span class="font-medium text-gray-900 text-right text-xs">{{ Str::limit($ship->shipping_address, 30) }}</span>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-100 space-y-2">
                        <div class="flex space-x-2">
                            @if($ship->can_track)
                                <a href="{{ $ship->tracking_url }}" target="_blank"
                                   class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-black text-white text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    Track
                                </a>
                                <button wire:click="refreshTrackingStatus({{ $ship->order->id }})"
                                        class="px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            @else
                                <button wire:click="markAsShipped({{ $ship->order->id }})"
                                        class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-500 text-white text-sm font-medium rounded-lg hover:bg-green-600 transition-colors">
                                    <i class="fas fa-truck mr-2"></i>
                                    Mark as Shipped
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shipping-fast text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No shipments yet</h3>
                    <p class="text-gray-500">Shipped orders will appear here for tracking.</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on Mobile) -->
        <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Order Details</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Tracking #</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Carrier</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($recentShipments as $ship)
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <div>
                                    <p class="font-semibold text-gray-900">{{ $ship->customer_name }}</p>
                                    <p class="text-gray-500">Order #{{ $ship->order->id }} • {{ $ship->item_count }} item(s)</p>
                                    <p class="text-gray-600 font-medium">₦{{ number_format($ship->total_amount, 2) }}</p>
                                    <p class="text-xs text-gray-400 mt-1">{{ Str::limit($ship->shipping_address, 40) }}</p>
                                </div>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm font-medium">{{ $ship->tracking_number }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ strtoupper($ship->carrier) }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $ship->status === 'Delivered' ? 'bg-green-100 text-green-800' :
                                       ($ship->status === 'In Transit' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800') }}">
                                    <i class="fas {{ $ship->status === 'Delivered' ? 'fa-check-circle' :
                                                  ($ship->status === 'In Transit' ? 'fa-truck' : 'fa-clock') }} mr-1"></i>
                                    {{ $ship->status }}
                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $ship->created_at->format('M d, Y g:i A') }}</td>
                            <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-right">
                                <div class="flex items-center justify-end space-x-2">
                                    @if($ship->can_track)
                                        <a href="{{ $ship->tracking_url }}" target="_blank"
                                           class="inline-flex items-center px-3 py-1 bg-black text-white text-xs font-medium rounded-lg hover:bg-gray-800 transition-colors">
                                            <i class="fas fa-external-link-alt mr-1"></i>
                                            Track
                                        </a>
                                        <button wire:click="refreshTrackingStatus({{ $ship->order->id }})"
                                                class="inline-flex items-center px-2 py-1 bg-blue-500 text-white text-xs font-medium rounded-lg hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    @else
                                        <button wire:click="markAsShipped({{ $ship->order->id }})"
                                                class="inline-flex items-center px-3 py-1 bg-green-500 text-white text-xs font-medium rounded-lg hover:bg-green-600 transition-colors">
                                            <i class="fas fa-truck mr-1"></i>
                                            Ship
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center py-12">
                                <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shipping-fast text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No shipments yet</h3>
                                <p class="text-gray-500">Shipped orders will appear here for tracking.</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
