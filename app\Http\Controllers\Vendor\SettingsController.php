<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    /**
     * Show the vendor settings edit form
     */
    public function edit()
    {
        $vendor = Auth::user()->vendor;
        
        // Load vendor with relationships
        $vendor->load('user');

        return view('vendor.settings.edit', compact('vendor'));
    }

    /**
     * Show the vendor profile settings
     */
    public function profile()
    {
        $vendor = Auth::user()->vendor;
        
        return view('vendor.settings.profile', compact('vendor'));
    }
}
