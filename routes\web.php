<?php

use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use App\Livewire\Vendor\Dashboard as VendorDashboard;
use App\Livewire\Vendor\Orders\Index as VendorOrdersIndex;
use App\Livewire\Vendor\Orders\Show as VendorOrdersShow;
use App\Livewire\Vendor\Shipping\Index as VendorShippingIndex;
use App\Livewire\Vendor\Earnings\Index as VendorEarningsIndex;
use App\Livewire\Vendor\Profile\Index as VendorProfileIndex;
use App\Livewire\Vendor\Subscription\Index as VendorSubscriptionIndex;
use App\Livewire\Vendor\Settings\Index as VendorSettingsIndex;
use App\Livewire\Vendor\Settings\Profile as VendorSettingsProfile;
use App\Livewire\Vendor\Reviews\Index as VendorReviewsIndex;
use App\Livewire\Vendor\Pending\Index as VendorPendingIndex;
use App\Livewire\Vendor\Onboarding\Index as VendorOnboardingIndex;
use App\Livewire\Vendor\Products\Index as VendorProductsIndex;
use App\Livewire\Vendor\Products\CreateProduct as VendorCreateProduct;
use App\Livewire\Vendor\Products\EditProduct as VendorEditProduct;
use App\Livewire\Auth\Vendor\Register;
use App\Livewire\Admin\Products\Index as AdminProductsIndex;
use App\Livewire\Admin\Products\CreateProduct as AdminCreateProduct;
use App\Livewire\Admin\Products\EditProduct as AdminEditProduct;
use App\Livewire\Admin\Vendors\Index as AdminVendorsIndex;
use App\Livewire\Admin\Vendors\VendorForm as AdminVendorForm;
use App\Livewire\Admin\Users\Index as AdminUsersIndex;
use App\Livewire\Admin\Users\UserForm as AdminUserForm;
use App\Livewire\Admin\Commissions\Index as AdminCommissionsIndex;
use App\Livewire\Admin\Withdrawals\Index as AdminWithdrawalsIndex;
use App\Livewire\Admin\Subscriptions\Index as AdminSubscriptionsIndex;
use App\Livewire\Admin\SubscriptionPlans\Index as AdminSubscriptionPlansIndex;
use App\Livewire\Admin\Payments\Index as AdminPaymentsIndex;
use App\Livewire\Admin\Brands\Index as AdminBrandsIndex;
use App\Livewire\Admin\Categories\Index as AdminCategoriesIndex;
use App\Livewire\Admin\Orders\Index as AdminOrdersIndex;
use App\Livewire\Admin\Orders\Show as AdminOrdersShow;
use App\Livewire\Cart\Index;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home'); // Uses dynamic welcome-bw.blade.php

// Debug route for cart data (remove in production)
Route::get('/debug/cart', function() {
    $cart = session()->get('cart', []);
    return response()->json([
        'cart_type' => gettype($cart),
        'cart_class' => is_object($cart) ? get_class($cart) : 'not_object',
        'cart_count' => is_countable($cart) ? count($cart) : 'not_countable',
        'cart_data' => $cart,
        'session_id' => session()->getId(),
        'is_array' => is_array($cart),
        'is_collection' => $cart instanceof \Illuminate\Support\Collection
    ]);
})->name('debug.cart');

// Static Pages
Route::view('/about', 'pages.about')->name('about');
Route::view('/contact', 'pages.contact')->name('contact');
Route::post('/contact/submit', [App\Http\Controllers\HomeController::class, 'handleContactForm'])->name('contact.submit');

// Products Routes
Route::get('/shop', \App\Livewire\Product\Index::class)->name('products.index');
Route::get('/category/{category:slug}', \App\Livewire\Product\CategoryProducts::class)->name('products.category');
Route::get('/products/{product:slug}', \App\Livewire\Product\Show::class)->name('products.show');
Route::get('/search', \App\Livewire\Product\Search::class)->name('products.search');
Route::post('/product/{product:slug}/reviews', [App\Http\Controllers\ReviewController::class, 'store'])->name('products.reviews.store')->middleware(['auth', 'throttle:5,1']);

// Vendor Storefront
Route::get('/vendors/{vendor:slug}', \App\Livewire\Vendor\Storefront::class)->name('vendors.storefront');

// Cart Routes
Route::get('/cart', Index::class)->name('cart.index');
Route::post('/cart/add/{product}', [\App\Http\Controllers\CartController::class, 'add'])->name('cart.add');


// Pricing Page
Route::get('/pricing', \App\Livewire\Pricing\Index::class)->name('pricing');

// Vendor Registration and Onboarding
Route::get('/become-a-vendor', \App\Livewire\Auth\Vendor\Register::class)->name('vendor.register');

Route::get('/store/{slug}', [App\Http\Controllers\VendorController::class, 'show'])->name('vendor.show');

// Customer Routes
Route::middleware(['auth', 'verified'])->group(function () {
        Route::get('/dashboard', \App\Livewire\Dashboard\Index::class)->name('dashboard');

    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Wishlist Routes
    Route::get('/wishlist', \App\Livewire\Wishlist\Index::class)->name('wishlist.index');

    // ROUTE FIX: Add missing wishlist toggle route for product cards
    Route::post('/wishlist/toggle/{product}', function(\App\Models\Product $product) {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to manage your wishlist.');
        }

        $user = Auth::user();
        $wishlistItem = $user->wishlist()->where('product_id', $product->id)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            return back()->with('success', 'Product removed from wishlist.');
        } else {
            $user->wishlist()->create(['product_id' => $product->id]);
            return back()->with('success', 'Product added to wishlist.');
        }
    })->name('wishlist.toggle');

    Route::post('/wishlist/add/{product}', function(\App\Models\Product $product) {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to add to wishlist.');
        }

        $user = Auth::user();
        if (!$user->wishlist()->where('product_id', $product->id)->exists()) {
            $user->wishlist()->create(['product_id' => $product->id]);
            return back()->with('success', 'Product added to wishlist.');
        }

        return back()->with('info', 'Product is already in your wishlist.');
    })->name('wishlist.add');

    Route::delete('/wishlist/remove/{product}', function(\App\Models\Product $product) {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $wishlistItem = $user->wishlist()->where('product_id', $product->id)->first();

        if ($wishlistItem) {
            $wishlistItem->delete();
            return back()->with('success', 'Product removed from wishlist.');
        }

        return back()->with('error', 'Product not found in wishlist.');
    })->name('wishlist.remove');

    // Order Routes
    Route::get('/orders', \App\Livewire\Orders\Index::class)->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    // Note: Order cancellation removed - customers cannot cancel orders directly
    Route::post('/orders/{order}/return', [App\Http\Controllers\OrderController::class, 'requestReturn'])->name('orders.return');

    // Checkout Routes
        Route::get('/checkout', \App\Livewire\Checkout\Index::class)->name('checkout.index');

    // Debug route for array error testing
    Route::get('/debug-checkout', function () {
        try {
            // Test cart session data
            $cart = session('cart', []);
            \Log::info('Debug checkout - cart data', [
                'cart_type' => gettype($cart),
                'cart_data' => $cart,
                'cart_count' => is_array($cart) ? count($cart) : 'not_array'
            ]);

            return response()->json([
                'status' => 'success',
                'cart_type' => gettype($cart),
                'cart_count' => is_array($cart) ? count($cart) : 'not_array',
                'message' => 'Checkout debug completed successfully'
            ]);
        } catch (\Exception $e) {
            \Log::error('Debug checkout error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    });

    // Minimal checkout test route
    Route::get('/test-checkout-minimal', function () {
        try {
            // Test just the basic view rendering without Livewire
            return view('test-checkout-minimal');
        } catch (\Exception $e) {
            \Log::error('Minimal checkout test error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    });

    // Debug Livewire checkout component
    Route::get('/debug-livewire-checkout', \App\Livewire\DebugCheckout::class);





    // Shipping Routes
    Route::post('/shipping/rates', [\App\Http\Controllers\ShippingController::class, 'getRates'])->name('shipping.rates');

    // Payment Routes with rate limiting
    Route::get('/payment/initialize', [\App\Http\Controllers\PaymentController::class, 'initialize'])->name('payment.initialize');
    Route::middleware(['throttle:10,1'])->group(function () {
        Route::post('/payment/paystack/initialize', [\App\Http\Controllers\PaymentController::class, 'initializePaystack'])->name('payment.paystack.initialize');
    });
    Route::get('/payment/paystack/callback', [\App\Http\Controllers\PaymentController::class, 'handlePaystackCallback'])->name('payment.paystack.callback');
        Route::get('/checkout/success/{transaction_id?}', \App\Livewire\Checkout\Success::class)->name('checkout.success');
});

// Vendor Routes

Route::middleware(['auth', 'vendor'])->prefix('vendor')->name('vendor.')->group(function () {
    Route::get('/onboarding', VendorOnboardingIndex::class)->name('onboarding.index');
});

Route::middleware(['auth', 'vendor', 'approved.vendor', 'vendor.subscription'])->prefix('vendor')->name('vendor.')->group(function () {
    // Dashboard
    Route::get('/dashboard', VendorDashboard::class)->name('dashboard');
    

    // Products Management
    Route::get('/products', VendorProductsIndex::class)->name('products.index');
    // FIXED: Use Livewire components for consistent SPA-like experience
    Route::get('/products/create', VendorCreateProduct::class)->name('products.create');
    Route::get('/products/{product:slug}/edit', [\App\Http\Controllers\Vendor\ProductController::class, 'edit'])->name('products.edit');
    Route::get('/products/{product:slug}/variants', \App\Livewire\Vendor\Products\Variants::class)->name('products.variants');
    Route::delete('/products/{product}', [\App\Http\Controllers\Vendor\ProductController::class, 'destroy'])->name('products.destroy');
    
    // Orders Management
    Route::get('/orders', VendorOrdersIndex::class)->name('orders.index');
    Route::get('/orders/{order}', VendorOrdersShow::class)->name('orders.show');

    
    // Shipping
        Route::get('/shipping', VendorShippingIndex::class)->name('shipping.index');

    // Earnings & Commissions
    Route::get('/earnings', VendorEarningsIndex::class)->name('earnings.index');
    Route::get('/financials', \App\Livewire\Vendor\Dashboard\FinancialDashboard::class)->name('financials.dashboard');
    
    // Profile Management
    Route::get('/profile', [\App\Http\Controllers\Vendor\ProfileController::class, 'edit'])->name('profile');

    // Subscription Management
    Route::get('/subscription', VendorSubscriptionIndex::class)->name('subscription.index');
    Route::get('/subscription/callback', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'handleCallback'])->name('subscription.callback');

    // Reviews
    Route::get('/reviews', VendorReviewsIndex::class)->name('reviews.index');

    // Profile & Settings
    Route::get('/settings', [\App\Http\Controllers\Vendor\SettingsController::class, 'edit'])->name('settings.index');
    Route::get('/settings/profile', [\App\Http\Controllers\Vendor\SettingsController::class, 'profile'])->name('settings.profile');
    

    
    // Pending Approval
    Route::get('/pending', VendorPendingIndex::class)->name('pending')->withoutMiddleware(['approved.vendor', 'vendor.subscription']);
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin root redirect
    Route::get('/', function () {
        return redirect()->route('admin.dashboard');
    });

    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    
    // User Management
    Route::get('users', AdminUsersIndex::class)->name('users.index');
    Route::get('users/create', [\App\Http\Controllers\Admin\UserController::class, 'create'])->name('users.create');
    Route::get('users/{user}', \App\Livewire\Admin\Users\Show::class)->name('users.show');
    Route::get('users/{user}/edit', [\App\Http\Controllers\Admin\UserController::class, 'edit'])->name('users.edit');
    Route::delete('users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('users.destroy');

    // Vendor Management
    Route::get('vendors', AdminVendorsIndex::class)->name('vendors.index');
    Route::get('vendors/create', [\App\Http\Controllers\Admin\VendorController::class, 'create'])->name('vendors.create');
    Route::get('vendors/{vendor}', \App\Livewire\Admin\Vendors\Show::class)->name('vendors.show');
    Route::get('vendors/{vendor}/edit', [\App\Http\Controllers\Admin\VendorController::class, 'edit'])->name('vendors.edit');
    Route::delete('vendors/{vendor}', [\App\Http\Controllers\Admin\VendorController::class, 'destroy'])->name('vendors.destroy');
    // Product Management
   Route::get('/products', AdminProductsIndex::class)->name('products.index');
   Route::get('/products/create', [\App\Http\Controllers\Admin\ProductController::class, 'create'])->name('products.create');
   Route::get('/products/{product:slug}', [\App\Http\Controllers\Admin\ProductController::class, 'show'])->name('products.show');
   Route::get('/products/{product:slug}/edit', [\App\Http\Controllers\Admin\ProductController::class, 'edit'])->name('products.edit');
   Route::delete('/products/{product}', [\App\Http\Controllers\Admin\ProductController::class, 'destroy'])->name('products.destroy');
   Route::get('/products/best-sellers', \App\Livewire\Admin\Products\BestSellerManager::class)->name('products.best-sellers');
   
    // Brand Management
    Route::get('brands', AdminBrandsIndex::class)->name('brands.index');
    Route::get('brands/create', [\App\Http\Controllers\Admin\BrandController::class, 'create'])->name('brands.create');
    Route::get('brands/{brand}/edit', [\App\Http\Controllers\Admin\BrandController::class, 'edit'])->name('brands.edit');
    Route::delete('brands/{brand}', [\App\Http\Controllers\Admin\BrandController::class, 'destroy'])->name('brands.destroy');

    // Category Management
    Route::get('categories', AdminCategoriesIndex::class)->name('categories.index');
    Route::get('categories/create', [\App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('categories.create');
    Route::get('categories/{category}/edit', [\App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('categories.edit');
    Route::delete('categories/{category}', [\App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('categories.destroy');

    // Color Management
    Route::get('colors', \App\Livewire\Admin\Colors\Index::class)->name('colors.index');

    // Size Management
    Route::get('sizes', \App\Livewire\Admin\Sizes\Index::class)->name('sizes.index');
    
    // Order Management
    Route::get('orders', AdminOrdersIndex::class)->name('orders.index');
    Route::get('orders/{order}', AdminOrdersShow::class)->name('orders.show');
    
    // Commission Management
    Route::get('commissions', AdminCommissionsIndex::class)->name('commissions.index');
    
    // Subscription Management
    Route::get('subscriptions', AdminSubscriptionsIndex::class)->name('subscriptions.index');
    
    // Subscription Plan Management
    Route::get('subscription-plans', AdminSubscriptionPlansIndex::class)->name('subscription-plans.index');
    Route::get('subscription-plans/create', \App\Livewire\Admin\SubscriptionPlans\SubscriptionPlanForm::class)->name('subscription-plans.create');
    Route::get('subscription-plans/{subscriptionPlan}/edit', \App\Livewire\Admin\SubscriptionPlans\SubscriptionPlanForm::class)->name('subscription-plans.edit');
    Route::delete('subscription-plans/{subscriptionPlan}', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'destroy'])->name('subscription-plans.destroy');
    
    // Payment Management
    Route::get('payments', AdminPaymentsIndex::class)->name('payments.index');

    // Withdrawal Management
    Route::get('withdrawals', AdminWithdrawalsIndex::class)->name('withdrawals.index');
    
    // Settings
    // FIXED: Corrected route name to follow consistent naming pattern
    Route::get('/settings', \App\Livewire\Admin\Settings\Index::class)->name('settings.index');
    
    // Profile
    Route::get('/profile', function () {
        return view('admin.profile');
    })->name('profile');
});

// CRITICAL FIX: Consolidated Paystack Webhook (Paystack only allows ONE webhook URL per application)
Route::middleware(['throttle:60,1'])->group(function () {
    Route::post('/paystack/webhook', [\App\Http\Controllers\WebhookController::class, 'handlePaystackWebhook'])->name('paystack.webhook');
});

// Webhook test endpoint (consolidated)
Route::match(['GET', 'POST'], '/paystack/webhook/test', [\App\Http\Controllers\WebhookController::class, 'testWebhook'])->name('paystack.webhook.test');

// DEPRECATED: Old separate webhook endpoints (kept temporarily for transition)
// TODO: Remove these after confirming consolidated webhook works
// Route::post('/paystack/webhook/old', [\App\Http\Controllers\PaymentController::class, 'handlePaystackWebhook'])->name('paystack.webhook.old');
// Route::post('/paystack/subscription/webhook/old', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'handleWebhook'])->name('paystack.subscription.webhook.old');

require __DIR__.'/auth.php';
