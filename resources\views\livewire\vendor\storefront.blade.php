<div class="min-h-screen bg-gray-50">
    <!-- Mobile-First Vendor Storefront -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        <!-- Enhanced Storefront Header with Mobile Optimization -->
        <div class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-2xl shadow-2xl overflow-hidden mb-6 sm:mb-12">
            <!-- Banner Background -->
            <div class="h-48 sm:h-64 lg:h-80 bg-cover bg-center relative"
                 style="background-image: url('{{ $vendor->banner_url ?? asset('images/default-banner.jpg') }}');">
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
            </div>

            <!-- Vendor Info Overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-4 sm:p-6 lg:p-8">
                <div class="flex flex-col sm:flex-row items-center sm:items-end space-y-4 sm:space-y-0 sm:space-x-6">
                    <!-- Vendor Logo -->
                    <div class="relative">
                        <img src="{{ $vendor->logo_url ?? asset('images/default-vendor-logo.png') }}"
                             alt="{{ $vendor->shop_name }} Logo"
                             class="h-20 w-20 sm:h-28 sm:w-28 lg:h-32 lg:w-32 rounded-full object-cover border-4 border-white shadow-xl">
                        @if($vendor->is_featured)
                            <div class="absolute -top-2 -right-2 bg-yellow-400 text-black text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                                ⭐ Featured
                            </div>
                        @endif
                    </div>

                    <!-- Vendor Details -->
                    <div class="flex-1 text-center sm:text-left">
                        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-extrabold text-white tracking-tight mb-2">
                            {{ $vendor->shop_name }}
                        </h1>
                        <p class="text-gray-300 text-sm sm:text-base lg:text-lg mb-3 max-w-2xl">
                            {{ $vendor->business_description ?? 'Welcome to our store!' }}
                        </p>

                        <!-- Vendor Stats -->
                        <div class="flex flex-wrap items-center justify-center sm:justify-start gap-4 mb-4">
                            <div class="flex items-center text-white/80 text-sm">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Verified Vendor
                            </div>
                            <div class="flex items-center text-white/80 text-sm">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                                {{ $this->products->total() }} Products
                            </div>
                            <div class="flex items-center text-white/80 text-sm">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ $vendor->city }}, {{ $vendor->state }}
                            </div>
                        </div>

                        <!-- Social Links -->
                        <div class="flex items-center justify-center sm:justify-start space-x-4">
                            @if($vendor->website)
                                <a href="{{ $vendor->website }}" target="_blank"
                                   class="p-2 bg-white/10 hover:bg-white/20 rounded-full text-white hover:text-yellow-400 transition-all duration-300 backdrop-blur-sm">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                </a>
                            @endif
                            @if($vendor->facebook_url)
                                <a href="{{ $vendor->facebook_url }}" target="_blank"
                                   class="p-2 bg-white/10 hover:bg-blue-600/20 rounded-full text-white hover:text-blue-400 transition-all duration-300 backdrop-blur-sm">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
                                    </svg>
                                </a>
                            @endif
                            @if($vendor->twitter_url)
                                <a href="{{ $vendor->twitter_url }}" target="_blank"
                                   class="p-2 bg-white/10 hover:bg-blue-400/20 rounded-full text-white hover:text-blue-300 transition-all duration-300 backdrop-blur-sm">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"></path>
                                    </svg>
                                </a>
                            @endif
                            @if($vendor->instagram_url)
                                <a href="{{ $vendor->instagram_url }}" target="_blank"
                                   class="p-2 bg-white/10 hover:bg-pink-500/20 rounded-full text-white hover:text-pink-400 transition-all duration-300 backdrop-blur-sm">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.247 7.053 7.757 8.35 7.757s2.448.49 3.323 1.297c.897.897 1.387 2.048 1.387 3.345s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.897-1.387-2.048-1.387-3.345s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.897.897 1.387 2.048 1.387 3.345s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"></path>
                                    </svg>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile-First Content Layout -->
        <div class="space-y-6 lg:space-y-8">
            <!-- About Section (Mobile-First) -->
            @if($vendor->about)
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-4 sm:p-6 lg:p-8">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h2 class="text-xl sm:text-2xl font-bold text-gray-900">About {{ $vendor->shop_name }}</h2>
                        </div>
                        <div class="prose prose-gray max-w-none text-sm sm:text-base leading-relaxed">
                            <p class="text-gray-600">{{ $vendor->about }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Categories Filter (Mobile-Optimized) -->
            @if($this->categories->count() > 0)
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-4 sm:p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            Shop by Category
                        </h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($this->categories as $category)
                                <a href="{{ route('products.index', ['category' => $category->slug, 'vendor' => $vendor->slug]) }}"
                                   class="inline-flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 rounded-full text-sm font-medium transition-colors duration-200">
                                    {{ $category->name }}
                                    <span class="ml-2 px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full text-xs">
                                        {{ $category->products_count }}
                                    </span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Products Section -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div class="p-4 sm:p-6 border-b border-gray-100">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <h2 class="text-xl sm:text-2xl font-bold text-gray-900 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            Our Products
                        </h2>
                        <div class="text-sm text-gray-500">
                            {{ $this->products->total() }} {{ Str::plural('product', $this->products->total()) }} available
                        </div>
                    </div>
                </div>

                <div class="p-4 sm:p-6">
                    <!-- Products Grid (Mobile-First) -->
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                        @forelse ($this->products as $product)
                            <livewire:product-card :product="$product" :key="'storefront-product-'.$product->id" />
                        @empty
                            <div class="col-span-2 sm:col-span-2 md:col-span-3 lg:col-span-4 text-center py-12">
                                <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No products yet</h3>
                                <p class="text-gray-500">This vendor hasn't added any products to their store.</p>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    @if($this->products->hasPages())
                        <div class="mt-8 border-t border-gray-100 pt-6">
                            {{ $this->products->links() }}
                        </div>
                    @endif
                </div>
            </div>
    </div>
</div>
