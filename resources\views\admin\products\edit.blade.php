@extends('layouts.admin')

@section('title', 'Edit Product')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Product: {{ $product->name }}</h1>
            <a href="{{ route('admin.products.index') }}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Products
            </a>
        </div>

        <!-- Success/Error Messages -->
        @if (session('success'))
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif

        @if (session('error'))
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
        @endif

        <!-- Product Information Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white mb-4">Product Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-500">Current Vendor:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->vendor->shop_name ?? 'No vendor' }}</p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-500">Current Category:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->category->name ?? 'No category' }}</p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-500">Current Brand:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->brand->name ?? 'No brand' }}</p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-500">Created:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->created_at->format('M d, Y') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Livewire Form Component -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <livewire:admin.product-edit-form :product="$product" :vendors="$vendors" :categories="$categories" :brands="$brands" />
            </div>
        </div>
    </div>
</div>
@endsection
