<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <!-- Header with back button -->
        <div class="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Product</h1>
            <a href="{{ route('admin.products.index') }}" 
               class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Products
            </a>
        </div>

        <form wire:submit.prevent="save" class="space-y-8">
            <!-- Main content grid -->
            <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
                <!-- Left column - Product Details -->
                <div class="space-y-6 lg:col-span-2">
                    <!-- Product Details Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Product Details</h3>
                            <div class="mt-6 space-y-4">
                                <!-- Product Name -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Product Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="name" wire:model.defer="name"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="Enter product name" required>
                                    @error('name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Description -->
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Description <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="description" wire:model.defer="description" rows="3"
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                              placeholder="Enter product description" required></textarea>
                                    @error('description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Stock Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Pricing & Stock</h3>
                            <div class="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
                                <!-- Price -->
                                <div>
                                    <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">₦</span>
                                        </div>
                                        <input type="number" id="price" wire:model.defer="price" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00" required>
                                    </div>
                                    @error('price') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Discount Price -->
                                <div>
                                    <label for="discount_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Discount Price
                                    </label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">₦</span>
                                        </div>
                                        <input type="number" id="discount_price" wire:model.defer="discount_price" step="0.01" min="0"
                                               class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                               placeholder="0.00">
                                    </div>
                                    @error('discount_price') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Stock -->
                                <div>
                                    <label for="stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Stock Quantity <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" id="stock" wire:model.defer="stock" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0" required>
                                    @error('stock') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Dimensions Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Shipping Dimensions</h3>
                            <div class="mt-6 grid grid-cols-2 gap-4 sm:grid-cols-4">
                                <!-- Weight -->
                                <div>
                                    <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Weight (kg)
                                    </label>
                                    <input type="number" id="weight" wire:model.defer="weight" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('weight') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Length -->
                                <div>
                                    <label for="length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Length (cm)
                                    </label>
                                    <input type="number" id="length" wire:model.defer="length" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('length') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Width -->
                                <div>
                                    <label for="width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Width (cm)
                                    </label>
                                    <input type="number" id="width" wire:model.defer="width" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('width') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Height -->
                                <div>
                                    <label for="height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Height (cm)
                                    </label>
                                    <input type="number" id="height" wire:model.defer="height" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500"
                                           placeholder="0.00">
                                    @error('height') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Variants Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Product Variants</h3>
                                <button type="button" wire:click="addVariant" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                    <svg class="-ml-0.5 mr-1.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    Add Variant
                                </button>
                            </div>
                            <div class="mt-6 space-y-6">
                                @forelse ($variants as $index => $variant)
                                    <div wire:key="variant-{{ $index }}" class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg relative">
                                        <button type="button" wire:click="removeVariant({{ $index }})" class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                            <div class="flex flex-col items-center justify-center space-y-2">
                                                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Image</label>
                                                @if (isset($variants[$index]['image']) && $variants[$index]['image'])
                                                    <img src="{{ $variants[$index]['image']->temporaryUrl() }}" class="h-20 w-20 object-cover rounded-lg shadow-md" alt="New Variant Image Preview">
                                                @elseif (isset($variant['existing_image_url']) && $variant['existing_image_url'])
                                                    <img src="{{ $variant['existing_image_url'] }}" class="h-20 w-20 object-cover rounded-lg shadow-md" alt="Existing Variant Image">
                                                @else
                                                    <div class="h-20 w-20 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                                        <i class="fas fa-image text-gray-400 text-2xl"></i>
                                                    </div>
                                                @endif
                                                <div class="flex items-center space-x-2">
                                                    <label for="variant-image-{{ $index }}" class="cursor-pointer text-sm text-blue-600 dark:text-blue-400 hover:underline">
                                                        {{ (isset($variant['existing_image_url']) && $variant['existing_image_url']) || (isset($variants[$index]['image']) && $variants[$index]['image']) ? 'Change' : 'Upload' }}
                                                    </label>
                                                    <input type="file" id="variant-image-{{ $index }}" wire:model="variants.{{ $index }}.image" class="sr-only">
                                                    @if ((isset($variant['existing_image_url']) && $variant['existing_image_url']) || (isset($variants[$index]['image']) && $variants[$index]['image']))
                                                        <button type="button" wire:click="removeVariantImage({{ $index }})" class="text-xs text-red-500 hover:underline">
                                                            Remove
                                                        </button>
                                                    @endif
                                                </div>
                                                <div wire:loading wire:target="variants.{{ $index }}.image" class="text-xs text-gray-500">Uploading...</div>
                                                @error("variants.{$index}.image") <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                            </div>
                                            <div class="space-y-2">
                                                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Type</label>
                                                <input type="text" wire:model.live="variants.{{ $index }}.name" class="w-full text-sm rounded-md border-gray-300 shadow-sm dark:bg-gray-700 dark:border-gray-600" placeholder="e.g., Color">
                                            </div>
                                            <div class="space-y-2">
                                                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Value</label>
                                                <input type="text" wire:model.live="variants.{{ $index }}.value" class="w-full text-sm rounded-md border-gray-300 shadow-sm dark:bg-gray-700 dark:border-gray-600" placeholder="e.g., Red">
                                            </div>
                                            <div class="space-y-2">
                                                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Price</label>
                                                <input type="number" step="0.01" wire:model.live="variants.{{ $index }}.price" class="w-full text-sm rounded-md border-gray-300 shadow-sm dark:bg-gray-700 dark:border-gray-600" placeholder="0.00">
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <p class="text-center text-gray-500 dark:text-gray-400">No variants added. Click 'Add Variant' to get started.</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right column - Organization & Media -->
                <div class="space-y-6">
                    <!-- Organization Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Organization</h3>
                            <div class="mt-6 space-y-4">
                                <!-- Vendor -->
                                <div>
                                    <label for="vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Vendor <span class="text-red-500">*</span>
                                    </label>
                                    <select id="vendor_id" wire:model="vendor_id"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500">
                                        <option value="">Select a vendor</option>
                                        @foreach($vendors as $vendor)
                                            <option value="{{ $vendor['id'] }}">{{ $vendor['shop_name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('vendor_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <select id="category_id" wire:model.defer="category_id"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-gray-500">
                                        <option value="">Select a category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Media & Status Card -->
                    <div class="overflow-hidden bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Media & Status</h3>
                            <div class="mt-6 space-y-4">
                                <!-- Main Image -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Main Image
                                    </label>
                                    @if($image)
                                        <div class="mt-2">
                                            <img src="{{ $image->temporaryUrl() }}" alt="Preview" class="h-32 w-auto object-cover rounded-md">
                                        </div>
                                    @elseif($product->hasMedia('product_images'))
                                        <div class="mt-2">
                                            <img src="{{ $product->getFirstMediaUrl('product_images') }}" alt="Current Image" class="h-32 w-auto object-cover rounded-md">
                                        </div>
                                    @endif
                                    <div class="mt-2 flex items-center">
                                        <label for="image-upload" class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600">
                                            <input id="image-upload" type="file" wire:model="image" class="sr-only">
                                            Change
                                        </label>
                                        @if($product->hasMedia('product_images') || $image)
                                            <button type="button" wire:click="$set('image', null)" class="ml-2 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                                Remove
                                            </button>
                                        @endif
                                    </div>
                                    @error('image') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Gallery Images -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Gallery Images
                                    </label>
                                    <div class="mt-2 grid grid-cols-3 gap-2">
                                        @foreach($gallery as $index => $galleryImage)
                                            <div class="relative group">
                                                <img src="{{ $galleryImage->temporaryUrl() }}" alt="Gallery Image" class="h-24 w-full object-cover rounded-md">
                                                <button type="button" wire:click="removeGalleryImage({{ $index }})" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </div>
                                        @endforeach
                                        @foreach($product->getMedia('product_gallery') as $media)
                                            <div class="relative group">
                                                <img src="{{ $media->getUrl() }}" alt="Gallery Image" class="h-24 w-full object-cover rounded-md">
                                                <button type="button" wire:click="deleteMedia({{ $media->id }})" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </div>
                                        @endforeach
                                        @if(count($gallery) + $product->getMedia('product_gallery')->count() < 5)
                                            <label class="flex items-center justify-center h-24 border-2 border-dashed border-gray-300 rounded-md cursor-pointer hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500">
                                                <input type="file" wire:model="gallery" class="sr-only" multiple>
                                                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                            </label>
                                        @endif
                                    </div>
                                    @error('gallery.*') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <!-- Status Toggles -->
                                <div class="pt-2">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="is_active" wire:model.defer="is_active"
                                               class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                        <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                            Active
                                        </label>
                                    </div>
                                    <div class="mt-2 flex items-center">
                                        <input type="checkbox" id="is_featured" wire:model.defer="is_featured"
                                               class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                        <label for="is_featured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                            Featured
                                        </label>
                                    </div>

                                    <div class="mt-2 flex items-center">
                                        <input type="checkbox" id="is_best_seller" wire:model.defer="is_best_seller"
                                               class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                                        <label for="is_best_seller" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                            Best Seller
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col-reverse sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                <button type="button" onclick="window.history.back()" 
                        class="mt-3 w-full inline-flex justify-center items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 sm:mt-0 sm:w-auto sm:text-sm">
                    Cancel
                </button>
                <button type="submit"
                        class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:bg-gray-800 dark:hover:bg-gray-700 sm:w-auto sm:text-sm transition-colors duration-200">
                    <span wire:loading.remove wire:target="save">
                        Update Product
                    </span>
                    <span wire:loading wire:target="save">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>
