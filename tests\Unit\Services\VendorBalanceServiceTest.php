<?php

namespace Tests\Unit\Services;

use App\Services\VendorBalanceService;
use App\Models\Vendor;
use App\Models\VendorTransaction;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class VendorBalanceServiceTest extends TestCase
{
    use RefreshDatabase;

    protected VendorBalanceService $balanceService;
    protected $vendor;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->balanceService = new VendorBalanceService();
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create([
            'user_id' => $this->user->id,
            'total_earnings' => 0,
            'pending_balance' => 0,
            'available_balance' => 0,
            'withdrawn_amount' => 0,
        ]);
    }

    /** @test */
    public function it_calculates_vendor_balance_correctly()
    {
        // Create test transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -500.00,
        ]);

        $balance = $this->balanceService->calculateVendorBalance($this->vendor);

        $this->assertEquals(473.00, $balance); // 1000 - 27 - 500
    }

    /** @test */
    public function it_updates_vendor_balance_atomically()
    {
        // Create initial transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
        ]);

        $this->balanceService->updateVendorBalance($this->vendor);

        $this->vendor->refresh();
        $this->assertEquals(1000.00, $this->vendor->total_earnings);
        $this->assertEquals(27.00, $this->vendor->withdrawn_amount); // Commission is treated as withdrawn
        $this->assertEquals(973.00, $this->vendor->available_balance);
        $this->assertNotNull($this->vendor->last_balance_update);
    }

    /** @test */
    public function it_handles_concurrent_balance_updates()
    {
        // Simulate concurrent updates using database transactions
        $results = [];

        // First transaction
        DB::transaction(function () use (&$results) {
            $balance1 = $this->balanceService->calculateVendorBalance($this->vendor);
            $results[] = $balance1;
        });

        // Second transaction
        DB::transaction(function () use (&$results) {
            VendorTransaction::factory()->create([
                'vendor_id' => $this->vendor->id,
                'type' => 'sale',
                'amount' => 500.00,
            ]);
            
            $balance2 = $this->balanceService->calculateVendorBalance($this->vendor);
            $results[] = $balance2;
        });

        $this->assertCount(2, $results);
        $this->assertEquals(0.00, $results[0]); // Initial balance
        $this->assertEquals(500.00, $results[1]); // After adding transaction
    }

    /** @test */
    public function it_calculates_pending_balance_correctly()
    {
        // Create orders with different statuses
        $paidOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 1000.00,
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        $shippedOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 500.00,
            'payment_status' => 'paid',
            'status' => 'shipped',
        ]);

        $deliveredOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 750.00,
            'payment_status' => 'paid',
            'status' => 'delivered',
        ]);

        $pendingBalance = $this->balanceService->calculatePendingBalance($this->vendor);

        // Only processing and shipped orders should be pending
        // Delivered orders are available for withdrawal
        $expectedPending = 1000.00 + 500.00; // processing + shipped
        $this->assertEquals($expectedPending, $pendingBalance);
    }

    /** @test */
    public function it_calculates_available_balance_correctly()
    {
        // Create transactions and orders
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 2000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -54.00, // 2.7% of 2000
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -500.00,
        ]);

        // Create pending orders
        Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'total_amount' => 800.00,
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        $availableBalance = $this->balanceService->calculateAvailableBalance($this->vendor);

        // Total earnings: 2000 - 54 - 500 = 1446
        // Pending: 800 (processing order)
        // Available: 1446 - 800 = 646
        $this->assertEquals(646.00, $availableBalance);
    }

    /** @test */
    public function it_reconciles_vendor_balance_discrepancies()
    {
        // Set incorrect balance values
        $this->vendor->update([
            'total_earnings' => 999.99, // Incorrect
            'available_balance' => 888.88, // Incorrect
            'withdrawn_amount' => 111.11, // Incorrect
        ]);

        // Create actual transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
        ]);

        $reconciliation = $this->balanceService->reconcileVendorBalance($this->vendor);

        $this->assertTrue($reconciliation['has_discrepancies']);
        $this->assertEquals(1000.00, $reconciliation['calculated_total_earnings']);
        $this->assertEquals(27.00, $reconciliation['calculated_withdrawn']);

        // Verify balance was corrected
        $this->vendor->refresh();
        $this->assertEquals(1000.00, $this->vendor->total_earnings);
        $this->assertEquals(27.00, $this->vendor->withdrawn_amount);
    }

    /** @test */
    public function it_processes_vendor_withdrawal()
    {
        // Set up vendor with available balance
        $this->vendor->update([
            'available_balance' => 1000.00,
            'total_earnings' => 1000.00,
        ]);

        $withdrawalAmount = 500.00;
        $result = $this->balanceService->processWithdrawal($this->vendor, $withdrawalAmount, 'Bank transfer');

        $this->assertTrue($result['success']);
        $this->assertDatabaseHas('vendor_transactions', [
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -500.00,
        ]);

        $this->vendor->refresh();
        $this->assertEquals(500.00, $this->vendor->available_balance);
        $this->assertEquals(500.00, $this->vendor->withdrawn_amount);
    }

    /** @test */
    public function it_prevents_withdrawal_exceeding_available_balance()
    {
        $this->vendor->update(['available_balance' => 100.00]);

        $result = $this->balanceService->processWithdrawal($this->vendor, 200.00, 'Bank transfer');

        $this->assertFalse($result['success']);
        $this->assertStringContains('insufficient', strtolower($result['message']));
        
        // Verify no transaction was created
        $this->assertDatabaseMissing('vendor_transactions', [
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -200.00,
        ]);
    }

    /** @test */
    public function it_handles_zero_withdrawal_amount()
    {
        $result = $this->balanceService->processWithdrawal($this->vendor, 0.00, 'Test');

        $this->assertFalse($result['success']);
        $this->assertStringContains('greater than zero', $result['message']);
    }

    /** @test */
    public function it_handles_negative_withdrawal_amount()
    {
        $result = $this->balanceService->processWithdrawal($this->vendor, -100.00, 'Test');

        $this->assertFalse($result['success']);
        $this->assertStringContains('greater than zero', $result['message']);
    }

    /** @test */
    public function it_gets_balance_history()
    {
        // Create transactions over time
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
            'created_at' => now()->subDays(5),
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
            'created_at' => now()->subDays(4),
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -500.00,
            'created_at' => now()->subDays(2),
        ]);

        $history = $this->balanceService->getBalanceHistory($this->vendor, 7);

        $this->assertCount(3, $history);
        
        // Verify running balance calculation
        $this->assertEquals(1000.00, $history[0]['running_balance']);
        $this->assertEquals(973.00, $history[1]['running_balance']);
        $this->assertEquals(473.00, $history[2]['running_balance']);
    }

    /** @test */
    public function it_validates_balance_integrity()
    {
        // Create transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -27.00,
        ]);

        // Update vendor balance correctly
        $this->balanceService->updateVendorBalance($this->vendor);

        $validation = $this->balanceService->validateBalanceIntegrity($this->vendor);

        $this->assertTrue($validation['is_valid']);
        $this->assertEmpty($validation['discrepancies']);
    }

    /** @test */
    public function it_detects_balance_integrity_issues()
    {
        // Create transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 1000.00,
        ]);

        // Set incorrect vendor balance
        $this->vendor->update([
            'total_earnings' => 500.00, // Should be 1000.00
            'available_balance' => 300.00, // Should be 1000.00
        ]);

        $validation = $this->balanceService->validateBalanceIntegrity($this->vendor);

        $this->assertFalse($validation['is_valid']);
        $this->assertNotEmpty($validation['discrepancies']);
        $this->assertArrayHasKey('total_earnings', $validation['discrepancies']);
        $this->assertArrayHasKey('available_balance', $validation['discrepancies']);
    }

    /** @test */
    public function it_gets_vendor_financial_summary()
    {
        // Create various transactions
        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'sale',
            'amount' => 2000.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'commission',
            'amount' => -54.00,
        ]);

        VendorTransaction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'type' => 'withdrawal',
            'amount' => -500.00,
        ]);

        $this->balanceService->updateVendorBalance($this->vendor);

        $summary = $this->balanceService->getVendorFinancialSummary($this->vendor);

        $this->assertIsArray($summary);
        $this->assertArrayHasKey('total_earnings', $summary);
        $this->assertArrayHasKey('total_commissions', $summary);
        $this->assertArrayHasKey('total_withdrawals', $summary);
        $this->assertArrayHasKey('available_balance', $summary);
        $this->assertArrayHasKey('pending_balance', $summary);
        $this->assertArrayHasKey('transaction_count', $summary);

        $this->assertEquals(2000.00, $summary['total_earnings']);
        $this->assertEquals(54.00, $summary['total_commissions']);
        $this->assertEquals(500.00, $summary['total_withdrawals']);
    }
}
