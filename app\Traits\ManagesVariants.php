<?php

namespace App\Traits;

use Livewire\WithFileUploads;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Provides common functionality for managing product variants
 */
trait ManagesVariants
{
    use WithFileUploads;

    public $hasVariants = false;
    public $showVariantHelp = false;

    /**
     * Add a new empty variant
     */
    public function addVariant()
    {
        $this->variants[] = [
            'id' => null,
            'name' => '',
            'value' => '',
            'price' => null,
            'image' => null,
            'existing_image_path' => null
        ];

        // Automatically enable variants when adding one
        $this->hasVariants = true;
    }

    /**
     * Remove a variant by index
     */
    public function removeVariant($index)
    {
        // If this is an existing variant, mark its image for cleanup
        if (isset($this->variants[$index]['id'])) {
            $this->mediaToDelete[] = $this->variants[$index]['id'];
        }

        // Remove the variant
        unset($this->variants[$index]);
        $this->variants = array_values($this->variants);

        // If no variants left, disable variants
        if (empty($this->variants)) {
            $this->hasVariants = false;
        }
    }

    /**
     * Toggle variants on/off
     */
    public function toggleVariants()
    {
        $this->hasVariants = !$this->hasVariants;

        if (!$this->hasVariants) {
            // Clear all variants when disabled
            $this->variants = [];
        } else {
            // Add one empty variant when enabled
            if (empty($this->variants)) {
                $this->addVariant();
            }
        }
    }

    /**
     * Add quick variant setup for common patterns
     */
    public function addQuickVariants($pattern)
    {
        $this->hasVariants = true;

        switch ($pattern) {
            case 'sizes':
                $sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
                foreach ($sizes as $size) {
                    $this->variants[] = [
                        'id' => null,
                        'name' => 'Size',
                        'value' => $size,
                        'price' => null,
                        'image' => null,
                        'existing_image_path' => null
                    ];
                }
                break;

            case 'colors':
                $colors = ['Black', 'White', 'Red', 'Blue', 'Green'];
                foreach ($colors as $color) {
                    $this->variants[] = [
                        'id' => null,
                        'name' => 'Color',
                        'value' => $color,
                        'price' => null,
                        'image' => null,
                        'existing_image_path' => null
                    ];
                }
                break;

            case 'materials':
                $materials = ['Cotton', 'Polyester', 'Silk', 'Wool'];
                foreach ($materials as $material) {
                    $this->variants[] = [
                        'id' => null,
                        'name' => 'Material',
                        'value' => $material,
                        'price' => null,
                        'image' => null,
                        'existing_image_path' => null
                    ];
                }
                break;
        }
    }

    /**
     * Duplicate a variant
     */
    public function duplicateVariant($index)
    {
        if (isset($this->variants[$index])) {
            $variant = $this->variants[$index];
            // Remove ID so it creates a new variant
            $variant['id'] = null;
            $variant['value'] = $variant['value'] . ' (Copy)';
            $this->variants[] = $variant;
        }
    }

    /**
     * Get available variant types
     */
    public function getVariantTypes()
    {
        return [
            'Color' => 'Color',
            'Size' => 'Size',
            'Material' => 'Material',
            'Style' => 'Style',
            'Pattern' => 'Pattern',
            'Finish' => 'Finish',
            'Weight' => 'Weight',
            'Capacity' => 'Capacity',
            'Custom' => 'Custom'
        ];
    }

    /**
     * Handle variant image upload
     */
    // This method is now handled by the shared ProductForm or is deprecated

    /**
     * Save variants for a product
     */
    public function saveVariants($product, $variantData = null)
    {
        $variantData = $variantData ?? $this->variants;
        $savedVariantIds = [];

        foreach ($variantData as $index => $variant) {
            if (empty($variant['name']) || empty($variant['value'])) {
                continue;
            }

            $variantDetails = [
                'name' => $variant['name'],
                'value' => $variant['value'],
                'price' => $variant['price'] ?? $product->price,
            ];

            // Update or create the variant
            $savedVariant = $product->variants()->updateOrCreate(
                ['id' => $variant['id'] ?? null],
                $variantDetails
            );

            // Handle image upload using Spatie MediaLibrary
            if (!empty($variant['image'])) {
                $savedVariant->addMedia($variant['image']->getRealPath())
                    ->toMediaCollection('variant_image');
            }

            $savedVariantIds[] = $savedVariant->id;
        }

        // Delete variants that were removed
        $product->variants()->whereNotIn('id', array_filter($savedVariantIds))->delete();

        return $savedVariantIds;
    }

    /**
     * Get validation rules for variants
     */
    protected function getVariantValidationRules()
    {
        return [
            'variants' => 'nullable|array',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
        ];
    }

    /**
     * Get validation messages for variants
     */
    protected function getVariantValidationMessages()
    {
        return [
            'variants.*.name.required_with' => 'Variant name is required when value is provided.',
            'variants.*.value.required_with' => 'Variant value is required when name is provided.',
            'variants.*.price.numeric' => 'Variant price must be a number.',
            'variants.*.price.min' => 'Variant price must be at least 0.',
            'variants.*.image.image' => 'Variant image must be a valid image file.',
            'variants.*.image.max' => 'Variant image must not be larger than 2MB.',
        ];
    }
}
