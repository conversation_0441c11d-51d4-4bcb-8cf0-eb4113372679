<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    {{ $subscriptionPlan->exists ? 'Edit' : 'Create' }} Subscription Plan
                </h1>
                <p class="text-gray-600 mt-1">
                    {{ $subscriptionPlan->exists ? 'Update the subscription plan details' : 'Create a new subscription plan for vendors' }}
                </p>
            </div>
            <a href="{{ route('admin.subscription-plans.index') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Plans
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <form wire:submit="save" class="p-6 space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name *</label>
                    <input type="text" 
                           wire:model="name" 
                           id="name"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="e.g., Basic Plan">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                    <select wire:model="status" 
                            id="status"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    @error('status')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <textarea wire:model="description" 
                          id="description"
                          rows="4"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Describe what this plan includes..."></textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Pricing -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price (₦) *</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">₦</span>
                        </div>
                        <input type="number" 
                               wire:model="price" 
                               id="price"
                               step="0.01"
                               min="0"
                               class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="0.00">
                    </div>
                    @error('price')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="interval" class="block text-sm font-medium text-gray-700 mb-2">Billing Interval *</label>
                    <select wire:model="interval" 
                            id="interval"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                    </select>
                    @error('interval')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Limits -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="product_limit" class="block text-sm font-medium text-gray-700 mb-2">Product Limit</label>
                    <input type="number" 
                           wire:model="product_limit" 
                           id="product_limit"
                           min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Unlimited">
                    @error('product_limit')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-xs mt-1">Leave empty for unlimited</p>
                </div>

                <div>
                    <label for="order_limit" class="block text-sm font-medium text-gray-700 mb-2">Order Limit</label>
                    <input type="number" 
                           wire:model="order_limit" 
                           id="order_limit"
                           min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Unlimited">
                    @error('order_limit')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-xs mt-1">Leave empty for unlimited</p>
                </div>

                <div>
                    <label for="storage_limit" class="block text-sm font-medium text-gray-700 mb-2">Storage Limit (MB)</label>
                    <input type="number" 
                           wire:model="storage_limit" 
                           id="storage_limit"
                           min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Unlimited">
                    @error('storage_limit')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-xs mt-1">Leave empty for unlimited</p>
                </div>
            </div>

            <!-- Features -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Features</label>
                
                <!-- Add Feature -->
                <div class="flex gap-2 mb-4">
                    <input type="text" 
                           wire:model="newFeature" 
                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Add a feature..."
                           wire:keydown.enter.prevent="addFeature">
                    <button type="button" 
                            wire:click="addFeature"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <!-- Features List -->
                @if(count($features) > 0)
                    <div class="space-y-2">
                        @foreach($features as $index => $feature)
                            <div class="flex items-center justify-between bg-gray-50 px-4 py-2 rounded-lg">
                                <span class="text-gray-700">{{ $feature }}</span>
                                <button type="button" 
                                        wire:click="removeFeature({{ $index }})"
                                        class="text-red-500 hover:text-red-700 transition-colors">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.subscription-plans.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        wire:loading.attr="disabled"
                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50">
                    <span wire:loading.remove>
                        {{ $subscriptionPlan->exists ? 'Update Plan' : 'Create Plan' }}
                    </span>
                    <span wire:loading>
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        {{ $subscriptionPlan->exists ? 'Updating...' : 'Creating...' }}
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>
