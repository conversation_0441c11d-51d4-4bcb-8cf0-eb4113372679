<?php

namespace App\Traits;

/**
 * Provides common functionality for managing product specifications
 */
trait ManagesSpecifications
{
    /**
     * Add a new empty specification
     */
    public function addSpecification()
    {
        $this->specifications[] = [
            'id' => null,
            'name' => '',
            'value' => ''
        ];
    }

    /**
     * Remove a specification by index
     */
    public function removeSpecification($index)
    {
        if (isset($this->specifications[$index]['id'])) {
            $this->mediaToDelete[] = $this->specifications[$index]['id'];
        }
        
        unset($this->specifications[$index]);
        $this->specifications = array_values($this->specifications);
    }

    /**
     * Save specifications for a product
     */
    public function saveSpecifications($product, $specificationData = null)
    {
        $specificationData = $specificationData ?? $this->specifications;
        $savedSpecificationIds = [];

        foreach ($specificationData as $spec) {
            if (empty($spec['name']) || empty($spec['value'])) {
                continue;
            }

            $savedSpec = $product->specifications()->updateOrCreate(
                ['id' => $spec['id'] ?? null],
                [
                    'name' => $spec['name'],
                    'value' => $spec['value']
                ]
            );

            $savedSpecificationIds[] = $savedSpec->id;
        }

        // Delete specifications that were removed
        $product->specifications()->whereNotIn('id', array_filter($savedSpecificationIds))->delete();

        return $savedSpecificationIds;
    }

    /**
     * Get validation rules for specifications
     */
    protected function getSpecificationValidationRules()
    {
        return [
            'specifications' => 'nullable|array',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ];
    }

    /**
     * Get validation messages for specifications
     */
    protected function getSpecificationValidationMessages()
    {
        return [
            'specifications.*.name.required_with' => 'Specification name is required when value is provided.',
            'specifications.*.value.required_with' => 'Specification value is required when name is provided.',
        ];
    }
}
