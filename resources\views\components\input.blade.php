@props([
    'label' => '',
    'placeholder' => '',
    'type' => 'text',
    'value' => '',
    'disabled' => false,
])

<div class="space-y-2">
    @if ($label)
        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">{{ $label }}</label>
    @endif
    <input
        type="{{ $type }}"
        placeholder="{{ $placeholder }}"
        value="{{ $value }}"
        {{ $disabled ? 'disabled' : '' }}
        {!! $attributes->merge(['class' => 'w-full px-3 sm:px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base sm:text-lg min-h-[44px] touch-manipulation']) !!}
    >
    @php
        $wireModelAttribute = $attributes->whereStartsWith('wire:model')->first();
        $errorKey = is_string($wireModelAttribute) ? str_replace(['wire:model.', 'wire:model=', 'wire:model'], '', $wireModelAttribute) : null;
        if ($errorKey && str_contains($errorKey, '"')) {
            $errorKey = trim($errorKey, '"\'');
        }
    @endphp

    <x-form.error :field="$errorKey" />
</div>