<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-4 sm:p-6 md:p-8 rounded-2xl shadow-2xl mb-6 sm:mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="space-y-1 sm:space-y-2">
                <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Edit Product
                </h1>
                <p class="text-gray-300 text-sm sm:text-base md:text-lg">
                    Update your product details and settings
                </p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3 sm:space-x-3">
                <a href="{{ route('vendor.products.index') }}"
                   class="group border-2 border-white text-white px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center justify-center space-x-2 text-sm sm:text-base">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-0.5"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" form="product-form"
                        wire:loading.attr="disabled"
                        class="group bg-white text-black px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2 text-sm sm:text-base disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100">
                    <div wire:loading.remove wire:target="save">
                        <i class="fa-solid fa-save transition-transform duration-300 group-hover:scale-110"></i>
                    </div>
                    <div wire:loading wire:target="save">
                        <svg class="animate-spin h-4 w-4 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <span wire:loading.remove wire:target="save">Update Product</span>
                    <span wire:loading wire:target="save">Updating...</span>
                </button>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="save" id="product-form" class="space-y-8">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {{-- Main Content --}}
            <div class="lg:col-span-2 space-y-8">
                {{-- Basic Information Card --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-info-circle text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Product Information</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm">Basic details about your product</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">
                        <div class="space-y-1 sm:space-y-2">
                            <label for="name" class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                                Product Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   wire:model.live.debounce.500ms="name"
                                   id="name"
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                   placeholder="Enter product name..."
                                   required>
                            @error('name')
                                <p class="text-red-500 text-xs sm:text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="space-y-1 sm:space-y-2">
                            <label for="description" class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea wire:model.live="description"
                                      id="description"
                                      rows="4"
                                      class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 resize-none text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                      placeholder="Describe your product in detail..."
                                      required></textarea>
                            @error('description')
                                <p class="text-red-500 text-xs sm:text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div class="space-y-1 sm:space-y-2">
                                <label for="price" class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                                    Price (₦) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm sm:text-base">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="price"
                                           id="price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-3 sm:pr-4 py-2 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                           placeholder="0.00"
                                           required>
                                </div>
                                @error('price')
                                    <p class="text-red-500 text-xs sm:text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-1 sm:space-y-2">
                                <label for="discount_price" class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                                    Sale Price (₦)
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm sm:text-base">₦</span>
                                    </div>
                                    <input type="number"
                                           wire:model.live="discount_price"
                                           id="discount_price"
                                           step="0.01"
                                           min="0"
                                           class="w-full pl-8 pr-3 sm:pr-4 py-2 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                           placeholder="0.00 (Optional)">
                                </div>
                                @error('discount_price')
                                    <p class="text-red-500 text-xs sm:text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                                @if($product->discount_price && $product->price)
                                    <p class="text-green-600 dark:text-green-400 text-xs sm:text-sm flex items-center">
                                        <i class="fas fa-tag mr-1 text-xs"></i>
                                        {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% discount
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Inventory & Status Section --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-boxes text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Inventory & Status</h3>
                                <p class="text-gray-600 dark:text-gray-400 text-xs sm:text-sm">Manage stock quantity and product availability</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                            {{-- Stock Quantity --}}
                            <div class="space-y-2">
                                <label for="quantity" class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Stock Quantity *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-boxes text-gray-500 dark:text-gray-400"></i>
                                    </div>
                                    <input type="number"
                                           wire:model.live="stock"
                                           id="quantity"
                                           min="0"
                                           class="w-full pl-10 pr-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-xl shadow-sm focus:ring-2 focus:ring-black dark:focus:ring-blue-500 focus:border-black dark:focus:border-blue-500 transition-all duration-300 text-sm sm:text-lg"
                                           placeholder="Enter available quantity">
                                </div>
                                @error('stock')
                                    <p class="text-red-500 text-xs sm:text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                                <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Number of items available for sale
                                </p>
                            </div>

                            {{-- Product Status --}}
                            <div class="space-y-2">
                                <label class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Product Status</label>
                                <div class="flex items-center space-x-3 p-3 sm:p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                                    <input type="checkbox"
                                           wire:model.live="is_active"
                                           id="is_active"
                                           class="h-4 w-4 sm:h-5 sm:w-5 text-black dark:text-blue-500 border-gray-300 dark:border-gray-600 rounded focus:ring-black dark:focus:ring-blue-500 focus:ring-2">
                                    <label for="is_active" class="text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Product is Active and Available for Sale
                                    </label>
                                </div>
                                <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Inactive products won't be visible to customers
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Enhanced Variants Section --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-purple-500 rounded-lg">
                                    <i class="fas fa-palette text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Product Variants</h3>
                                    <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm">Add different options like colors, sizes, materials, etc.</p>
                                </div>
                            </div>
                            <button type="button"
                                    wire:click="$toggle('showVariantHelp')"
                                    class="p-2 text-purple-500 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-300">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>

                        {{-- Help Section --}}
                        @if($showVariantHelp)
                            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                    <div class="text-sm text-blue-800 dark:text-blue-200">
                                        <p class="font-medium mb-2">When to use variants:</p>
                                        <ul class="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300">
                                            <li>Your product comes in different colors, sizes, or materials</li>
                                            <li>Different options have different prices</li>
                                            <li>You want customers to choose specific options</li>
                                        </ul>
                                        <p class="mt-2 font-medium">Skip variants if your product has no options or variations.</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="p-4 sm:p-6 space-y-6">
                        {{-- Variant Toggle --}}
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           wire:model.live="hasVariants"
                                           wire:click="toggleVariants"
                                           class="h-5 w-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                    <label class="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                                        This product has variants (colors, sizes, etc.)
                                    </label>
                                </div>
                            </div>
                            @if($hasVariants || count($variants) > 0)
                                <div class="flex items-center space-x-2">
                                    {{-- Quick Setup Buttons --}}
                                    <div class="relative group">
                                        <button type="button"
                                                class="px-3 py-1.5 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
                                            Quick Setup
                                        </button>
                                        <div class="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                                            <div class="p-2 space-y-1">
                                                <button type="button"
                                                        wire:click="addQuickVariants('sizes')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                                                    <i class="fas fa-tshirt mr-2"></i>Clothing Sizes
                                                </button>
                                                <button type="button"
                                                        wire:click="addQuickVariants('colors')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                                                    <i class="fas fa-palette mr-2"></i>Basic Colors
                                                </button>
                                                <button type="button"
                                                        wire:click="addQuickVariants('materials')"
                                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                                                    <i class="fas fa-layer-group mr-2"></i>Materials
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button"
                                            wire:click="addVariant"
                                            class="group inline-flex items-center px-3 sm:px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm rounded-xl font-medium transition-all duration-300 hover:scale-105">
                                        <i class="fas fa-plus mr-2 text-xs transition-transform duration-300 group-hover:rotate-90"></i>
                                        <span>Add Variant</span>
                                    </button>
                                </div>
                            @endif
                        </div>

                        {{-- Variants List --}}
                        @if($hasVariants || count($variants) > 0)
                            <div class="space-y-4">
                                @forelse ($variants as $index => $variant)
                            <div class="group p-4 sm:p-6 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-purple-300 dark:hover:border-purple-500 hover:shadow-lg transition-all duration-300 relative" wire:key="variant-{{ $index }}">
                                <div class="absolute top-2 right-2 sm:top-4 sm:right-4 flex items-center space-x-2">
                                    <button type="button"
                                            wire:click="duplicateVariant({{ $index }})"
                                            class="p-1.5 sm:p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300"
                                            title="Duplicate variant">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button type="button"
                                            wire:click="removeVariant({{ $index }})"
                                            class="p-1.5 sm:p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-all duration-300"
                                            title="Remove variant">
                                        <i class="fas fa-times text-sm"></i>
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-6 pr-8 sm:pr-12">
                                    {{-- Variant Image Uploader --}}
                                    <div class="md:col-span-1 flex flex-col items-center justify-center space-y-2">
                                        <label class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Variant Image</label>
                                        @if (isset($variants[$index]['image']) && $variants[$index]['image'])
                                            <img src="{{ $variants[$index]['image']->temporaryUrl() }}" class="h-20 w-20 object-cover rounded-lg shadow-md" alt="New Variant Image Preview">
                                        @elseif (isset($variant['existing_image_url']) && $variant['existing_image_url'])
                                            <img src="{{ $variant['existing_image_url'] }}" class="h-20 w-20 object-cover rounded-lg shadow-md" alt="Existing Variant Image">
                                        @else
                                            <div class="h-20 w-20 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400 text-2xl"></i>
                                            </div>
                                        @endif
                                        <div class="flex items-center space-x-2">
                                            <label for="variant-image-{{ $index }}" class="cursor-pointer text-sm text-blue-600 dark:text-blue-400 hover:underline">
                                                {{ (isset($variant['existing_image_url']) && $variant['existing_image_url']) || (isset($variants[$index]['image']) && $variants[$index]['image']) ? 'Change' : 'Upload' }}
                                            </label>
                                            <input type="file" id="variant-image-{{ $index }}" wire:model="variants.{{ $index }}.image" class="sr-only">
                                            @if ((isset($variant['existing_image_url']) && $variant['existing_image_url']) || (isset($variants[$index]['image']) && $variants[$index]['image']))
                                                <button type="button" wire:click="removeVariantImage({{ $index }})" class="text-xs text-red-500 hover:underline">
                                                    Remove
                                                </button>
                                            @endif
                                        </div>
                                        <div wire:loading wire:target="variants.{{ $index }}.image" class="text-xs text-gray-500">Uploading...</div>
                                        @error("variants.{$index}.image") <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                    </div>

                                    {{-- Variant Details --}}
                                    <div class="space-y-1 sm:space-y-2">
                                        <label class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Variant Type</label>
                                        <select wire:model.live="variants.{{ $index }}.name"
                                                class="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white">
                                            <option value="">Select Type</option>
                                            @foreach($this->getVariantTypes() as $key => $label)
                                                <option value="{{ $key }}">{{ $label }}</option>
                                            @endforeach
                                        </select>
                                        @error("variants.{$index}.name")
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="space-y-1 sm:space-y-2">
                                        <label class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Variant Value</label>
                                        @if(isset($variants[$index]['name']) && $variants[$index]['name'] === 'Color')
                                            <select wire:model.live="variants.{{ $index }}.value"
                                                    class="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white">
                                                <option value="">Select Color</option>
                                                @foreach($colors as $color)
                                                    <option value="{{ $color->name }}">{{ $color->name }}</option>
                                                @endforeach
                                            </select>
                                        @elseif(isset($variants[$index]['name']) && $variants[$index]['name'] === 'Size')
                                            <select wire:model.live="variants.{{ $index }}.value"
                                                    class="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white">
                                                <option value="">Select Size</option>
                                                @foreach($sizes as $size)
                                                    <option value="{{ $size->code }}">{{ $size->name }} ({{ $size->code }})</option>
                                                @endforeach
                                            </select>
                                        @else
                                            <input type="text"
                                                   wire:model.live="variants.{{ $index }}.value"
                                                   class="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                                   placeholder="e.g., Red, Large">
                                        @endif
                                    </div>
                                    <div class="space-y-1 sm:space-y-2">
                                        <label class="block text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Extra Price (₦)</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 dark:text-gray-400 text-sm">₦</span>
                                            </div>
                                            <input type="number"
                                                   step="0.01"
                                                   min="0"
                                                   wire:model.live="variants.{{ $index }}.price"
                                                   class="w-full pl-8 pr-3 sm:pr-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                                   placeholder="0.00">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8 sm:py-12">
                                <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                                    <i class="fas fa-palette text-gray-400 text-lg sm:text-xl"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-300 font-medium text-sm sm:text-base">No variants added yet</p>
                                <p class="text-gray-400 dark:text-gray-400 text-xs sm:text-sm mt-1">Add variants like colors, sizes, or styles</p>
                            </div>
                        @endforelse
                    </div>
                @endif
            </div>

                {{-- Specifications Section --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                            <div class="flex items-center space-x-3">
                                <div class="p-2 bg-blue-500 rounded-lg">
                                    <i class="fas fa-list-ul text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Specifications</h3>
                                    <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm">Add detailed product specifications and features</p>
                                </div>
                            </div>
                            <button type="button"
                                    wire:click="addSpecification"
                                    class="mt-2 sm:mt-0 inline-flex items-center justify-center px-3 sm:px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm sm:text-base rounded-xl font-medium transition-all duration-300 hover:scale-105">
                                <i class="fas fa-plus mr-1.5 text-xs sm:text-sm"></i>
                                <span>Add Specification</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">
                        @forelse ($specifications as $index => $specification)
                            <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 items-start sm:items-end" wire:key="spec-{{ $index }}">
                                <div class="w-full sm:flex-1 space-y-1">
                                    <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                    <input type="text"
                                           wire:model.defer="specifications.{{ $index }}.name"
                                           class="w-full px-3 sm:px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                           placeholder="e.g., Material">
                                </div>
                                <div class="w-full sm:flex-1 space-y-1">
                                    <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Value</label>
                                    <div class="flex items-center gap-2">
                                        <input type="text"
                                               wire:model.defer="specifications.{{ $index }}.value"
                                               class="w-full px-3 sm:px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-all duration-300 text-sm sm:text-base dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                                               placeholder="e.g., 100% Cotton">
                                        <button type="button"
                                                wire:click="removeSpecification({{ $index }})"
                                                class="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-all duration-300">
                                            <i class="fas fa-times text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8 sm:py-10">
                                <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                                    <i class="fas fa-list-ul text-gray-400 text-lg sm:text-xl"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-300 font-medium text-sm sm:text-base">No specifications added yet</p>
                                <p class="text-gray-400 dark:text-gray-400 text-xs sm:text-sm mt-1">Add product specifications like material, dimensions, etc.</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            {{-- Sidebar --}}
            <div class="space-y-6">
                {{-- Product Image --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                        <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Product Image</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm mt-1">Upload a high-quality product image</p>
                    </div>
                    <div class="p-4 sm:p-6">
                        <div class="flex flex-col items-center justify-center px-4 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-xl transition-colors duration-300 hover:border-blue-400 dark:hover:border-blue-500">
                            <div class="text-center">
                                @if ($image)
                                    <img src="{{ $image->temporaryUrl() }}" class="mx-auto h-24 w-24 sm:h-28 sm:w-28 object-cover rounded-lg shadow-sm mb-3" alt="Product preview">
                                @elseif ($product->getFirstMedia('product_images'))
                                    <img src="{{ $product->getFirstMedia('product_images')->getUrl('thumb') }}" class="mx-auto h-24 w-24 sm:h-28 sm:w-28 object-cover rounded-lg shadow-sm mb-3" alt="Current product image">
                                @elseif ($product->image_url)
                                    <img src="{{ $product->image_url }}" class="mx-auto h-24 w-24 sm:h-28 sm:w-28 object-cover rounded-lg shadow-sm mb-3" alt="Product image">
                                @else
                                    <div class="mx-auto h-16 w-16 sm:h-20 sm:w-20 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 mb-3">
                                        <i class="fas fa-image text-2xl text-gray-400"></i>
                                    </div>
                                @endif
                                <div class="flex flex-col items-center text-sm text-gray-600 dark:text-gray-300">
                                    <label for="image" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500 transition-colors duration-300">
                                        <span>Choose a file</span>
                                        <input id="image" wire:model="image" type="file" class="sr-only" accept="image/*">
                                    </label>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">or drag and drop</p>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF (max 2MB)</p>
                                </div>
                            </div>
                        </div>
                        @error('image')
                            <p class="mt-2 text-xs text-red-500 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>
                </div>

                {{-- Category & Status --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30">
                        <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Category & Status</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm mt-1">Select category and set product status</p>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">
                        <div class="space-y-1">
                            <label for="category_id" class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <select wire:model.defer="category_id"
                                    id="category_id"
                                    class="mt-1 block w-full pl-3 pr-10 py-2.5 text-sm sm:text-base border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:focus:ring-purple-400 dark:focus:border-purple-400 dark:bg-gray-700 dark:text-white transition-all duration-300">
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" class="dark:bg-gray-800">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <p class="mt-1 text-xs text-red-500 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1 text-xs"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="pt-2">
                            <div class="flex items-center">
                                <div class="relative flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="is_active"
                                               wire:model.defer="is_active"
                                               type="checkbox"
                                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:checked:bg-purple-600 transition duration-150 ease-in-out">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="is_active" class="font-medium text-gray-700 dark:text-gray-300">Product is active</label>
                                        <p class="text-gray-500 dark:text-gray-400 text-xs">Make this product visible to customers</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Shipping Details --}}
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden transition-all duration-300">
                    <div class="p-4 sm:p-6 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                        <h3 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Shipping Details</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-xs sm:text-sm mt-1">Product dimensions and weight</p>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div class="space-y-1">
                                <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Weight (kg)</label>
                                <div class="relative">
                                    <input type="number" 
                                           step="0.01" 
                                           min="0"
                                           wire:model.defer="weight"
                                           class="block w-full pl-3 pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white transition-all duration-300 text-sm sm:text-base"
                                           placeholder="0.00">
                                </div>
                            </div>
                            <div class="space-y-1">
                                <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Length (cm)</label>
                                <div class="relative">
                                    <input type="number"
                                           step="0.01"
                                           min="0"
                                           wire:model.defer="length"
                                           class="block w-full pl-3 pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white transition-all duration-300 text-sm sm:text-base"
                                           placeholder="0.00">
                                </div>
                            </div>
                            <div class="space-y-1">
                                <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Width (cm)</label>
                                <div class="relative">
                                    <input type="number"
                                           step="0.01"
                                           min="0"
                                           wire:model.defer="width"
                                           class="block w-full pl-3 pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white transition-all duration-300 text-sm sm:text-base"
                                           placeholder="0.00">
                                </div>
                            </div>
                            <div class="space-y-1">
                                <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Height (cm)</label>
                                <div class="relative">
                                    <input type="number"
                                           step="0.01"
                                           min="0"
                                           wire:model.defer="height"
                                           class="block w-full pl-3 pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white transition-all duration-300 text-sm sm:text-base"
                                           placeholder="0.00">
                                </div>
                            </div>
                        </div>
                        <div class="pt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-info-circle mr-1"></i>
                                Accurate dimensions help calculate shipping costs.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>