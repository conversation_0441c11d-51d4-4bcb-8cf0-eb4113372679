<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Commission extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'order_item_id',
        'vendor_id',
        'amount',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * BUSINESS LOGIC FIX: Calculate commission amount using centralized rate
     */
    public static function calculateCommissionAmount(float $orderTotal): float
    {
        $commissionRate = config('brandify.commission_rate', 0.027);
        return round($orderTotal * $commissionRate, 2);
    }

    /**
     * BUSINESS LOGIC FIX: Calculate vendor earnings (order total minus commission)
     */
    public static function calculateVendorEarnings(float $orderTotal): float
    {
        $commissionRate = config('brandify.commission_rate', 0.027);
        return round($orderTotal * (1 - $commissionRate), 2);
    }

    /**
     * BUSINESS LOGIC FIX: Get current commission rate
     */
    public static function getCommissionRate(): float
    {
        return config('brandify.commission_rate', 0.027);
    }
}
