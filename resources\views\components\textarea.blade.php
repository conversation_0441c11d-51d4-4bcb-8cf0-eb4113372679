@props([
    'label' => '',
    'placeholder' => '',
    'value' => '',
    'disabled' => false,
])

<div class="space-y-2">
    @if ($label)
        <label class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">{{ $label }}</label>
    @endif
    <textarea
        placeholder="{{ $placeholder }}"
        {{ $disabled ? 'disabled' : '' }}
        {!! $attributes->merge(['class' => 'w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 resize-none']) !!}
    >{{ $value }}</textarea>
    @php
        $wireModelAttribute = $attributes->whereStartsWith('wire:model')->first();
        $errorKey = is_string($wireModelAttribute) ? str_replace(['wire:model.', 'wire:model=', 'wire:model'], '', $wireModelAttribute) : null;
        if ($errorKey && str_contains($errorKey, '"')) {
            $errorKey = trim($errorKey, '"\'');
        }
    @endphp
    @if($errorKey)
        @error($errorKey)
            <p class="text-red-500 text-sm flex items-center mt-1">
                <i class="fas fa-exclamation-circle mr-1"></i>
                {{ $message }}
            </p>
        @enderror
    @endif
</div>