<?php

namespace App\Livewire\Dashboard;

use App\Models\Order;
use Livewire\Component;

class MapWidget extends Component
{
    public $orderData = [];

    public function mount()
    {
        $this->loadOrderData();
    }

    public function loadOrderData()
    {
        $ordersByState = Order::query()
            ->selectRaw('shipping_state, COUNT(*) as order_count')
            ->whereNotNull('shipping_state')
            ->groupBy('shipping_state')
            ->get()
            ->pluck('order_count', 'shipping_state');

        $this->orderData = $ordersByState;
    }

    public function render()
    {
        return view('livewire.dashboard.map-widget');
    }
}