<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Session;

/**
 * Service for tracking and retrieving recently viewed products.
 */
class ViewedProductsService
{
    /**
     * Session key to store viewed products.
     *
     * @var string
     */
    private const SESSION_KEY = 'recently_viewed_products';
    
    /**
     * Maximum number of products to track.
     *
     * @var int
     */
    private const MAX_PRODUCTS = 6;
    
    /**
     * Add a product to the recently viewed list.
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function addProduct(Product $product): void
    {
        if (!$product || !$product->exists) {
            return;
        }
        
        $productId = $product->id;
        $viewedProducts = $this->getViewedProductIds();
        
        // Remove the product if it already exists to reorder it
        if (($key = array_search($productId, $viewedProducts)) !== false) {
            unset($viewedProducts[$key]);
        }
        
        // Add the product to the beginning of the array
        array_unshift($viewedProducts, $productId);
        
        // Trim the array to the maximum size
        $viewedProducts = array_slice($viewedProducts, 0, self::MAX_PRODUCTS);
        
        // Save back to session
        Session::put(self::SESSION_KEY, $viewedProducts);
    }
    
    /**
     * Get recently viewed product IDs from the session.
     *
     * @return array
     */
    public function getViewedProductIds(): array
    {
        return Session::get(self::SESSION_KEY, []);
    }
    
    /**
     * Get recently viewed products as eloquent models.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentlyViewedProducts(int $limit = 3)
    {
        $productIds = $this->getViewedProductIds();
        
        // If there are no viewed products, return an empty collection
        if (empty($productIds)) {
            return Product::where('is_active', true)
                ->inRandomOrder()
                ->limit($limit)
                ->get();
        }
        
        // Retrieve products in the correct order
        $productIds = array_slice($productIds, 0, $limit);
        
        // Use the whereIn method but maintain the order from the session
        $products = Product::whereIn('id', $productIds)
            ->where('is_active', true)
            ->get();
            
        // Re-sort the products to match the order in the session
        $ordered = collect();
        foreach ($productIds as $id) {
            $product = $products->firstWhere('id', $id);
            if ($product) {
                $ordered->push($product);
            }
        }
        
        // If we don't have enough products, add some recommended ones
        if ($ordered->count() < $limit) {
            $existing = $ordered->pluck('id')->toArray();
            $additional = Product::where('is_active', true)
                ->whereNotIn('id', $existing)
                ->inRandomOrder()
                ->limit($limit - $ordered->count())
                ->get();
                
            $ordered = $ordered->merge($additional);
        }
        
        return $ordered;
    }
    
    /**
     * Clear the recently viewed products list.
     *
     * @return void
     */
    public function clearViewedProducts(): void
    {
        Session::forget(self::SESSION_KEY);
    }
}
