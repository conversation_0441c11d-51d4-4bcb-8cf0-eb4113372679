<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class MediaLibraryIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $vendor;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles first
        $vendorRole = \App\Models\Role::firstOrCreate(['name' => 'vendor']);

        // Create test user and vendor
        $this->user = User::factory()->create(['role_id' => $vendorRole->id]);
        $this->vendor = Vendor::factory()->create(['user_id' => $this->user->id]);

        // Fake storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function vendor_model_implements_has_media_interface()
    {
        $this->assertInstanceOf(\Spatie\MediaLibrary\HasMedia::class, $this->vendor);
    }

    /** @test */
    public function product_model_implements_has_media_interface()
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);
        $this->assertInstanceOf(\Spatie\MediaLibrary\HasMedia::class, $product);
    }

    /** @test */
    public function vendor_has_logo_media_collection()
    {
        $file = UploadedFile::fake()->image('logo.jpg');
        
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $this->vendor->getMedia('logo'));
        $this->assertEquals('Test Logo', $this->vendor->getFirstMedia('logo')->name);
    }

    /** @test */
    public function vendor_has_document_media_collections()
    {
        $idDoc = UploadedFile::fake()->create('id.pdf', 1000, 'application/pdf');
        $businessDoc = UploadedFile::fake()->create('business.pdf', 1000, 'application/pdf');
        
        $this->vendor->addMedia($idDoc->getRealPath())
            ->usingName('ID Document')
            ->toMediaCollection('id_documents');
            
        $this->vendor->addMedia($businessDoc->getRealPath())
            ->usingName('Business Document')
            ->toMediaCollection('business_documents');

        $this->assertCount(1, $this->vendor->getMedia('id_documents'));
        $this->assertCount(1, $this->vendor->getMedia('business_documents'));
    }

    /** @test */
    public function product_has_image_media_collections()
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);
        
        $mainImage = UploadedFile::fake()->image('main.jpg');
        $galleryImage1 = UploadedFile::fake()->image('gallery1.jpg');
        $galleryImage2 = UploadedFile::fake()->image('gallery2.jpg');
        
        $product->addMedia($mainImage->getRealPath())
            ->usingName('Main Product Image')
            ->toMediaCollection('product_images');
            
        $product->addMedia($galleryImage1->getRealPath())
            ->usingName('Gallery Image 1')
            ->toMediaCollection('product_gallery');
            
        $product->addMedia($galleryImage2->getRealPath())
            ->usingName('Gallery Image 2')
            ->toMediaCollection('product_gallery');

        $this->assertCount(1, $product->getMedia('product_images'));
        $this->assertCount(2, $product->getMedia('product_gallery'));
    }

    /** @test */
    public function media_conversions_are_generated()
    {
        $file = UploadedFile::fake()->image('logo.jpg', 500, 500);
        
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $media = $this->vendor->getFirstMedia('logo');
        
        // Check if thumbnail conversion is generated
        $this->assertTrue($media->hasGeneratedConversion('thumb'));
        
        // Check conversion dimensions
        $thumbPath = $media->getPath('thumb');
        $this->assertFileExists($thumbPath);
    }

    /** @test */
    public function product_image_conversions_are_generated()
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);
        $file = UploadedFile::fake()->image('product.jpg', 1200, 800);
        
        $product->addMedia($file->getRealPath())
            ->usingName('Product Image')
            ->toMediaCollection('product_images');

        $media = $product->getFirstMedia('product_images');
        
        // Check if conversions are generated
        $this->assertTrue($media->hasGeneratedConversion('thumb'));
        $this->assertTrue($media->hasGeneratedConversion('large'));
    }

    /** @test */
    public function media_accepts_correct_mime_types()
    {
        // Test accepted image types for logo
        $jpegFile = UploadedFile::fake()->image('test.jpg');
        $pngFile = UploadedFile::fake()->image('test.png');
        $webpFile = UploadedFile::fake()->image('test.webp');
        
        $this->vendor->addMedia($jpegFile->getRealPath())
            ->toMediaCollection('logo');
        $this->vendor->addMedia($pngFile->getRealPath())
            ->toMediaCollection('logo');
        $this->vendor->addMedia($webpFile->getRealPath())
            ->toMediaCollection('logo');

        // Should have 3 media items (logo collection allows multiple for testing)
        $this->assertGreaterThanOrEqual(1, $this->vendor->getMedia('logo')->count());
    }

    /** @test */
    public function single_file_collections_replace_existing_media()
    {
        // Add first logo
        $firstLogo = UploadedFile::fake()->image('logo1.jpg');
        $this->vendor->addMedia($firstLogo->getRealPath())
            ->usingName('First Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $this->vendor->getMedia('logo'));

        // Add second logo (should replace first due to singleFile())
        $secondLogo = UploadedFile::fake()->image('logo2.jpg');
        $this->vendor->addMedia($secondLogo->getRealPath())
            ->usingName('Second Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $this->vendor->getMedia('logo'));
        $this->assertEquals('Second Logo', $this->vendor->getFirstMedia('logo')->name);
    }

    /** @test */
    public function media_urls_are_generated_correctly()
    {
        $file = UploadedFile::fake()->image('logo.jpg');
        
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $media = $this->vendor->getFirstMedia('logo');
        $url = $media->getUrl();
        $thumbUrl = $media->getUrl('thumb');

        $this->assertNotEmpty($url);
        $this->assertNotEmpty($thumbUrl);
        $this->assertStringContainsString('storage', $url);
        $this->assertStringContainsString('storage', $thumbUrl);
    }

    /** @test */
    public function vendor_logo_url_attribute_works()
    {
        // Test with no logo
        $this->assertStringContainsString('default-vendor.svg', $this->vendor->logo_url);

        // Test with media library logo
        $file = UploadedFile::fake()->image('logo.jpg');
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $this->vendor->refresh();
        $logoUrl = $this->vendor->logo_url;
        
        $this->assertStringContainsString('storage', $logoUrl);
        $this->assertStringNotContainsString('default-vendor.svg', $logoUrl);
    }

    /** @test */
    public function product_image_url_attribute_works()
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);
        
        // Test with no image
        $this->assertStringContainsString('product-placeholder.svg', $product->image_url);

        // Test with media library image
        $file = UploadedFile::fake()->image('product.jpg');
        $product->addMedia($file->getRealPath())
            ->usingName('Product Image')
            ->toMediaCollection('product_images');

        $product->refresh();
        $imageUrl = $product->image_url;
        
        $this->assertStringContainsString('storage', $imageUrl);
        $this->assertStringNotContainsString('product-placeholder.svg', $imageUrl);
    }

    /** @test */
    public function media_can_be_cleared_from_collections()
    {
        $file = UploadedFile::fake()->image('logo.jpg');
        
        $this->vendor->addMedia($file->getRealPath())
            ->usingName('Test Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $this->vendor->getMedia('logo'));

        // Clear the collection
        $this->vendor->clearMediaCollection('logo');

        $this->assertCount(0, $this->vendor->getMedia('logo'));
    }

    /** @test */
    public function media_library_configuration_is_correct()
    {
        $config = config('media-library');
        
        $this->assertEquals('public', $config['disk_name']);
        $this->assertEquals(1024 * 1024 * 10, $config['max_file_size']); // 10MB
        $this->assertEquals('sync', $config['queue_connection_name']);
    }
}
