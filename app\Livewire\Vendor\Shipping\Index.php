<?php

namespace App\Livewire\Vendor\Shipping;

use App\Models\Order;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Index extends Component
{
    public $recentShipments = [];
    public $defaultPackages = [];
    public $defaultAddress;

    protected $shipBubbleService;

    public function boot(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    public function mount()
    {
        $this->authorize('viewAny', Order::class);

        $vendor = Auth::user()->vendor;

        // Create a default address object from vendor's address fields
        if ($vendor->business_address || $vendor->city || $vendor->state) {
            $this->defaultAddress = (object) [
                'recipient_name' => $vendor->business_name ?? $vendor->user->name,
                'address' => $vendor->business_address,
                'line1' => $vendor->business_address,
                'city' => $vendor->city,
                'state' => $vendor->state,
                'country' => $vendor->country ?? 'Nigeria',
                'postal_code' => null,
            ];
        } else {
            $this->defaultAddress = null;
        }

        // Load recent shipments from shipped orders
        $this->loadRecentShipments();
        $this->defaultPackages = [];
    }

    public function loadRecentShipments()
    {
        $vendor = Auth::user()->vendor;

        // Get orders that have been shipped or completed (broader criteria for better data display)
        $shippedOrders = Order::whereHas('items.product', function ($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
        ->whereIn('status', ['shipped', 'completed', 'delivered'])
        ->where('payment_status', 'paid') // Only show paid orders
        ->with(['user', 'items.product', 'items' => function($query) use ($vendor) {
            // Only load items that belong to this vendor
            $query->whereHas('product', function($q) use ($vendor) {
                $q->where('vendor_id', $vendor->id);
            });
        }])
        ->orderBy('updated_at', 'desc')
        ->limit(15) // Show more recent shipments
        ->get();

        // Transform orders into shipment format with enhanced tracking
        $this->recentShipments = $shippedOrders->map(function ($order) {
            // Calculate vendor-specific total (only items from this vendor)
            $vendorTotal = $order->items->sum(function($item) {
                return $item->price * $item->quantity;
            });

            // Determine tracking status and URL
            $trackingUrl = $order->shipping_tracking_url ?? '#';
            $trackingNumber = $order->shipping_provider_order_id ?? 'ORD-' . $order->id;

            // Enhanced status based on order status
            $shipmentStatus = match($order->status) {
                'shipped' => 'In Transit',
                'completed' => 'Delivered',
                'delivered' => 'Delivered',
                default => 'Processing'
            };

            return (object) [
                'id' => $order->id,
                'tracking_number' => $trackingNumber,
                'carrier' => $order->shipping_provider ?? 'ShipBubble',
                'status' => $shipmentStatus,
                'created_at' => $order->updated_at, // Use updated_at as ship date
                'tracking_url' => $trackingUrl,
                'order' => $order,
                'customer_name' => $order->user->name ?? 'Guest Customer',
                'customer_phone' => $order->shipping_phone ?? $order->user->phone ?? 'N/A',
                'shipping_address' => $order->shipping_address_line,
                'total_amount' => $vendorTotal, // Show vendor-specific total
                'item_count' => $order->items->count(),
                'can_track' => !empty($order->shipping_tracking_url),
            ];
        })->toArray();
    }



    public function refreshTrackingStatus($orderId)
    {
        $order = Order::findOrFail($orderId);
        $this->authorize('view', $order);

        if (!$order || !$order->shipping_provider_order_id) {
            session()->flash('error', 'Cannot track this shipment - no tracking information available.');
            return;
        }

        try {
            $trackingResult = $this->shipBubbleService->trackShipment($order->shipping_provider_order_id);

            if ($trackingResult && isset($trackingResult['status']) && $trackingResult['status'] === 'success') {
                $deliveryStatus = $trackingResult['data']['status'] ?? '';
                $trackingUrl = $trackingResult['data']['tracking_url'] ?? $order->shipping_tracking_url;

                // Update order with latest tracking information
                $updateData = [];
                if ($trackingUrl && $trackingUrl !== $order->shipping_tracking_url) {
                    $updateData['shipping_tracking_url'] = $trackingUrl;
                }

                // Update order status based on delivery status
                if (in_array(strtolower($deliveryStatus), ['delivered', 'completed'])) {
                    $updateData['status'] = 'completed';
                    $updateData['delivered_at'] = now();
                } elseif (in_array(strtolower($deliveryStatus), ['shipped', 'in_transit', 'out_for_delivery'])) {
                    if ($order->status === 'processing') {
                        $updateData['status'] = 'shipped';
                    }
                }

                if (!empty($updateData)) {
                    $order->update($updateData);
                }

                // Refresh the shipments list
                $this->loadRecentShipments();

                session()->flash('success', "Tracking status updated: {$deliveryStatus}");

            } else {
                session()->flash('error', 'Unable to fetch tracking information from ShipBubble.');
            }

        } catch (\Exception $e) {
            \Log::error('Error refreshing tracking status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            session()->flash('error', 'Error updating tracking status. Please try again.');
        }
    }

    public function markAsShipped($orderId)
    {
        $order = Order::findOrFail($orderId);
        $this->authorize('view', $order);

        if (!$order) {
            session()->flash('error', 'Order not found or you do not have permission to update it.');
            return;
        }

        if ($order->status !== 'processing') {
            session()->flash('error', 'Only processing orders can be marked as shipped.');
            return;
        }

        $order->update([
            'status' => 'shipped',
            'shipped_at' => now()
        ]);

        // Refresh the shipments list
        $this->loadRecentShipments();

        session()->flash('success', 'Order marked as shipped successfully.');
    }

    public function render()
    {
        return view('livewire.vendor.shipping.index');
    }
}
