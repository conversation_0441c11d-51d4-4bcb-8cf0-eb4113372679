function checkout() {
    return {
        step: 1,
        totalSteps: 3,
        states: window.checkoutStates || [],
        lgas: [],
        address: {
            first_name: window.authFirstName || '',
            last_name: window.authLastName || '',
            street: window.authStreet || '',
            city: window.authCity || '',
            lga: window.authLga || '',
            state: window.authState || '',
            country: window.authCountry || 'Nigeria',
            phone: window.authPhone || '',
            email: window.authEmail || '',
        },
        paymentMethod: null,
        submitting: false,
        submissionError: null,

        loadingRates: false,
        ratesError: null,
        shippingRates: {}, // Per-vendor rates from the API
        consolidatedRates: [], // Combined rates for display
        selectedShippingRates: {}, // Final selections to be sent to the server
        selectedCourier: null, // The single courier selected by the user
        selectedCourierVendorBreakdown: [], // Detailed breakdown for the selected courier

        subtotal: window.cartSubtotal || 0,

        get totalShippingCost() {
            return this.selectedCourier ? this.selectedCourier.total : 0;
        },

        get total() {
            return this.subtotal + this.totalShippingCost;
        },

        init() {
            if (this.address.state) {
                const selectedState = this.states.find(s => s.state === this.address.state);
                if (selectedState) {
                    this.lgas = selectedState.lgas;
                }
            }

            this.$watch('address.state', (newState) => {
                this.address.lga = '';
                this.lgas = [];
                const selectedState = this.states.find(s => s.state === newState);
                if (selectedState) {
                    this.lgas = selectedState.lgas;
                }
            });

            this.$watch('address', _.debounce(() => {
                if (this.isAddressValid()) {
                    this.getRates();
                }
            }, 1000), { deep: true });
        },

        validateAndNext(nextStep) {
            if (this.step === 1 && !this.isAddressValid()) {
                // Optionally, show some validation feedback
                return;
            }
            if (this.step === 2 && !this.allVendorsHaveShippingSelected) {
                return;
            }
            this.step = nextStep;
        },

        get allVendorsHaveShippingSelected() {
            // Check if we have a selected courier and that all vendors have shipping rates
            if (!this.selectedCourier) {
                return false;
            }

            // Ensure all vendors in shippingRates have corresponding entries in selectedShippingRates
            const vendorIds = Object.keys(this.shippingRates);
            const selectedVendorIds = Object.keys(this.selectedShippingRates);

            // Check if all vendors have shipping options selected
            for (const vendorId of vendorIds) {
                if (!selectedVendorIds.includes(vendorId)) {
                    console.warn(`Vendor ${vendorId} missing from selected shipping rates`);
                    return false;
                }

                // Validate the shipping rate structure
                const rate = this.selectedShippingRates[vendorId];
                if (!rate || !rate.courier_id || !rate.total) {
                    console.warn(`Invalid shipping rate for vendor ${vendorId}:`, rate);
                    return false;
                }
            }

            return vendorIds.length > 0 && vendorIds.length === selectedVendorIds.length;
        },

        isAddressValid() {
            // CRITICAL FIX: Only validate address fields required for shipping rate calculation
            // Customer personal info is not needed for ShipBubble rate requests
            return this.address.street && this.address.city && this.address.state && this.address.phone;
        },

        getRates: _.debounce(function() {
            if (!this.isAddressValid()) return;

            this.loadingRates = true;
            this.ratesError = null;
            this.shippingRates = {};

            // CRITICAL FIX: Only send address fields for shipping rate requests
            // Customer personal info is not required by ShipBubble for rate calculation
            const payload = {
                address: this.address.street,
                city: this.address.city,
                lga: this.address.lga,
                state: this.address.state,
                phone: this.address.phone,
                country: this.address.country || 'Nigeria',
                // Optional fields - only include if available
                ...(this.address.first_name && this.address.last_name && {
                    name: `${this.address.first_name} ${this.address.last_name}`.trim()
                }),
                ...(this.address.email && { email: this.address.email }),
            };

            fetch(window.shippingRatesUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(payload)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.error || `Request failed with status ${response.status}`) });
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                this.shippingRates = data.rates_by_vendor;
                this.consolidatedRates = data.consolidated_rates;
                this.autoSelectCheapestRate();
            })
            .catch(error => {
                this.ratesError = error.message;
            })
            .finally(() => {
                this.loadingRates = false;
            });
        }, 1000),

        autoSelectCheapestRate() {
            if (this.consolidatedRates && this.consolidatedRates.length > 0) {
                const cheapestCourier = this.consolidatedRates.reduce((cheapest, courier) => (courier.total < cheapest.total) ? courier : cheapest, this.consolidatedRates[0]);
                this.selectCourier(cheapestCourier);
            }
        },

        selectCourier(courier) {
            this.selectedCourier = courier;
            let newSelectedRates = {};
            let breakdown = [];

            // When a consolidated courier is selected, find the corresponding rate and vendor details.
            for (const vendorId in this.shippingRates) {
                const vendorInfo = this.shippingRates[vendorId];
                if (vendorInfo.data && vendorInfo.data.couriers) {
                    const matchingRate = vendorInfo.data.couriers.find(rate => rate.courier_id === courier.courier_id);
                    if (matchingRate) {
                        // Ensure the rate structure matches backend expectations
                        newSelectedRates[vendorId] = {
                            courier_id: matchingRate.courier_id,
                            service_code: matchingRate.service_code,
                            total: matchingRate.total,
                            courier_name: matchingRate.courier_name,
                            // Include additional fields that might be needed
                            total_charge: matchingRate.total_charge || matchingRate.total,
                            delivery_eta: matchingRate.delivery_eta,
                            service_type: matchingRate.service_type
                        };
                        breakdown.push({
                            vendor_name: vendorInfo.vendor_name,
                            cost: matchingRate.total_charge || matchingRate.total
                        });
                    }
                }
            }
            this.selectedShippingRates = newSelectedRates;
            this.selectedCourierVendorBreakdown = breakdown;

            // Debug logging to help troubleshoot
            console.log('Selected shipping rates:', this.selectedShippingRates);
            console.log('Vendor breakdown:', breakdown);
        },

        isCourierSelected(courierId) {
            return this.selectedCourier && this.selectedCourier.courier_id === courierId;
        },

        /**
         * CRITICAL FIX FV3: Unified validation system that matches backend rules
         * This ensures frontend validation matches Livewire backend validation exactly
         */
        submitForm(event) {
            this.submitting = true;
            this.submissionError = null;

            // Validate form fields to match backend validation rules
            const validationErrors = this.validateFormFields();
            if (validationErrors.length > 0) {
                this.submissionError = validationErrors.join(' ');
                this.submitting = false;
                return;
            }

            // Validate shipping options before submission
            if (!this.allVendorsHaveShippingSelected) {
                this.submissionError = 'Please select a shipping option for each vendor.';
                this.submitting = false;
                return;
            }

            // Validate that we have shipping options for all vendors
            const vendorIds = Object.keys(this.shippingRates);
            const missingVendors = [];

            for (const vendorId of vendorIds) {
                if (!this.selectedShippingRates[vendorId]) {
                    const vendorName = this.shippingRates[vendorId]?.vendor_name || `Vendor ${vendorId}`;
                    missingVendors.push(vendorName);
                }
            }

            if (missingVendors.length > 0) {
                this.submissionError = `Please select shipping options for: ${missingVendors.join(', ')}`;
                this.submitting = false;
                return;
            }

            // Construct the payload directly from the component's state
            // to ensure all data is correctly captured, especially from x-model.
            const payload = {
                ...this.address,
                payment_method: this.paymentMethod,
                shipping_options: this.selectedShippingRates,
            };

            // Debug logging
            console.log('Submitting payload:', {
                ...payload,
                shipping_options_count: Object.keys(payload.shipping_options).length,
                vendor_count: vendorIds.length
            });

            fetch(window.paymentInitializeUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                },
                body: JSON.stringify(payload)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { 
                        throw { status: response.status, message: err.message, errors: err.errors };
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    this.submissionError = data.message || 'An unknown error occurred.';
                    this.submitting = false;
                }
            })
            .catch(error => {
                console.error('Submission Error:', error);
                if (error.errors) {
                    const errorMessages = Object.values(error.errors).flat().join(' ');
                    this.submissionError = `Validation failed: ${errorMessages}`;
                } else {
                    this.submissionError = error.message || 'An error occurred while submitting the form.';
                }
                this.submitting = false;
            });
        },

        /**
         * CRITICAL FIX FV3: Frontend validation that matches backend rules exactly
         * This prevents validation conflicts between frontend and backend
         */
        validateFormFields() {
            const errors = [];
            const form = document.querySelector('#checkout-form');

            if (!form) return errors;

            // Validate address (min:10, max:255)
            const address = form.querySelector('[name="street"]')?.value?.trim();
            if (!address) {
                errors.push('Address is required.');
            } else if (address.length < 10) {
                errors.push('Address must be at least 10 characters.');
            } else if (address.length > 255) {
                errors.push('Address must not exceed 255 characters.');
            }

            // Validate city (min:2, max:255)
            const city = form.querySelector('[name="city"]')?.value?.trim();
            if (!city) {
                errors.push('City is required.');
            } else if (city.length < 2) {
                errors.push('City must be at least 2 characters.');
            } else if (city.length > 255) {
                errors.push('City must not exceed 255 characters.');
            }

            // Validate state
            const state = form.querySelector('[name="state"]')?.value?.trim();
            if (!state) {
                errors.push('State is required.');
            }

            // Validate LGA (min:2)
            const lga = form.querySelector('[name="lga"]')?.value?.trim();
            if (!lga) {
                errors.push('Local Government Area is required.');
            } else if (lga.length < 2) {
                errors.push('Local Government Area must be at least 2 characters.');
            }

            // Validate phone (Nigerian format: /^0[789][01]\d{8}$/)
            const phone = form.querySelector('[name="phone"]')?.value?.trim();
            if (!phone) {
                errors.push('Phone number is required.');
            } else if (!/^0[789][01]\d{8}$/.test(phone)) {
                errors.push('Please enter a valid Nigerian phone number (e.g., 08012345678).');
            }

            // Validate first name
            const firstName = form.querySelector('[name="first_name"]')?.value?.trim();
            if (!firstName) {
                errors.push('First name is required.');
            }

            // Validate last name
            const lastName = form.querySelector('[name="last_name"]')?.value?.trim();
            if (!lastName) {
                errors.push('Last name is required.');
            }

            // Validate email
            const email = form.querySelector('[name="email"]')?.value?.trim();
            if (!email) {
                errors.push('Email is required.');
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                errors.push('Please enter a valid email address.');
            }

            return errors;
        },

        formatCurrency(amount) {
            return '₦' + (amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
    };
}
