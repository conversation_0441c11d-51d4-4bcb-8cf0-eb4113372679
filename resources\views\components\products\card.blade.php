{{-- CRITICAL FIX RD2: Unified Product Card Component with consistent responsive heights --}}
@props(['product'])

<div class="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-[450px] sm:h-[480px] lg:h-[520px] flex flex-col">
    <!-- Product Image Container -->
    <div class="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
        <a href="{{ route('products.show', $product->slug) }}" wire:navigate class="block relative">
            {{-- FIXED RD2: Standardized image heights across all breakpoints --}}
            <img src="{{ $product->image_url }}"
                 alt="{{ $product->name }}"
                 class="w-full h-48 sm:h-56 lg:h-72 object-cover transform group-hover:scale-110 transition-transform duration-700 ease-out"
                 onerror="this.onerror=null; this.src='{{ asset('images/product-placeholder.svg') }}'; this.classList.add('opacity-80');"
                 loading="lazy">
        </a>

        <!-- Sale Badge -->
        @if($product->is_on_sale)
            <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-600 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg animate-pulse">
                SALE
            </div>
        @endif

        <!-- Wishlist Button -->
        @auth
            <form action="{{ route('wishlist.add', $product->id) }}" method="POST" class="absolute top-4 right-4">
                @csrf
                <button type="submit" class="p-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group/wishlist">
                    @if(auth()->user()->wishlist()->where('product_id', $product->id)->exists())
                        <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                    @else
                        <svg class="w-5 h-5 text-gray-600 group-hover/wishlist:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    @endif
                </button>
            </form>
        @endauth
    </div>

    <!-- Product Details -->
    <div class="p-4 sm:p-6 flex-1 flex flex-col justify-between">
        <div>
            <!-- Brand -->
            @if ($product->brand)
                <p class="text-sm font-medium text-gray-500 mb-2 uppercase tracking-wide">{{ $product->brand->name }}</p>
            @endif

            <!-- Product Name -->
            <h3 class="text-base sm:text-lg font-bold text-gray-900 mb-2 sm:mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                <a href="{{ route('products.show', $product->slug) }}" class="hover:underline">
                    {{ $product->name }}
                </a>
            </h3>

            <!-- Vendor -->
            <p class="text-xs sm:text-sm text-gray-600 mb-2 sm:mb-3">
                by <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="font-medium text-black hover:underline">{{ $product->vendor->shop_name ?? $product->vendor->name }}</a>
            </p>

            <!-- Price -->
            <div class="mb-3 sm:mb-4">
                @if($product->isOnSale() && $product->discount_price)
                    <div class="flex items-center space-x-2">
                        <span class="text-lg sm:text-2xl font-bold text-black">₦{{ number_format($product->discount_price, 2) }}</span>
                        <span class="text-sm sm:text-lg text-gray-500 line-through">₦{{ number_format($product->price, 2) }}</span>
                    </div>
                @else
                    <span class="text-lg sm:text-2xl font-bold text-black">₦{{ number_format($product->price, 2) }}</span>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
            <!-- Add to Cart Button -->
            @if($product->is_active && $product->stock > 0)
                <livewire:add-to-cart-button :product="$product" :key="'add-to-cart-'.$product->id" />
            @else
                <div class="flex-1">
                    <button disabled class="w-full bg-gray-300 text-gray-500 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-semibold cursor-not-allowed text-sm sm:text-base">
                        @if(!$product->is_active)
                            <span class="hidden sm:inline">Unavailable</span>
                            <span class="sm:hidden">N/A</span>
                        @else
                            <span class="hidden sm:inline">Out of Stock</span>
                            <span class="sm:hidden">Sold Out</span>
                        @endif
                    </button>
                </div>
            @endif

            <!-- Quick View Button -->
            <a href="{{ route('products.show', $product->slug) }}"
               wire:navigate
               class="flex-shrink-0 p-2.5 sm:p-3 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-300 transform hover:scale-105 group/view sm:w-auto w-full flex items-center justify-center">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 group-hover/view:text-gray-900 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span class="ml-2 sm:hidden text-sm font-medium text-gray-600 group-hover/view:text-gray-900">View Details</span>
            </a>
        </div>
    </div>
</div>
