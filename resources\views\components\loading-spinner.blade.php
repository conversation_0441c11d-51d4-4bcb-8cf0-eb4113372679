{{-- 
    FIXED: Standardized loading spinner component for consistent UI
    Usage: <x-loading-spinner size="sm|md|lg" text="Loading..." />
--}}

@props([
    'size' => 'md',
    'text' => 'Loading...',
    'color' => 'gray',
    'inline' => false
])

@php
$sizeClasses = [
    'sm' => 'w-4 h-4',
    'md' => 'w-6 h-6', 
    'lg' => 'w-8 h-8'
];

$colorClasses = [
    'gray' => 'text-gray-600',
    'white' => 'text-white',
    'black' => 'text-black',
    'blue' => 'text-blue-600'
];

$containerClasses = $inline ? 'inline-flex items-center space-x-2' : 'flex items-center justify-center space-x-2';
@endphp

<div class="{{ $containerClasses }}" {{ $attributes }}>
    <svg class="animate-spin {{ $sizeClasses[$size] }} {{ $colorClasses[$color] }}" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    @if($text)
        <span class="text-sm {{ $colorClasses[$color] }}">{{ $text }}</span>
    @endif
</div>
