<?php

namespace App\Livewire\Vendor\Products;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Color;
use App\Models\Size;
use Livewire\WithPagination;

#[Layout('layouts.vendor')]
class Variants extends Component
{
    use WithPagination;

    public Product $product;
    public $showCreateModal = false;
    public $showEditModal = false;
    public $editingVariant = null;

    // Form fields
    public $color_id = '';
    public $size_id = '';
    public $sku = '';
    public $price_adjustment = '';
    public $stock_quantity = 0;
    public $is_active = true;

    protected $rules = [
        'color_id' => 'nullable|exists:colors,id',
        'size_id' => 'nullable|exists:sizes,id',
        'sku' => 'required|string|max:255|unique:product_variants,sku',
        'price_adjustment' => 'nullable|numeric',
        'stock_quantity' => 'required|integer|min:0',
        'is_active' => 'boolean',
    ];

    public function mount(Product $product)
    {
        $this->authorize('manageVariants', $product);
        $this->product = $product->load('variants.color', 'variants.size');
    }

    public function render()
    {
        $variants = $this->product->variants()
            ->with(['color', 'size'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $colors = Color::where('is_active', true)->orderBy('name')->get();
        $sizes = Size::where('is_active', true)->orderBy('sort_order')->orderBy('name')->get();

        return view('livewire.vendor.products.variants', compact('variants', 'colors', 'sizes'));
    }

    public function create()
    {
        $this->resetForm();
        $this->generateSku();
        $this->showCreateModal = true;
    }

    public function store()
    {
        // Validate that at least one of color or size is selected
        $this->validate();

        if (empty($this->color_id) && empty($this->size_id)) {
            $this->addError('color_id', 'Please select at least a color or size for the variant.');
            return;
        }

        // Check if this combination already exists
        $existingVariant = $this->product->variants()
            ->where('color_id', $this->color_id ?: null)
            ->where('size_id', $this->size_id ?: null)
            ->first();

        if ($existingVariant) {
            $this->addError('color_id', 'A variant with this color and size combination already exists.');
            return;
        }

        ProductVariant::create([
            'product_id' => $this->product->id,
            'color_id' => $this->color_id ?: null,
            'size_id' => $this->size_id ?: null,
            'sku' => $this->sku,
            'price_adjustment' => $this->price_adjustment ?: null,
            'stock_quantity' => $this->stock_quantity,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        $this->dispatch('toast', message: 'Product variant created successfully!', type: 'success');
    }

    public function edit($variantId)
    {
        $this->authorize('manageVariants', $this->product);
        $this->editingVariant = $this->product->variants()->findOrFail($variantId);

        $this->color_id = $this->editingVariant->color_id;
        $this->size_id = $this->editingVariant->size_id;
        $this->sku = $this->editingVariant->sku;
        $this->price_adjustment = $this->editingVariant->price_adjustment;
        $this->stock_quantity = $this->editingVariant->stock_quantity;
        $this->is_active = $this->editingVariant->is_active;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->rules['sku'] = 'required|string|max:255|unique:product_variants,sku,' . $this->editingVariant->id;
        $this->validate();

        if (empty($this->color_id) && empty($this->size_id)) {
            $this->addError('color_id', 'Please select at least a color or size for the variant.');
            return;
        }

        // Check if this combination already exists (excluding current variant)
        $existingVariant = $this->product->variants()
            ->where('color_id', $this->color_id ?: null)
            ->where('size_id', $this->size_id ?: null)
            ->where('id', '!=', $this->editingVariant->id)
            ->first();

        if ($existingVariant) {
            $this->addError('color_id', 'A variant with this color and size combination already exists.');
            return;
        }

        $this->editingVariant->update([
            'color_id' => $this->color_id ?: null,
            'size_id' => $this->size_id ?: null,
            'sku' => $this->sku,
            'price_adjustment' => $this->price_adjustment ?: null,
            'stock_quantity' => $this->stock_quantity,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        $this->dispatch('toast', message: 'Product variant updated successfully!', type: 'success');
    }

    public function delete($variantId)
    {
        $this->authorize('manageVariants', $this->product);
        $variant = $this->product->variants()->findOrFail($variantId);
        $variant->delete();
        $this->dispatch('toast', message: 'Product variant deleted successfully!', type: 'success');
    }

    public function toggleStatus($variantId)
    {
        $this->authorize('manageVariants', $this->product);
        $variant = $this->product->variants()->findOrFail($variantId);

        $variant->update(['is_active' => !$variant->is_active]);
        
        $status = $variant->is_active ? 'activated' : 'deactivated';
        $this->dispatch('toast', message: "Variant {$status} successfully!", type: 'success');
    }

    public function updatedColorId()
    {
        $this->generateSku();
    }

    public function updatedSizeId()
    {
        $this->generateSku();
    }

    private function generateSku()
    {
        if (!$this->showCreateModal && !$this->showEditModal) {
            return;
        }

        $skuParts = [
            strtoupper(substr(auth()->user()->vendor->shop_name, 0, 3)),
            $this->product->id,
        ];

        if ($this->color_id) {
            $color = Color::find($this->color_id);
            if ($color) {
                $skuParts[] = strtoupper(substr($color->name, 0, 3));
            }
        }

        if ($this->size_id) {
            $size = Size::find($this->size_id);
            if ($size) {
                $skuParts[] = strtoupper($size->code);
            }
        }

        $baseSku = implode('-', $skuParts);

        // ATOMIC FIX: Use database-level uniqueness check with retry logic
        $sku = $baseSku;
        $counter = 1;
        $maxAttempts = 10; // Prevent infinite loops
        
        do {
            $attemptSku = $sku;
            if ($counter > 1) {
                $attemptSku = $baseSku . '-' . ($counter - 1);
            }
            
            // Check if SKU already exists in database
            $exists = ProductVariant::where('sku', $attemptSku)
                ->when($this->editingVariant, function ($query) use ($attemptSku) {
                    return $query->where('id', '!=', $this->editingVariant->id);
                })
                ->exists();
                
            if (!$exists) {
                $this->sku = $attemptSku;
                return;
            }
            
            $counter++;
        } while ($counter <= $maxAttempts);
        
        // If we've exhausted attempts, generate a unique SKU with timestamp
        $this->sku = $baseSku . '-' . time() . '-' . rand(100, 999);
    }

    private function resetForm()
    {
        $this->color_id = '';
        $this->size_id = '';
        $this->sku = '';
        $this->price_adjustment = '';
        $this->stock_quantity = 0;
        $this->is_active = true;
        $this->editingVariant = null;
        $this->resetValidation();
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->resetForm();
    }
}
