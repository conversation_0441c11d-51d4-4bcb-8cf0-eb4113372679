<?php

namespace App\Livewire;

use App\Mail\ContactFormMail;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ContactForm extends Component
{
    public $name = '';
    public $email = '';
    public $subject = '';
    public $message = '';
    public $success = false;
    public $error = false;

    protected function rules()
    {
        return [
            'name' => 'required|string|min:3',
            'email' => 'required|email',
            'subject' => 'required|string|min:5',
            'message' => 'required|string|min:10',
        ];
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function submit(): void
    {
        $validatedData = $this->validate();

        try {
            Mail::to(config('mail.from.address'))
                ->send(new ContactFormMail($this->name, $this->email, $this->subject, $this->message));

            $this->success = true;
            $this->error = false;
            $this->reset(['name', 'email', 'subject', 'message']);

        } catch (\Exception $e) {
            $this->success = false;
            $this->error = true;
            \Log::error('Contact form error: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.contact-form');
    }
}
