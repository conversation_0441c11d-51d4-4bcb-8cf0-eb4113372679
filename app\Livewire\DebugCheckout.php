<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Log;

class DebugCheckout extends Component
{
    public $testData = [];
    public $cartItems = [];
    public $debugInfo = [];

    public function mount()
    {
        try {
            Log::info('DebugCheckout mount started');
            
            // Test session data
            $this->cartItems = session('cart', []);
            
            // Test various array operations that might cause issues
            $this->testArrayOperations();
            
            Log::info('DebugCheckout mount completed successfully');
            
        } catch (\Exception $e) {
            Log::error('DebugCheckout mount error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->debugInfo[] = 'Mount Error: ' . $e->getMessage();
        }
    }

    public function testArrayOperations()
    {
        try {
            // Test 1: Basic array operations
            $testArray = ['key1' => 'value1', 'key2' => 'value2'];
            $exists = array_key_exists('key1', $testArray);
            $this->debugInfo[] = 'Test 1 - Basic array_key_exists: ' . ($exists ? 'PASS' : 'FAIL');
            
            // Test 2: Empty array
            $emptyArray = [];
            $exists = array_key_exists('nonexistent', $emptyArray);
            $this->debugInfo[] = 'Test 2 - Empty array: ' . ($exists ? 'FAIL' : 'PASS');
            
            // Test 3: Cart items (potential source of error)
            if (is_array($this->cartItems)) {
                foreach ($this->cartItems as $key => $item) {
                    if (is_array($item)) {
                        $hasName = array_key_exists('name', $item);
                        $this->debugInfo[] = "Test 3 - Cart item $key has name: " . ($hasName ? 'YES' : 'NO');
                    } else {
                        $this->debugInfo[] = "Test 3 - Cart item $key is not an array: " . gettype($item);
                    }
                }
            } else {
                $this->debugInfo[] = 'Test 3 - Cart items is not an array: ' . gettype($this->cartItems);
            }
            
        } catch (\Exception $e) {
            Log::error('testArrayOperations error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            $this->debugInfo[] = 'Array Operations Error: ' . $e->getMessage();
        }
    }

    public function testCheckoutProcess()
    {
        try {
            Log::info('Testing checkout process simulation');
            
            // Simulate what happens in the real checkout
            $this->debugInfo[] = 'Starting checkout process test...';
            
            // Test shipping address array
            $shippingAddress = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'address' => 'Test Address'
            ];
            
            // Test array operations that might be problematic
            $hasName = array_key_exists('name', $shippingAddress);
            $this->debugInfo[] = 'Shipping address has name: ' . ($hasName ? 'YES' : 'NO');
            
            $this->debugInfo[] = 'Checkout process test completed successfully';
            
        } catch (\Exception $e) {
            Log::error('testCheckoutProcess error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            $this->debugInfo[] = 'Checkout Process Error: ' . $e->getMessage();
        }
    }

    public function render()
    {
        try {
            Log::info('DebugCheckout render started');
            return view('livewire.debug-checkout');
        } catch (\Exception $e) {
            Log::error('DebugCheckout render error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return '<div>Render Error: ' . $e->getMessage() . '</div>';
        }
    }
}
