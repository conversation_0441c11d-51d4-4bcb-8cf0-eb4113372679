<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ShipBubbleCacheService
{
    protected $cachePrefix = 'shipbubble_';
    protected $defaultTtl = 600; // 10 minutes in seconds
    protected $addressTtl = 1800; // 30 minutes for address validation
    protected $rateTtl = 300; // 5 minutes for shipping rates

    /**
     * Generate cache key for shipping rates
     */
    public function generateRatesCacheKey(array $cartItems, array $shippingAddress, $vendor = null): string
    {
        $vendorId = $vendor ? $vendor->id : 'default';
        
        // Create a hash of cart items for consistent caching
        $cartHash = md5(json_encode([
            'items' => collect($cartItems)->map(function ($item) {
                return [
                    'id' => $item['id'] ?? $item['product_id'],
                    'quantity' => $item['quantity'],
                    'weight' => $item['weight'] ?? 0.5,
                    'price' => $item['price'],
                ];
            })->toArray(),
            'total_weight' => collect($cartItems)->sum(function ($item) {
                return ($item['weight'] ?? 0.5) * ($item['quantity'] ?? 1);
            }),
            'total_amount' => collect($cartItems)->sum(function ($item) {
                return ($item['price'] ?? 0) * ($item['quantity'] ?? 1);
            }),
        ]));

        // Create a hash of shipping address
        $addressHash = md5(json_encode([
            'address' => $shippingAddress['address'] ?? '',
            'city' => $shippingAddress['city'] ?? '',
            'state' => $shippingAddress['state'] ?? '',
            'country' => $shippingAddress['country'] ?? 'NG',
        ]));

        return $this->cachePrefix . "rates_{$vendorId}_{$cartHash}_{$addressHash}";
    }

    /**
     * Generate cache key for address validation
     */
    public function generateAddressCacheKey(array $addressData): string
    {
        $addressHash = md5(json_encode([
            'name' => $addressData['name'] ?? '',
            'email' => $addressData['email'] ?? '',
            'phone' => $addressData['phone'] ?? '',
            'address' => $addressData['address'] ?? '',
            'city' => $addressData['city'] ?? '',
            'state' => $addressData['state'] ?? '',
            'country' => $addressData['country'] ?? 'NG',
        ]));

        return $this->cachePrefix . "address_{$addressHash}";
    }

    /**
     * Cache shipping rates with automatic expiration
     */
    public function cacheShippingRates(string $cacheKey, array $rates): void
    {
        try {
            Cache::put($cacheKey, [
                'data' => $rates,
                'cached_at' => now()->toISOString(),
                'expires_at' => now()->addSeconds($this->rateTtl)->toISOString(),
            ], $this->rateTtl);

            Log::info('ShipBubble rates cached', [
                'cache_key' => $cacheKey,
                'ttl_seconds' => $this->rateTtl,
                'expires_at' => now()->addSeconds($this->rateTtl)->toISOString(),
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to cache ShipBubble rates', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get cached shipping rates
     */
    public function getCachedShippingRates(string $cacheKey): ?array
    {
        try {
            $cached = Cache::get($cacheKey);
            
            if ($cached && isset($cached['data'])) {
                Log::info('ShipBubble rates retrieved from cache', [
                    'cache_key' => $cacheKey,
                    'cached_at' => $cached['cached_at'] ?? 'unknown',
                    'expires_at' => $cached['expires_at'] ?? 'unknown',
                ]);
                
                return $cached['data'];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to retrieve cached ShipBubble rates', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Cache address validation result
     */
    public function cacheAddressValidation(string $cacheKey, array $validationResult): void
    {
        try {
            Cache::put($cacheKey, [
                'data' => $validationResult,
                'cached_at' => now()->toISOString(),
                'expires_at' => now()->addSeconds($this->addressTtl)->toISOString(),
            ], $this->addressTtl);

            Log::info('ShipBubble address validation cached', [
                'cache_key' => $cacheKey,
                'ttl_seconds' => $this->addressTtl,
                'status' => $validationResult['status'] ?? 'unknown',
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to cache ShipBubble address validation', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get cached address validation result
     */
    public function getCachedAddressValidation(string $cacheKey): ?array
    {
        try {
            $cached = Cache::get($cacheKey);
            
            if ($cached && isset($cached['data'])) {
                Log::info('ShipBubble address validation retrieved from cache', [
                    'cache_key' => $cacheKey,
                    'cached_at' => $cached['cached_at'] ?? 'unknown',
                    'status' => $cached['data']['status'] ?? 'unknown',
                ]);
                
                return $cached['data'];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to retrieve cached ShipBubble address validation', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Clear all ShipBubble cache entries
     */
    public function clearAllCache(): void
    {
        try {
            // Get all cache keys with our prefix (this is implementation-dependent)
            // For file cache, we'll use a pattern-based approach
            $cacheKeys = $this->getAllCacheKeys();
            
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            Log::info('All ShipBubble cache entries cleared', [
                'keys_cleared' => count($cacheKeys),
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to clear ShipBubble cache', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Clear cache for specific vendor
     */
    public function clearVendorCache($vendorId): void
    {
        try {
            $pattern = $this->cachePrefix . "rates_{$vendorId}_*";
            $cacheKeys = $this->getCacheKeysByPattern($pattern);
            
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            Log::info('ShipBubble cache cleared for vendor', [
                'vendor_id' => $vendorId,
                'keys_cleared' => count($cacheKeys),
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to clear vendor ShipBubble cache', [
                'vendor_id' => $vendorId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        try {
            $allKeys = $this->getAllCacheKeys();
            $rateKeys = array_filter($allKeys, fn($key) => str_contains($key, 'rates_'));
            $addressKeys = array_filter($allKeys, fn($key) => str_contains($key, 'address_'));

            return [
                'total_entries' => count($allKeys),
                'rate_entries' => count($rateKeys),
                'address_entries' => count($addressKeys),
                'cache_prefix' => $this->cachePrefix,
                'rate_ttl_seconds' => $this->rateTtl,
                'address_ttl_seconds' => $this->addressTtl,
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get ShipBubble cache stats', [
                'error' => $e->getMessage(),
            ]);

            return [
                'total_entries' => 0,
                'rate_entries' => 0,
                'address_entries' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get all cache keys with our prefix (implementation-dependent)
     */
    protected function getAllCacheKeys(): array
    {
        // This is a simplified implementation
        // In production, you might want to use Redis SCAN or implement a key tracking system
        return [];
    }

    /**
     * Get cache keys matching a pattern
     */
    protected function getCacheKeysByPattern(string $pattern): array
    {
        // This is a simplified implementation
        // In production, you might want to use Redis pattern matching
        return [];
    }

    /**
     * Check if caching is enabled
     */
    public function isCachingEnabled(): bool
    {
        return config('cache.default') !== 'array' && 
               config('services.shipbubble.enable_caching', true);
    }

    /**
     * Get cache TTL for rates
     */
    public function getRateTtl(): int
    {
        return $this->rateTtl;
    }

    /**
     * Get cache TTL for address validation
     */
    public function getAddressTtl(): int
    {
        return $this->addressTtl;
    }
}
