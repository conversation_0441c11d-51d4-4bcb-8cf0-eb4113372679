<?php

namespace App\Livewire\Vendor\Orders;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    public $status = 'all';
    public $search = '';

    protected $queryString = [
        'status' => ['except' => 'all'],
        'search' => ['except' => ''],
    ];

    public function mount()
    {
        $this->authorize('viewAny', Order::class);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $vendor = Auth::user()->vendor;

        $query = Order::with(['user', 'items.product'])
            ->whereHas('items.product', function ($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            });

        if ($this->status !== 'all') {
            $query->where('status', $this->status);
        }

        if (!empty($this->search)) {
            $searchTerm = '%' . $this->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('id', 'like', $searchTerm)
                  ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                      $userQuery->where('name', 'like', $searchTerm);
                  });
            });
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('livewire.vendor.orders.index', [
            'orders' => $orders,
        ]);
    }
}
