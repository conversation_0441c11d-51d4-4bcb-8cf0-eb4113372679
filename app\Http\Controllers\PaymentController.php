<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Commission;
use App\Models\Product;
use App\Models\CheckoutSession;
use App\Services\PaystackService;
use App\Services\ShipBubbleService;
use App\Services\VendorEarningsService;
use App\Services\SubscriptionService;
use App\Models\Vendor;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Throwable;

class PaymentController extends Controller
{
    protected $paystackService;
    protected $shipBubbleService;
    protected $earningsService;

    public function __construct(PaystackService $paystackService, ShipBubbleService $shipBubbleService, VendorEarningsService $earningsService)
    {
        $this->paystackService = $paystackService;
        $this->shipBubbleService = $shipBubbleService;
        $this->earningsService = $earningsService;
    }

    /**
     * FIXED: Handle payment initialization from Livewire checkout component
     */
    public function initialize(Request $request)
    {
        $paymentData = session('payment_data');

        if (!$paymentData) {
            // Check if this is an AJAX request
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Payment data not found. Please try again.'], 400);
            }
            return redirect()->route('checkout.index')->with('error', 'Payment data not found. Please try again.');
        }

        // Clear the session data
        session()->forget('payment_data');

        // Create a new request with the payment data
        $request->merge($paymentData);

        // Call the existing initializePaystack method
        $response = $this->initializePaystack($request);

        // If this is an AJAX request, ensure we return JSON
        if ($request->expectsJson() && $response instanceof \Illuminate\Http\RedirectResponse) {
            // Extract the redirect URL from the response
            return response()->json(['redirect_url' => $response->getTargetUrl()]);
        }

        return $response;
    }

    public function initializePaystack(Request $request)
    {
        try {
            // Set custom error handler to catch array_key_exists errors
            // CRITICAL FIX EH1: Remove duplicate error handler to avoid conflicts
            // Global error handler in AppServiceProvider will handle array_key_exists errors

            // Add comprehensive logging for debugging (sanitized)
            \Log::info('Payment initialization started', [
                'request_data' => $this->sanitizeLogData($request->all()),
                'user_id' => auth()->id()
            ]);

            $validator = Validator::make($request->all(), [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'street' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'lga' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'postal_code' => 'nullable|string|max:20',
                'country' => 'required|string|max:255',
                'payment_method' => 'required|string|in:paystack,bank_transfer,cash_on_delivery',
                'shipping_options' => 'required|array',
                'shipping_options.*.courier_id' => 'nullable|string',
                'shipping_options.*.service_code' => 'nullable|string',
                'shipping_options.*.total' => 'required|numeric',
                'shipping_options.*.courier_name' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                \Log::error('Payment validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'request_data' => $request->all()
                ]);
                return response()->json(['message' => 'Validation failed.', 'errors' => $validator->errors()], 422);
            }
        } catch (\Exception $e) {
            \Log::error('Error in payment initialization validation', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'An error occurred during validation. Please try again.'], 500);
        }

        $validatedData = $validator->validated();

            // Sanitize input data
            $validatedData = $this->sanitizePaymentData($validatedData);
        $sessionCart = session('cart', []);

        if (empty($sessionCart)) {
            return response()->json(['message' => 'Your cart is empty.'], 400);
        }

        $productIds = array_keys($sessionCart);
        $products = Product::with('vendor')->findMany($productIds)->keyBy('id');

        // CRITICAL FIX: Validate vendor subscription status before processing payment
        $subscriptionService = app(\App\Services\SubscriptionService::class);
        $vendorValidationErrors = [];

        foreach ($products as $product) {
            if ($product->vendor && !$subscriptionService->canReceiveOrders($product->vendor)) {
                $vendorValidationErrors[] = "Vendor '{$product->vendor->shop_name}' cannot currently receive orders due to subscription status.";
            }
        }

        if (!empty($vendorValidationErrors)) {
            \Log::warning('Payment blocked due to vendor subscription issues', [
                'user_id' => auth()->id(),
                'errors' => $vendorValidationErrors,
                'product_ids' => $productIds
            ]);

            return response()->json([
                'message' => 'Some vendors cannot receive orders at this time.',
                'errors' => $vendorValidationErrors
            ], 422);
        }

        $cartItems = collect($sessionCart)->map(function ($item, $productId) use ($products) {
            if (!isset($products[$productId])) return null;
            $product = $products[$productId];
            return (object) [
                'id' => $productId,
                'qty' => $item['quantity'],
                'price' => $item['price'],
                'name' => $item['name'],
                'options' => (object) ['vendor_id' => $product->vendor_id],
            ];
        })->filter();

        if ($cartItems->isEmpty()) {
            return response()->json(['message' => 'Your cart is empty or contains invalid products.'], 400);
        }

        // Group cart items by vendor
        $vendorCarts = $cartItems->groupBy('options.vendor_id');
        $shippingOptions = $validatedData['shipping_options'];

        // Ensure shipping options is an array
        if (!is_array($shippingOptions)) {
            \Log::error('Shipping options is not an array', [
                'shipping_options_type' => gettype($shippingOptions),
                'shipping_options_data' => $shippingOptions
            ]);
            return response()->json(['message' => 'Invalid shipping options format.'], 400);
        }

        // Enhanced logging for debugging shipping validation
        \Log::info('Shipping validation debug', [
            'vendor_carts_count' => $vendorCarts->count(),
            'vendor_ids' => $vendorCarts->keys()->toArray(),
            'shipping_options_count' => count($shippingOptions),
            'shipping_option_keys' => safe_array_keys($shippingOptions),
            'user_id' => auth()->id()
        ]);

        $checkoutTransactionId = 'CHK-'.Str::uuid();
        $createdOrders = [];
        $totalAmount = 0;

        try {
            DB::beginTransaction();

            foreach ($vendorCarts as $vendorId => $items) {
                if (!isset($shippingOptions[$vendorId])) {
                    // Get vendor name for better error message
                    $vendorName = $items->first()->product->vendor->business_name ?? "Vendor {$vendorId}";

                    \Log::error('Paystack Initialization Failed: Shipping option missing', [
                        'vendor_id' => $vendorId,
                        'vendor_name' => $vendorName,
                        'available_shipping_options' => safe_array_keys($shippingOptions),
                        'vendor_carts' => safe_array_keys($vendorCarts),
                        'shipping_options_data' => $shippingOptions,
                        'cart_items_for_vendor' => $items->pluck('product.name')->toArray()
                    ]);

                    return response()->json([
                        'message' => "Please select a shipping option for {$vendorName}.",
                        'error_type' => 'missing_shipping_option',
                        'vendor_id' => $vendorId,
                        'vendor_name' => $vendorName
                    ], 422);
                }

                $shippingRate = $shippingOptions[$vendorId];

                // Validate shipping rate structure
                if (!is_array($shippingRate)) {
                    $vendorName = $items->first()->product->vendor->business_name ?? "Vendor {$vendorId}";
                    \Log::error('Shipping rate is not an array', [
                        'vendor_id' => $vendorId,
                        'vendor_name' => $vendorName,
                        'shipping_rate_type' => gettype($shippingRate),
                        'shipping_rate_data' => $shippingRate
                    ]);
                    return response()->json([
                        'message' => "Invalid shipping rate format for {$vendorName}.",
                        'error_type' => 'invalid_shipping_rate_format',
                        'vendor_id' => $vendorId
                    ], 422);
                }

                if (!isset($shippingRate['total']) || !is_numeric($shippingRate['total'])) {
                    $vendorName = $items->first()->product->vendor->business_name ?? "Vendor {$vendorId}";
                    \Log::error('Shipping rate total is missing or invalid', [
                        'vendor_id' => $vendorId,
                        'vendor_name' => $vendorName,
                        'shipping_rate' => $shippingRate
                    ]);
                    return response()->json([
                        'message' => "Invalid shipping rate total for {$vendorName}. Please refresh and try again.",
                        'error_type' => 'invalid_shipping_rate_total',
                        'vendor_id' => $vendorId
                    ], 422);
                }

                $subtotal = $items->sum(fn($item) => $item->price * $item->qty);
                $shippingCost = (float) $shippingRate['total'];
                $orderTotal = $subtotal + $shippingCost;

                $order = Order::create([
                    'user_id' => auth()->id(),
                    'vendor_id' => $vendorId,
                    'order_number' => 'ORD-' . strtoupper(Str::random(10)),
                    'checkout_transaction_id' => $checkoutTransactionId,
                    'status' => 'pending',
                    'payment_status' => 'pending',
                    'payment_method' => $validatedData['payment_method'],
                    'total' => $orderTotal,
                    'shipping_address' => $validatedData['street'],
                    'shipping_name' => $validatedData['first_name'] . ' ' . $validatedData['last_name'],
                    'shipping_city' => $validatedData['city'],
                    'shipping_lga' => $validatedData['lga'],
                    'shipping_state' => $validatedData['state'],
                    'shipping_postal_code' => $validatedData['postal_code'] ?? null,
                    'shipping_country' => $validatedData['country'],
                    'shipping_phone' => $validatedData['phone'],
                    'shipping_method' => $shippingRate['courier_name'],
                    'shipping_courier_id' => $shippingRate['courier_id'],
                    'shipping_service_code' => $shippingRate['service_code'],
                    'shipping_cost' => $shippingCost,
                    'shipbubble_token' => session('shipbubble_request_tokens.' . $vendorId),
                    'billing_address' => json_encode([
                        'first_name' => $validatedData['first_name'],
                        'last_name' => $validatedData['last_name'],
                        'email' => $validatedData['email'],
                        'phone' => $validatedData['phone'],
                        'address' => $validatedData['street'],
                        'city' => $validatedData['city'],
                        'state' => $validatedData['state'],
                        'postal_code' => $validatedData['postal_code'] ?? null,
                        'country' => $validatedData['country'],
                    ]),
                ]);

                DB::transaction(function () use ($items, $order) {
                    foreach ($items as $cartItem) {
                        $product = Product::where('id', $cartItem->id)->lockForUpdate()->first();

                        if ($product) {
                            if (!$product->hasSufficientStock($cartItem->qty)) {
                                throw new \Exception("Insufficient stock for product: {$product->name}. Only {$product->quantity} available.");
                            }

                            if (!$product->reduceStock($cartItem->qty)) {
                                throw new \Exception("Could not reserve stock for product: {$product->name}. Only {$product->fresh()->quantity} available.");
                            }

                            $order->items()->create([
                                'product_id' => $cartItem->id,
                                'vendor_id' => $vendorId, // Fix: Set vendor_id for order item
                                'quantity' => $cartItem->qty,
                                'price' => $cartItem->price,
                                'product_name' => $cartItem->name, // Store product name for reference
                                'unit_price' => $cartItem->price, // Store unit price
                                'subtotal' => $cartItem->price * $cartItem->qty, // Calculate subtotal
                                'product_data' => $product ? json_encode($product->toArray()) : null,
                            ]);

                            // CRITICAL FIX F2: Remove duplicate commission calculation
                            // Commission will be calculated properly during payment processing
                            // in the processOrderEarnings() method to avoid triple-calculation
                        }
                    }
                });
                
                $createdOrders[] = $order->id;
                $totalAmount += $orderTotal;
            }

            // Ensure we have valid totals for checkout session
            if (!isset($totalShippingCost)) {
                $totalShippingCost = 0;
                $subtotal = 0;

                // Recalculate from orders if needed
                foreach ($vendorCarts as $vendorId => $items) {
                    $vendorSubtotal = $items->sum(fn($item) => $item->price * $item->qty);
                    $vendorShippingCost = (float) $shippingOptions[$vendorId]['total'];

                    $subtotal += $vendorSubtotal;
                    $totalShippingCost += $vendorShippingCost;
                }
            }

            // If payment method is not Paystack, we can commit and redirect to a generic success page
            if ($validatedData['payment_method'] !== 'paystack') {
                DB::commit();
                Cart::destroy();
                return response()->json([
                    'message' => 'Order placed successfully!',
                    'redirect_url' => route('checkout.success', ['transaction_id' => $checkoutTransactionId])
                ]);
            }

            // Prepare vendor split data for internal tracking
            $vendorTotals = [];
            $vendorShippingData = [];
            $totalShippingCost = 0; // Initialize total shipping cost
            $subtotal = 0; // Initialize subtotal

            foreach ($vendorCarts as $vendorId => $items) {
                $vendor = Vendor::find($vendorId);
                if (!$vendor) {
                    throw new \Exception("Vendor with ID {$vendorId} not found.");
                }

                $vendorSubtotal = $items->sum(fn($item) => $item->price * $item->qty);
                $vendorShippingCost = (float) $shippingOptions[$vendorId]['total'];
                $vendorTotal = $vendorSubtotal + $vendorShippingCost;

                $vendorTotals[$vendorId] = $vendorTotal;

                // Accumulate totals for checkout session
                $subtotal += $vendorSubtotal;
                $totalShippingCost += $vendorShippingCost;

                // Store per-vendor shipping data for callback
                $vendorShippingData[$vendorId] = [
                    'shipping_option' => $shippingOptions[$vendorId],
                    'subtotal' => $vendorSubtotal,
                    'shipping_cost' => $vendorShippingCost,
                    'total' => $vendorTotal,
                    'vendor_info' => [
                        'id' => $vendor->id,
                        'shop_name' => $vendor->shop_name,
                        'business_address' => $vendor->business_address,
                        'city' => $vendor->city,
                        'state' => $vendor->state,
                        'phone' => $vendor->phone,
                    ],
                    'customer_info' => [
                        'name' => $validatedData['first_name'] . ' ' . $validatedData['last_name'],
                        'email' => $validatedData['email'],
                        'phone' => $validatedData['phone'],
                        'address' => $validatedData['street'],
                        'city' => $validatedData['city'],
                        'state' => $validatedData['state'],
                        'lga' => $validatedData['lga'],
                        'postal_code' => $validatedData['postal_code'],
                        'country' => $validatedData['country'],
                    ],
                ];
            }

            // Calculate vendor splits for internal tracking
            $vendorSplits = $this->paystackService->calculateVendorSplits($vendorTotals, $totalAmount);

            // Final safety checks for checkout session data
            $totalShippingCost = is_numeric($totalShippingCost) ? (float) $totalShippingCost : 0.0;
            $subtotal = is_numeric($subtotal) ? (float) $subtotal : 0.0;
            $totalAmount = is_numeric($totalAmount) ? (float) $totalAmount : 0.0;

            // Log the data before creating checkout session for debugging
            \Log::info('Creating checkout session with data', [
                'transaction_id' => $checkoutTransactionId,
                'user_id' => auth()->id(),
                'subtotal' => $subtotal,
                'shipping_cost' => $totalShippingCost,
                'total' => $totalAmount,
                'shipping_cost_type' => gettype($totalShippingCost),
                'shipping_cost_value' => $totalShippingCost
            ]);

            // FIXED: Store checkout data in database instead of session for reliability
            $checkoutSession = CheckoutSession::createSession([
                'transaction_id' => $checkoutTransactionId,
                'user_id' => auth()->id(),
                'cart_items' => session('cart', []),
                'shipping_address' => [
                    'name' => $validatedData['first_name'] . ' ' . $validatedData['last_name'],
                    'email' => $validatedData['email'],
                    'phone' => $validatedData['phone'],
                    'address' => $validatedData['street'],
                    'city' => $validatedData['city'],
                    'state' => $validatedData['state'],
                    'lga' => $validatedData['lga'],
                    'postal_code' => $validatedData['postal_code'],
                    'country' => $validatedData['country'],
                ],
                'selected_shipping_rate' => $validatedData['shipping_options'],
                'subtotal' => $subtotal,
                'shipping_cost' => $totalShippingCost ?? 0, // Ensure it's never null
                'total' => $totalAmount,
                'vendor_splits' => [
                    'splits' => $vendorSplits,
                    'shipping_data' => $vendorShippingData,
                ],
            ]);

            // Convert amount to kobo (integer) for Paystack
            $amountInKobo = (int) round($totalAmount * 100);

            // Log amount conversion for debugging
            \Log::info('Paystack amount conversion', [
                'original_amount' => $totalAmount,
                'amount_in_kobo' => $amountInKobo,
                'transaction_id' => $checkoutTransactionId
            ]);

            // Initialize Paystack transaction (all funds go to platform account)
            $paystackData = [
                'email' => $validatedData['email'],
                'amount' => $amountInKobo, // Paystack amount is in kobo (must be integer)
                'reference' => $checkoutTransactionId,
                'callback_url' => route('payment.paystack.callback'),
                'metadata' => [
                    'order_ids' => $createdOrders,
                    'user_id' => auth()->id(),
                    'checkout_transaction_id' => $checkoutTransactionId,
                    'vendor_count' => count($vendorCarts),
                    'total_amount' => $totalAmount,
                    'vendor_splits' => $vendorSplits, // Store splits in metadata for reference
                ]
            ];

            $result = $this->paystackService->initializeTransactionWithInternalSplit($paystackData);

            if (!$result || !$result['status']) {
                throw new \Exception($result['message'] ?? 'Failed to initialize payment.');
            }
            
            DB::commit();

            // Restore original error handler
            restore_error_handler();

            // Check if this is an AJAX request or expects JSON
            if ($request->expectsJson()) {
                return response()->json(['redirect_url' => $result['data']['authorization_url']]);
            } else {
                // For regular requests, redirect directly to Paystack
                return redirect($result['data']['authorization_url']);
            }

        } catch (Throwable $e) {
            DB::rollBack();

            // Restore stock for any products that had their quantity reduced
            $this->restoreStockForFailedOrder($createdOrders);

            // Restore original error handler
            restore_error_handler();

            // Log the detailed error information
            \Illuminate\Support\Facades\Log::error('Paystack Initialization Failed: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            // Return a generic error message to the user
            return response()->json(['message' => 'An error occurred while processing your order. Please try again.'], 500);
        }
    }

    public function handlePaystackCallback(Request $request)
    {
        $reference = $request->input('reference');

        // Enhanced logging for callback debugging
        \Log::info('Paystack callback received', [
            'reference' => $reference,
            'all_params' => $request->all(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        if (!$reference) {
            \Log::error('Paystack callback: No reference provided', [
                'request_data' => $request->all(),
                'query_params' => $request->query(),
                'post_data' => $request->post()
            ]);

            // For localhost testing, provide more helpful error message
            $errorMessage = 'Payment reference not found.';
            if (app()->environment('local')) {
                $errorMessage .= ' This might be due to localhost callback limitations. Check your Paystack dashboard for payment status.';
            }

            return redirect()->route('checkout.index')->with('error', $errorMessage);
        }

        $transaction = $this->paystackService->verifyTransaction($reference);

        \Log::info('Paystack transaction verification result', [
            'reference' => $reference,
            'transaction_status' => $transaction['status'] ?? 'unknown',
            'payment_status' => $transaction['data']['status'] ?? 'unknown'
        ]);

        if ($transaction && $transaction['status'] && $transaction['data']['status'] === 'success') {
            $metadata = $transaction['data']['metadata'];
            $checkoutTransactionId = $metadata['checkout_transaction_id'] ?? $reference;

            // FIXED: Retrieve checkout session from database instead of session
            $checkoutSession = CheckoutSession::where('transaction_id', $checkoutTransactionId)
                ->where('status', 'pending')
                ->first();

            if (!$checkoutSession) {
                \Log::error('Checkout session not found for transaction', [
                    'transaction_id' => $checkoutTransactionId,
                    'reference' => $reference
                ]);
                return redirect()->route('checkout.index')->with('error', 'Checkout session expired or not found.');
            }

            // Mark session as processing
            $checkoutSession->markAsProcessing();
            $checkoutSession->update(['paystack_reference' => $reference]);

            $orders = Order::where('checkout_transaction_id', $checkoutTransactionId)->get();

            foreach ($orders as $order) {
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'processing',
                    'payment_details' => json_encode($transaction['data']),
                ]);
            }

            // Process vendor earnings using stored split data from database
            $vendorSplitsData = $checkoutSession->vendor_splits;

            // Add safety checks to prevent array errors
            if (!is_array($vendorSplitsData)) {
                \Log::error('Vendor splits data is not an array', [
                    'checkout_session_id' => $checkoutSession->id,
                    'vendor_splits_type' => gettype($vendorSplitsData),
                    'vendor_splits_data' => $vendorSplitsData
                ]);
                $vendorSplitsData = [];
            }

            $vendorSplits = [];
            if (isset($vendorSplitsData['splits']) && is_array($vendorSplitsData['splits'])) {
                $vendorSplits = $vendorSplitsData['splits'];
            }

            foreach ($orders as $order) {
                if ($order->vendor && isset($vendorSplits[$order->vendor_id]) && is_array($vendorSplits[$order->vendor_id])) {
                    $vendorSplitData = $vendorSplits[$order->vendor_id];

                    // Ensure amount exists and is numeric
                    if (!isset($vendorSplitData['amount']) || !is_numeric($vendorSplitData['amount'])) {
                        \Log::error('Invalid vendor split amount', [
                            'order_id' => $order->id,
                            'vendor_id' => $order->vendor_id,
                            'split_data' => $vendorSplitData
                        ]);
                        continue;
                    }

                    // CRITICAL FIX F2: Use the new processOrderEarnings method to avoid triple calculation
                    $earningsResult = $this->earningsService->processOrderEarnings($order);

                    \Log::info('Order earnings processed via new method', [
                        'order_id' => $order->id,
                        'vendor_id' => $order->vendor_id,
                        'earnings_result' => $earningsResult,
                    ]);
                }
            }

            // Create separate shipments for each vendor using stored shipping data from database
            $vendorShippingData = [];
            if (isset($vendorSplitsData['shipping_data']) && is_array($vendorSplitsData['shipping_data'])) {
                $vendorShippingData = $vendorSplitsData['shipping_data'];
            }

            if ($vendorShippingData && is_array($vendorShippingData)) {
                foreach ($orders as $order) {
                    $vendorId = $order->vendor_id;

                    if (isset($vendorShippingData[$vendorId]) && is_array($vendorShippingData[$vendorId])) {
                        $shippingData = $vendorShippingData[$vendorId];

                        // Validate shipping data structure
                        if (!isset($shippingData['shipping_option']) || !is_array($shippingData['shipping_option'])) {
                            \Log::error('Invalid shipping option data', [
                                'order_id' => $order->id,
                                'vendor_id' => $vendorId,
                                'shipping_data' => $shippingData
                            ]);
                            continue;
                        }

                        if (!isset($shippingData['vendor_info']) || !is_array($shippingData['vendor_info'])) {
                            \Log::error('Invalid vendor info data', [
                                'order_id' => $order->id,
                                'vendor_id' => $vendorId,
                                'shipping_data' => $shippingData
                            ]);
                            continue;
                        }

                        $shippingOption = $shippingData['shipping_option'];
                        $vendorInfo = $shippingData['vendor_info'];
                        $customerInfo = $shippingData['customer_info'] ?? [];

                        try {
                            // Create shipment for this specific vendor
                            $shipmentPayload = [
                                'courier_id' => $shippingOption['courier_id'],
                                'service_code' => $shippingOption['service_code'],
                                'sender' => [
                                    'name' => $vendorInfo['shop_name'],
                                    'phone' => $vendorInfo['phone'] ?: config('services.shipbubble.default_phone'),
                                    'address' => $vendorInfo['business_address'] ?: config('services.shipbubble.default_address'),
                                    'city' => $vendorInfo['city'] ?: 'Lagos',
                                    'state' => $vendorInfo['state'] ?: 'Lagos',
                                ],
                                'receiver' => [
                                    'name' => $customerInfo['name'],
                                    'phone' => $customerInfo['phone'],
                                    'address' => $customerInfo['address'],
                                    'city' => $customerInfo['city'],
                                    'state' => $customerInfo['state'],
                                    'lga' => $customerInfo['lga'],
                                ],
                                'package' => [
                                    'description' => "Order #{$order->order_number} from {$vendorInfo['shop_name']}",
                                    'weight' => 1, // Default weight, should be calculated from products
                                    'length' => 10,
                                    'width' => 10,
                                    'height' => 10,
                                ],
                                'delivery_instructions' => 'Handle with care',
                                'reference' => $order->order_number,
                            ];

                            $shipmentResult = $this->shipBubbleService->createShipment($shipmentPayload);

                            if ($shipmentResult && isset($shipmentResult['status']) && $shipmentResult['status'] === 'success') {
                                $order->update([
                                    'shipping_tracking_url' => $shipmentResult['data']['tracking_url'] ?? null,
                                    'shipping_provider_order_id' => $shipmentResult['data']['order_id'] ?? null,
                                    'status' => 'shipped',
                                ]);
                            } else {
                                \Illuminate\Support\Facades\Log::error('Failed to create ShipBubble shipment for order ' . $order->id, [
                                    'vendor_id' => $vendorId,
                                    'response' => $shipmentResult,
                                    'payload' => $shipmentPayload,
                                ]);
                            }
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('Exception creating shipment for order ' . $order->id, [
                                'vendor_id' => $vendorId,
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                            ]);
                        }
                    }
                }

            }

            // FIXED: Mark checkout session as completed and clean up cart
            $checkoutSession->markAsCompleted();
            session()->forget('cart');

            \Log::info('Payment successful, redirecting to success page', [
                'transaction_id' => $checkoutTransactionId,
                'reference' => $reference,
                'user_id' => auth()->id()
            ]);

            return redirect()->route('checkout.success', ['transaction_id' => $checkoutTransactionId])
                ->with('success', 'Payment successful! Your order is being processed.');
        }

        // FIXED: Mark checkout session as failed for failed payments
        $metadata = $transaction['data']['metadata'] ?? [];
        $checkoutTransactionId = $metadata['checkout_transaction_id'] ?? $reference;

        $checkoutSession = CheckoutSession::where('transaction_id', $checkoutTransactionId)->first();
        if ($checkoutSession) {
            $checkoutSession->markAsFailed();
        }

        return redirect()->route('checkout.index')->with('error', 'Payment verification failed.');
    }

    public function checkoutSuccess(Request $request, $transaction_id = null)
    {
        $transactionId = $transaction_id ?? $request->query('transaction_id');
        if (!$transactionId) {
            return redirect()->route('home');
        }

        $orders = Order::where('checkout_transaction_id', $transactionId)
            ->where('user_id', auth()->id())
            ->with('vendor', 'items.product')
            ->get();

        if ($orders->isEmpty()) {
            return redirect()->route('home')->with('error', 'No order found.');
        }

        return view('checkout.success', compact('orders'));
    }

    /**
     * Handle Paystack webhooks for payment verification redundancy
     */
    public function handlePaystackWebhook(Request $request)
    {
        // Log incoming webhook for debugging
        \Log::info('Paystack webhook received', [
            'headers' => $request->headers->all(),
            'body' => $request->getContent(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Verify the webhook signature
        $signature = $request->header('x-paystack-signature');
        $webhookSecret = config('services.paystack.secret');

        if (!$webhookSecret) {
            \Log::error('Paystack webhook: Secret key not configured');
            return response()->json(['status' => 'error', 'message' => 'Webhook secret not configured'], 500);
        }

        $computedSignature = hash_hmac('sha512', $request->getContent(), $webhookSecret);

        if (!$signature || $signature !== $computedSignature) {
            \Log::warning('Paystack webhook: Invalid signature', [
                'provided_signature' => $signature,
                'computed_signature' => $computedSignature,
                'content_length' => strlen($request->getContent()),
                'secret_configured' => !empty($webhookSecret)
            ]);
            return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 401);
        }

        $event = $request->input('event');
        $data = $request->input('data');

        \Log::info('Paystack webhook received', [
            'event' => $event,
            'reference' => $data['reference'] ?? 'unknown',
            'status' => $data['status'] ?? 'unknown'
        ]);

        // Handle charge.success event for payment verification
        if ($event === 'charge.success' && $data['status'] === 'success') {
            $reference = $data['reference'];
            $metadata = $data['metadata'] ?? [];

            // SECURITY FIX: Use only the checkout_transaction_id from metadata.
            // Do not fall back to the Paystack reference for security reasons.
            $checkoutTransactionId = $metadata['checkout_transaction_id'] ?? null;

            if (!$checkoutTransactionId) {
                \Log::error('Paystack webhook: checkout_transaction_id missing from metadata', [
                    'reference' => $reference,
                    'metadata' => $metadata
                ]);
                return response()->json(['status' => 'error', 'message' => 'Transaction ID missing'], 400);
            }

            // Check if this payment has already been processed
            $checkoutSession = CheckoutSession::where('transaction_id', $checkoutTransactionId)->first();

            if (!$checkoutSession) {
                \Log::warning('Paystack webhook: Checkout session not found', [
                    'transaction_id' => $checkoutTransactionId,
                    'reference' => $reference
                ]);
                return response()->json(['status' => 'error', 'message' => 'Checkout session not found'], 404);
            }

            if ($checkoutSession->status === 'completed') {
                \Log::info('Paystack webhook: Payment already processed', [
                    'transaction_id' => $checkoutTransactionId,
                    'reference' => $reference
                ]);
                return response()->json(['status' => 'success', 'message' => 'Payment already processed'], 200);
            }

            // Process the payment if not already completed
            if ($checkoutSession->status === 'pending') {
                try {
                    // Mark session as processing
                    $checkoutSession->markAsProcessing();
                    $checkoutSession->update(['paystack_reference' => $reference]);

                    $orders = Order::where('checkout_transaction_id', $checkoutTransactionId)->get();

                    foreach ($orders as $order) {
                        $order->update([
                            'payment_status' => 'paid',
                            'status' => 'processing',
                            'payment_details' => json_encode($data),
                        ]);
                    }

                    // Process vendor earnings and commissions
                    $this->processVendorEarnings($checkoutSession, $orders);

                    // Mark session as completed
                    $checkoutSession->markAsCompleted();

                    \Log::info('Paystack webhook: Payment processed successfully', [
                        'transaction_id' => $checkoutTransactionId,
                        'reference' => $reference
                    ]);

                    return response()->json(['status' => 'success', 'message' => 'Payment processed'], 200);

                } catch (\Exception $e) {
                    \Log::error('Paystack webhook: Error processing payment', [
                        'transaction_id' => $checkoutTransactionId,
                        'reference' => $reference,
                        'error' => $e->getMessage()
                    ]);

                    $checkoutSession->markAsFailed();
                    return response()->json(['status' => 'error', 'message' => 'Payment processing failed'], 500);
                }
            }
        }

        return response()->json(['status' => 'success', 'message' => 'Webhook received'], 200);
    }

    /**
     * Process vendor earnings and commissions (extracted for reuse)
     */
    private function processVendorEarnings($checkoutSession, $orders)
    {
        foreach ($orders as $order) {
            $this->earningsService->processOrderEarnings($order);
        }
    }

    /**
     * Restore stock for products when order creation fails
     */
    private function restoreStockForFailedOrder(array $orderIds)
    {
        try {
            foreach ($orderIds as $orderId) {
                $order = Order::find($orderId);
                if ($order) {
                    foreach ($order->items as $orderItem) {
                        $product = Product::find($orderItem->product_id);
                        if ($product && isset($product->quantity)) {
                            $product->increment('quantity', $orderItem->quantity);

                            \Log::info('Stock restored for failed order', [
                                'product_id' => $product->id,
                                'product_name' => $product->name,
                                'quantity_restored' => $orderItem->quantity,
                                'new_stock' => $product->fresh()->quantity,
                                'order_id' => $orderId
                            ]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to restore stock for failed order', [
                'order_ids' => $orderIds,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sanitize payment data to prevent XSS and other attacks
     */
    private function sanitizePaymentData(array $data): array
    {
        // Sanitize string fields
        $stringFields = ['first_name', 'last_name', 'email', 'phone', 'street', 'city', 'state', 'lga', 'country'];

        foreach ($stringFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = strip_tags(trim($data[$field]));
                $data[$field] = htmlspecialchars($data[$field], ENT_QUOTES, 'UTF-8');
            }
        }

        // Sanitize postal code (allow alphanumeric and spaces only)
        if (isset($data['postal_code'])) {
            $data['postal_code'] = preg_replace('/[^a-zA-Z0-9\s]/', '', $data['postal_code']);
        }

        // Validate and sanitize phone number
        if (isset($data['phone'])) {
            $data['phone'] = preg_replace('/[^0-9+]/', '', $data['phone']);
        }

        // Validate email format
        if (isset($data['email'])) {
            $data['email'] = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \InvalidArgumentException('Invalid email format');
            }
        }

        return $data;
    }

    /**
     * Sanitize data for logging to prevent sensitive information exposure
     */
    private function sanitizeLogData(array $data): array
    {
        $sensitiveFields = [
            'password', 'password_confirmation', 'token', 'api_key',
            'secret', 'authorization_code', 'card_number', 'cvv', 'pin'
        ];

        $sanitized = $data;

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }

        // Partially mask email and phone for privacy
        if (isset($sanitized['email'])) {
            $email = $sanitized['email'];
            $parts = explode('@', $email);
            if (count($parts) === 2) {
                $username = $parts[0];
                $domain = $parts[1];
                $maskedUsername = substr($username, 0, 2) . str_repeat('*', max(0, strlen($username) - 2));
                $sanitized['email'] = $maskedUsername . '@' . $domain;
            }
        }

        if (isset($sanitized['phone'])) {
            $phone = $sanitized['phone'];
            $sanitized['phone'] = substr($phone, 0, 4) . str_repeat('*', max(0, strlen($phone) - 6)) . substr($phone, -2);
        }

        return $sanitized;
    }

    /**
     * Test endpoint to verify webhook connectivity
     */
    public function testWebhook(Request $request)
    {
        \Log::info('Paystack webhook test endpoint called', [
            'method' => $request->method(),
            'headers' => $request->headers->all(),
            'body' => $request->getContent(),
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Webhook endpoint is reachable',
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'webhook_url' => route('paystack.webhook'),
            'callback_url' => route('payment.paystack.callback')
        ]);
    }
}
