<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Withdrawal extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'vendor_id',
        'amount',
        'method',
        'details',
        'status',
        'processed_at',
        'reference_number',
        'notes'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'processed_at' => 'datetime',
        'details' => 'array',
        'amount' => 'decimal:2',
    ];
    
    /**
     * Get the vendor that requested the withdrawal.
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
}
