<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Mobile-First Header with Gradient --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-4 sm:p-6 lg:p-8 rounded-xl lg:rounded-2xl shadow-2xl mb-6 sm:mb-8 transform transition-all duration-500 hover:shadow-3xl">
        <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div class="space-y-1 sm:space-y-2">
                <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Orders Management
                </h1>
                <p class="text-gray-300 text-sm sm:text-base lg:text-lg">View and manage your store's orders</p>
            </div>
            <div class="flex">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2 text-sm sm:text-base">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span class="hidden sm:inline">Back to Dashboard</span>
                    <span class="sm:hidden">Back</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Mobile-Optimized Search and Filter Section --}}
    <div class="bg-white rounded-xl lg:rounded-2xl shadow-lg border border-gray-100 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
        <div class="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0 lg:space-x-6">
            <div class="w-full lg:w-1/2">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input wire:model.live.debounce.300ms="search"
                           type="text"
                           placeholder="Search orders..."
                           class="w-full pl-10 pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 text-sm sm:text-base">
                </div>
            </div>

            <div class="w-full lg:w-1/3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-filter text-gray-400"></i>
                    </div>
                    <select wire:model.live="status"
                            class="w-full pl-10 pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-300 appearance-none bg-white text-sm sm:text-base">
                        <option value="all">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipping">Shipping</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modern Orders Table --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="p-8 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Orders</h3>
                    <p class="text-gray-500 mt-1">{{ $orders->total() }} total orders</p>
                </div>
            </div>
        </div>

        <!-- Mobile Card View (Hidden on Desktop) -->
        <div class="block lg:hidden space-y-4">
            @forelse ($orders as $order)
                @php
                    $vendorItems = $order->items->filter(fn($item) => $item->product && $item->product->vendor_id == auth()->user()->vendor->id);
                    $count = $vendorItems->count();
                    $vendorTotal = $vendorItems->sum(fn($item) => $item->price * $item->quantity);
                @endphp
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-4 space-y-3">
                        <!-- Order Header -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                    #{{ $order->id }}
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900 text-sm">{{ $order->user->name ?? 'Guest' }}</p>
                                    <p class="text-xs text-gray-500">{{ $order->created_at->format('M d, Y g:i A') }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-gray-900">₦{{ number_format($vendorTotal, 2) }}</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($order->status === 'completed') bg-green-100 text-green-800
                                    @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                    @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                                    @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                    @else bg-yellow-100 text-yellow-800 @endif">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </div>
                        </div>

                        <!-- Order Details -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">{{ $count }} {{ $count === 1 ? 'item' : 'items' }}</span>
                            <a href="{{ route('vendor.orders.show', $order) }}"
                               class="inline-flex items-center px-3 py-1.5 bg-black text-white text-xs font-medium rounded-lg hover:bg-gray-800 transition-colors">
                                View Details
                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-bag text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
                    <p class="text-gray-500">Orders from customers will appear here.</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on Mobile) -->
        <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-100">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Order ID</th>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Customer</th>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Products</th>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-4 xl:px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Status</th>
                        <th scope="col" class="relative px-4 xl:px-8 py-4"><span class="sr-only">Actions</span></th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-50">
                    @forelse ($orders as $order)
                        <tr class="hover:bg-gray-50 transition-all duration-300 group">
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                        #{{ $order->id }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">{{ $order->user->name ?? 'Guest' }}</p>
                                        <p class="text-xs text-gray-500">{{ $order->user->email ?? 'No email' }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                <div class="text-sm text-gray-900 font-medium">{{ $order->created_at->format('M d, Y') }}</div>
                                <div class="text-xs text-gray-500">{{ $order->created_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                @php
                                    $vendorItems = $order->items->filter(fn($item) => $item->product && $item->product->vendor_id == auth()->user()->vendor->id);
                                    $count = $vendorItems->count();
                                @endphp
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-box text-green-600 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">{{ $count }} {{ Str::plural('item', $count) }}</span>
                                </div>
                            </td>
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                @php
                                    $orderTotal = $vendorItems->sum(fn($item) => $item->price * $item->quantity);
                                @endphp
                                <div class="text-sm font-bold text-gray-900">₦{{ number_format($orderTotal, 2) }}</div>
                            </td>
                            <td class="px-4 xl:px-8 py-4 xl:py-6 whitespace-nowrap">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                    @switch($order->status)
                                        @case('completed') bg-green-100 text-green-800 @break
                                        @case('processing') bg-yellow-100 text-yellow-800 @break
                                        @case('shipping') bg-blue-100 text-blue-800 @break
                                        @case('cancelled') bg-red-100 text-red-800 @break
                                        @default bg-gray-100 text-gray-800
                                    @endswitch
                                ">
                                    <div class="w-2 h-2 rounded-full mr-2
                                        @switch($order->status)
                                            @case('completed') bg-green-500 @break
                                            @case('processing') bg-yellow-500 @break
                                            @case('shipping') bg-blue-500 @break
                                            @case('cancelled') bg-red-500 @break
                                            @default bg-gray-500
                                        @endswitch
                                    "></div>
                                    {{ ucfirst($order->status) }}
                                </span>
                            </td>
                            <td class="px-8 py-6 whitespace-nowrap text-right">
                                <a href="{{ route('vendor.orders.show', $order->id) }}"
                                   class="group inline-flex items-center px-4 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                                    <span>View Details</span>
                                    <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-8 py-16 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                        <i class="fas fa-shopping-cart text-gray-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No orders found</h3>
                                    <p class="text-gray-500 mb-6">No orders match your current filters.</p>
                                    <a href="{{ route('vendor.dashboard') }}"
                                       class="inline-flex items-center px-4 py-2 bg-black text-white rounded-xl font-medium hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                                        <i class="fas fa-arrow-left mr-2"></i>
                                        <span>Back to Dashboard</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if ($orders->hasPages())
            <div class="p-8 border-t border-gray-100 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing {{ $orders->firstItem() }} to {{ $orders->lastItem() }} of {{ $orders->total() }} results
                    </div>
                    <div class="pagination-wrapper">
                        {{ $orders->links() }}
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
