<?php

namespace App\Livewire\Admin\Products;

use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use App\Livewire\Forms\ProductForm;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

#[Layout('layouts.admin')]
class EditProductV3 extends Component
{
    public Product $product;
    public ProductForm $form;

    /**
     * Mount the component with the product to edit
     */
    public function mount(Product $product): void
    {
        $this->product = $product;
        $this->form->setProduct($product);
    }

    /**
     * Computed property for vendors - cached during component lifecycle
     */
    #[Computed]
    public function vendors()
    {
        return Vendor::select('id', 'shop_name')
            ->where('is_active', true)
            ->orderBy('shop_name')
            ->get()
            ->map(fn($vendor) => [
                'id' => $vendor->id,
                'shop_name' => $vendor->shop_name
            ])
            ->toArray();
    }

    /**
     * Computed property for categories - cached during component lifecycle
     */
    #[Computed]
    public function categories()
    {
        return Category::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(fn($category) => [
                'id' => $category->id,
                'name' => $category->name
            ])
            ->toArray();
    }

    /**
     * Computed property for brands - cached during component lifecycle
     */
    #[Computed]
    public function brands()
    {
        return Brand::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(fn($brand) => [
                'id' => $brand->id,
                'name' => $brand->name
            ])
            ->toArray();
    }

    /**
     * Update the product
     */
    public function save(): void
    {
        try {
            $this->form->update();
            
            $this->dispatch('product-updated');
            session()->flash('success', 'Product updated successfully!');
        } catch (\Exception $e) {
            $this->dispatch('product-update-failed');
            session()->flash('error', 'Failed to update product. Please try again.');
            
            // Log the error for debugging
            \Log::error('Product update failed', [
                'product_id' => $this->product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Cancel editing and return to products list
     */
    public function cancel(): void
    {
        // Just dispatch an event - let the frontend handle navigation
        $this->dispatch('cancel-edit');
    }

    /**
     * Real-time validation for specific fields
     */
    public function updatedFormName(): void
    {
        $this->form->validateOnly('name');
    }

    public function updatedFormPrice(): void
    {
        $this->form->validateOnly('price');
    }

    public function updatedFormDiscountPrice(): void
    {
        $this->form->validateOnly('discount_price');
    }

    public function updatedFormStock(): void
    {
        $this->form->validateOnly('stock');
    }

    public function updatedFormCategoryId(): void
    {
        $this->form->validateOnly('category_id');
    }

    public function updatedFormVendorId(): void
    {
        $this->form->validateOnly('vendor_id');
    }

    /**
     * Listen for validation errors and scroll to top
     */
    #[On('validation-error')]
    public function handleValidationError(): void
    {
        $this->dispatch('scroll-to-top');
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.admin.products.edit-product-v3');
    }
}
