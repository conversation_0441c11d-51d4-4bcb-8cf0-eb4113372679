<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Brands are now automatically created when vendors are created
        // This seeder is kept for potential future standalone brands
        // that are not tied to specific vendors

        $this->command->info('Brands will be automatically created when vendors are seeded.');
    }
}
