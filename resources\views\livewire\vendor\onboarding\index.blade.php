<div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-8">
            <h1 class="text-3xl font-bold text-gray-900 text-center">Vendor Onboarding</h1>
            <p class="mt-2 text-center text-gray-600">Complete the steps below to set up your store.</p>

            <!-- Progress Bar - Updated for 2-step process -->
            <div class="mt-8">
                <div class="relative">
                    <div class="absolute top-1/2 left-0 w-full h-1 bg-gray-200 rounded"></div>
                    <div class="absolute top-1/2 left-0 h-1 bg-black rounded"
                         style="width: {{ $step === 'business' ? '0%' : '100%' }};"></div>
                    <div class="flex justify-between items-center relative">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step === 'business' || $step === 'shipping' ? 'bg-black text-white' : 'bg-gray-200 text-gray-500' }}">
                                1
                            </div>
                            <p class="mt-2 text-sm font-medium">Business Info & Logo</p>
                        </div>
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step === 'shipping' ? 'bg-black text-white' : 'bg-gray-200 text-gray-500' }}">
                                2
                            </div>
                            <p class="mt-2 text-sm font-medium">Shipping Address</p>
                        </div>
                    </div>
                </div>
            </div>

            @if (session()->has('success'))
                <div class="mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Step 1: Business Information & Logo -->
            <form wire:submit.prevent="saveBusinessInfo" class="mt-8 space-y-6 {{ $step === 'business' ? '' : 'hidden' }}">
                <h2 class="text-xl font-semibold text-gray-800">Step 1: Business Information & Logo</h2>
                <p class="text-gray-600">Tell us about your business and upload your logo.</p>

                <x-input wire:model.defer="business_name" label="Business Name" placeholder="Your Company LLC" />
                <x-input wire:model.defer="phone" label="Phone Number" placeholder="+234 (*************" />
                <x-textarea wire:model.defer="business_description" label="Business Description" placeholder="Tell us about your business..." />

                <div x-data="{ isUploading: false, progress: 0 }"
                     x-on:livewire-upload-start="isUploading = true"
                     x-on:livewire-upload-finish="isUploading = false"
                     x-on:livewire-upload-error="isUploading = false"
                     x-on:livewire-upload-progress="progress = $event.detail.progress">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Business Logo (Optional)</label>
                    <input type="file" wire:model="logo" accept="image/*" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
                    <p class="text-xs text-gray-500 mt-1">Accepted formats: JPG, PNG, GIF. Max size: 2MB</p>
                    <div x-show="isUploading" class="mt-2">
                        <progress max="100" x-bind:value="progress" class="w-full"></progress>
                    </div>
                </div>

                @if ($logo)
                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">Logo Preview:</p>
                        <img src="{{ $logo->temporaryUrl() }}" class="h-24 w-24 object-cover rounded-md border">
                    </div>
                @elseif ($vendor->logo_url)
                     <div class="mt-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">Current Logo:</p>
                        <img src="{{ $vendor->logo_url }}" class="h-24 w-24 object-cover rounded-md border">
                    </div>
                @endif

                <div class="flex justify-end">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Continue to Shipping Address</button>
                </div>
            </form>

            <!-- Step 2: Shipping Address -->
            <form wire:submit.prevent="saveShippingInfo" class="mt-8 space-y-6 {{ $step === 'shipping' ? '' : 'hidden' }}">
                <h2 class="text-xl font-semibold text-gray-800">Step 2: Shipping Address</h2>
                <p class="text-gray-600">Provide your shipping address for accurate shipping calculations and order fulfillment.</p>

                <x-textarea wire:model.defer="shipping_address" label="Street Address" placeholder="123 Main Street, Apartment 4B" />

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <x-input wire:model.defer="shipping_city" label="City" placeholder="Lagos" />
                    <x-input wire:model.defer="shipping_state" label="State" placeholder="Lagos State" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <x-input wire:model.defer="shipping_postal_code" label="Postal Code (Optional)" placeholder="100001" />
                    <x-input wire:model.defer="shipping_country" label="Country" placeholder="Nigeria" />
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                This address will be used for shipping calculations and as your default sender address for orders. You can update it later in your settings.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">Complete Onboarding</button>
                </div>
            </form>
        </div>
    </div>
</div>
