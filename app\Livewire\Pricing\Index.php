<?php

namespace App\Livewire\Pricing;

use App\Models\SubscriptionPlan;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    public $plans = [];

    public function mount()
    {
        // Get all active subscription plans ordered by price
        $this->plans = SubscriptionPlan::where('is_active', true)
            ->where('name', '!=', 'Free Plan') // Exclude free plan as it's not purchasable
            ->orderBy('price')
            ->get();
    }

    public function selectPlan($planId)
    {
        // Redirect to vendor registration with selected plan
        return redirect()->route('vendor.register', ['plan' => $planId]);
    }

    public function render()
    {
        return view('livewire.pricing.index');
    }
}
