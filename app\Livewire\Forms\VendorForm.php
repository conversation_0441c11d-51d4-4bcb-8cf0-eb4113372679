<?php

namespace App\Livewire\Forms;

use App\Models\User;
use App\Models\Vendor;
use Livewire\Attributes\Validate;
use Livewire\Form;
use Illuminate\Validation\Rule;

class VendorForm extends Form
{
    public ?Vendor $vendor = null;
    public ?User $user = null;

    // User fields
    #[Validate('required|string|max:255')]
    public string $name = '';

    #[Validate('required|email|max:255')]
    public string $email = '';

    #[Validate('nullable|string|min:8|confirmed')]
    public ?string $password = null;

    #[Validate('nullable|string|min:8')]
    public ?string $password_confirmation = null;

    // Vendor fields
    #[Validate('required|string|max:255')]
    public string $shop_name = '';

    #[Validate('nullable|string|max:255')]
    public string $business_name = '';

    #[Validate('nullable|string|max:1000')]
    public string $business_description = '';

    #[Validate('nullable|string|regex:/^0[789][01]\d{8}$/')]
    public string $phone = '';

    #[Validate('nullable|string|max:255')]
    public string $business_address = '';

    #[Validate('nullable|string|max:100')]
    public string $city = '';

    #[Validate('nullable|string|max:100')]
    public string $state = '';

    #[Validate('nullable|string|max:100')]
    public string $country = '';

    #[Validate('boolean')]
    public bool $is_featured = false;

    #[Validate('boolean')]
    public bool $is_approved = false;

    #[Validate('nullable|string|max:5000')]
    public string $about = '';

    #[Validate('nullable|url|max:255')]
    public string $facebook_url = '';

    #[Validate('nullable|url|max:255')]
    public string $twitter_url = '';

    #[Validate('nullable|url|max:255')]
    public string $instagram_url = '';

    #[Validate('nullable|url|max:255')]
    public string $website_url = '';

    /**
     * Set the vendor and user for editing
     */
    public function setVendor(Vendor $vendor): void
    {
        $this->vendor = $vendor;
        $this->user = $vendor->user;
        
        // Populate user fields
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        
        // Populate vendor fields
        $this->shop_name = $vendor->shop_name;
        $this->business_name = $vendor->business_name ?? '';
        $this->business_description = $vendor->business_description ?? '';
        $this->phone = $vendor->phone ?? '';
        $this->business_address = $vendor->business_address ?? '';
        $this->city = $vendor->city ?? '';
        $this->state = $vendor->state ?? '';
        $this->country = $vendor->country ?? '';
        $this->is_featured = $vendor->is_featured;
        $this->is_approved = $vendor->is_approved;
        $this->about = $vendor->about ?? '';
        $this->facebook_url = $vendor->facebook_url ?? '';
        $this->twitter_url = $vendor->twitter_url ?? '';
        $this->instagram_url = $vendor->instagram_url ?? '';
        $this->website_url = $vendor->website_url ?? '';
    }

    /**
     * Store a new vendor
     */
    public function store(): Vendor
    {
        $this->validate();

        // Create user first
        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'password' => bcrypt($this->password),
            'role_id' => 2, // Vendor role
        ];

        $user = User::create($userData);

        // Create vendor
        $vendorData = [
            'user_id' => $user->id,
            'shop_name' => $this->shop_name,
            'business_name' => $this->business_name,
            'business_description' => $this->business_description,
            'phone' => $this->phone,
            'business_address' => $this->business_address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'is_featured' => $this->is_featured,
            'is_approved' => $this->is_approved,
            'about' => $this->about,
            'facebook_url' => $this->facebook_url,
            'twitter_url' => $this->twitter_url,
            'instagram_url' => $this->instagram_url,
            'website_url' => $this->website_url,
        ];

        $vendor = Vendor::create($vendorData);

        $this->reset();

        return $vendor;
    }

    /**
     * Update the existing vendor
     */
    public function update(): void
    {
        $this->validate();

        // Update user
        $userData = [
            'name' => $this->name,
            'email' => $this->email,
        ];

        if ($this->password) {
            $userData['password'] = bcrypt($this->password);
        }

        $this->user->update($userData);

        // Update vendor
        $vendorData = [
            'shop_name' => $this->shop_name,
            'business_name' => $this->business_name,
            'business_description' => $this->business_description,
            'phone' => $this->phone,
            'business_address' => $this->business_address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'is_featured' => $this->is_featured,
            'is_approved' => $this->is_approved,
            'about' => $this->about,
            'facebook_url' => $this->facebook_url,
            'twitter_url' => $this->twitter_url,
            'instagram_url' => $this->instagram_url,
            'website_url' => $this->website_url,
        ];

        $this->vendor->update($vendorData);
    }

    /**
     * Custom validation rules that require runtime logic
     */
    protected function rules(): array
    {
        $rules = [];

        // Email uniqueness validation
        if ($this->user) {
            $rules['email'] = [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($this->user->id)
            ];
        } else {
            $rules['email'] = [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ];
        }

        // Password validation for new vendors
        if (!$this->vendor || !$this->vendor->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        }

        return $rules;
    }

    /**
     * Custom validation messages
     */
    protected function messages(): array
    {
        return [
            'name.required' => 'Full name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required for new vendors.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'shop_name.required' => 'Shop name is required.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
            'facebook_url.url' => 'Please enter a valid Facebook URL.',
            'twitter_url.url' => 'Please enter a valid Twitter URL.',
            'instagram_url.url' => 'Please enter a valid Instagram URL.',
            'website_url.url' => 'Please enter a valid website URL.',
        ];
    }

    /**
     * Custom attribute names for validation messages
     */
    protected function validationAttributes(): array
    {
        return [
            'name' => 'full name',
            'email' => 'email address',
            'shop_name' => 'shop name',
            'business_name' => 'business name',
            'business_description' => 'business description',
            'phone' => 'phone number',
            'business_address' => 'business address',
            'is_featured' => 'featured status',
            'is_approved' => 'approval status',
            'facebook_url' => 'Facebook URL',
            'twitter_url' => 'Twitter URL',
            'instagram_url' => 'Instagram URL',
            'website_url' => 'website URL',
        ];
    }
}
