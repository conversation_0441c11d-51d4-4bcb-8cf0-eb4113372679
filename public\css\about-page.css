/* Custom styles for the About Us page */

.stat-card, .testimonial-card, .team-card, .mission-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover, .testimonial-card:hover, .team-card:hover, .mission-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
}

.brand-logo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 2rem;
    align-items: center;
}

.brand-logo img {
    max-width: 100%;
    height: auto;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: filter 0.3s ease, opacity 0.3s ease;
}

.brand-logo:hover img {
    filter: grayscale(0%);
    opacity: 1;
}

.hero-section {
    background: url('https://source.unsplash.com/1800x800/?nigeria,market,vibrant') no-repeat center center;
    background-size: cover;
    position: relative;
}

.hero-section .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}
