<?php

namespace App\Livewire\Vendor\Dashboard;

use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Computed;

#[Layout('layouts.vendor')]
#[Title('Financial Dashboard')]
class FinancialDashboard extends Component
{
    use WithPagination;

    public Vendor $vendor;

    public function mount()
    {
        $this->vendor = Auth::user()->vendor;
    }

    #[Computed(cache: true, key: 'vendor-balance', seconds: 600)]
    public function balance()
    {
        return $this->vendor->balance;
    }

    #[Computed(cache: true, key: 'vendor-total-earnings', seconds: 600)]
    public function totalEarnings()
    {
        return $this->vendor->transactions()->where('amount', '>', 0)->sum('amount');
    }

    #[Computed(cache: true, key: 'vendor-total-withdrawals', seconds: 600)]
    public function totalWithdrawals()
    {
        return abs($this->vendor->transactions()->where('type', 'withdrawal')->sum('amount'));
    }

    #[Computed(persist: true)]
    public function recentTransactions()
    {
        return $this->vendor->transactions()->latest()->paginate(10);
    }

    public function render()
    {
        return view('livewire.vendor.dashboard.financial-dashboard');
    }
}
