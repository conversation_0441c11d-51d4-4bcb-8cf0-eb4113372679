<?php

namespace App\Traits;

use Livewire\WithFileUploads;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Provides common functionality for handling product images and media
 */
trait HandlesProductImages
{
    use WithFileUploads;

    /**
     * Handle main product image upload
     */
    public function handleMainImageUpload($product, $imageField = 'image', $collection = 'product_images')
    {
        try {
            if (!$this->$imageField) {
                return;
            }

            // Clear existing media in the collection if needed
            if ($product->hasMedia($collection)) {
                $product->clearMediaCollection($collection);
            }

            // Add the new media
            $product->addMedia($this->$imageField->getRealPath())
                ->usingName($product->name)
                ->usingFileName($this->$imageField->getClientOriginalName())
                ->toMediaCollection($collection);

            // Reset the image field
            $this->$imageField = null;
            
            return true;
        } catch (\Exception $e) {
            $this->addError($imageField, 'Failed to upload image: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle gallery images upload
     */
    public function handleGalleryUploads($product, $galleryField = 'gallery', $collection = 'product_gallery')
    {
        try {
            if (empty($this->$galleryField)) {
                return true;
            }

            foreach ($this->$galleryField as $image) {
                $product->addMedia($image->getRealPath())
                    ->usingName($product->name)
                    ->toMediaCollection($collection);
            }

            // Reset the gallery field
            $this->$galleryField = [];
            
            return true;
        } catch (\Exception $e) {
            $this->addError($galleryField, 'Failed to upload one or more gallery images: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete media by ID
     */
    public function deleteMedia($mediaId)
    {
        try {
            $media = Media::find($mediaId);
            if ($media) {
                $media->delete();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->addError('media', 'Failed to delete media: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * SECURITY FIX: Enhanced validation rules for image uploads with security checks
     */
    protected function getImageValidationRules($field = 'image', $maxSize = 10240, $isArray = false)
    {
        // SECURITY FIX: More restrictive MIME types and additional security validations
        $allowedMimes = ['jpeg', 'jpg', 'png', 'webp'];
        $mimesRule = 'mimes:' . implode(',', $allowedMimes);

        // SECURITY FIX: Add dimensions validation to prevent extremely large images
        $dimensionsRule = 'dimensions:max_width=4000,max_height=4000,min_width=100,min_height=100';

        $baseRule = "nullable|image|max:{$maxSize}|{$mimesRule}|{$dimensionsRule}";

        if ($isArray) {
            return [
                $field => 'nullable|array|max:10',
                $field.'.*' => "image|max:{$maxSize}|{$mimesRule}|{$dimensionsRule}",
            ];
        }

        return [
            $field => $baseRule,
        ];
    }

    /**
     * SECURITY FIX: Validate file content to prevent malicious uploads
     */
    protected function validateFileContent($file): bool
    {
        try {
            // Check if file is actually an image by reading its content
            $imageInfo = getimagesize($file->getPathname());

            if (!$imageInfo) {
                return false;
            }

            // Validate MIME type matches file extension
            $allowedTypes = [
                IMAGETYPE_JPEG => ['jpg', 'jpeg'],
                IMAGETYPE_PNG => ['png'],
                IMAGETYPE_WEBP => ['webp']
            ];

            $detectedType = $imageInfo[2];
            $fileExtension = strtolower($file->getClientOriginalExtension());

            if (!isset($allowedTypes[$detectedType]) ||
                !in_array($fileExtension, $allowedTypes[$detectedType])) {
                return false;
            }

            // Check file size consistency
            $actualSize = filesize($file->getPathname());
            $reportedSize = $file->getSize();

            if (abs($actualSize - $reportedSize) > 1024) { // Allow 1KB difference
                return false;
            }

            return true;
        } catch (\Exception $e) {
            \Log::warning('File validation error', [
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * SECURITY FIX: Sanitize filename to prevent path traversal
     */
    protected function sanitizeFilename(string $filename): string
    {
        // Remove path traversal attempts
        $filename = basename($filename);

        // Remove special characters except dots, hyphens, and underscores
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);

        // Limit filename length
        if (strlen($filename) > 100) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = substr(pathinfo($filename, PATHINFO_FILENAME), 0, 96 - strlen($extension));
            $filename = $name . '.' . $extension;
        }

        return $filename;
    }

    /**
     * SECURITY FIX: Enhanced upload method with security validations
     */
    protected function secureUpload($file, $collection = 'default')
    {
        try {
            // Validate file content
            if (!$this->validateFileContent($file)) {
                throw new \Exception('Invalid file content or type mismatch');
            }

            // Sanitize filename
            $originalName = $this->sanitizeFilename($file->getClientOriginalName());

            // Log upload attempt
            \Log::info('Secure file upload initiated', [
                'original_name' => $file->getClientOriginalName(),
                'sanitized_name' => $originalName,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'user_id' => auth()->id()
            ]);

            // Use Spatie Media Library for secure upload
            return $this->addMediaFromRequest('file')
                ->usingName($originalName)
                ->toMediaCollection($collection);

        } catch (\Exception $e) {
            \Log::error('Secure upload failed', [
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            throw $e;
        }
    }
}
