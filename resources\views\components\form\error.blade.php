{{-- Standardized Error Message Component --}}
@props(['field' => null, 'message' => null])

@php
    // Determine the error field to check
    $errorField = $field;
    
    // Clean up field name if it contains quotes
    if ($errorField && str_contains($errorField, '"')) {
        $errorField = trim($errorField, '"\'');
    }
@endphp

@if($message)
    {{-- Direct message provided --}}
    <p class="text-red-500 text-sm flex items-center mt-1" role="alert" aria-live="polite">
        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
        {{ $message }}
    </p>
@elseif($errorField)
    {{-- Check for Laravel validation errors --}}
    @error($errorField)
        <p class="text-red-500 text-sm flex items-center mt-1" role="alert" aria-live="polite">
            <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
            {{ $message }}
        </p>
    @enderror
@endif
