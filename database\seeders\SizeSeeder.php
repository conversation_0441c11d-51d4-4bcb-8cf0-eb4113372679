<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Size;

class SizeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sizes = [
            ['name' => 'Extra Small', 'code' => 'XS', 'sort_order' => 1],
            ['name' => 'Small', 'code' => 'S', 'sort_order' => 2],
            ['name' => 'Medium', 'code' => 'M', 'sort_order' => 3],
            ['name' => 'Large', 'code' => 'L', 'sort_order' => 4],
            ['name' => 'Extra Large', 'code' => 'XL', 'sort_order' => 5],
            ['name' => 'Double Extra Large', 'code' => 'XXL', 'sort_order' => 6],
            // Additional sizes for specific categories
            ['name' => 'One Size', 'code' => 'OS', 'sort_order' => 7],
            ['name' => 'Free Size', 'code' => 'FS', 'sort_order' => 8],
        ];

        foreach ($sizes as $size) {
            Size::updateOrCreate(
                ['code' => $size['code']],
                [
                    'name' => $size['name'],
                    'sort_order' => $size['sort_order'],
                    'is_active' => true
                ]
            );
        }
    }
}
