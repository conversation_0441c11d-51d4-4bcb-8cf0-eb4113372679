/* 
 * FIXED: Standardized component styles for consistent UI/UX
 * This file contains reusable component classes for the application
 */

/* Loading States */
.loading-overlay {
    @apply absolute inset-0 bg-white/75 flex items-center justify-center z-50;
}

.loading-overlay.dark {
    @apply bg-gray-900/75;
}

/* Button Variants */
.btn-primary {
    @apply bg-black text-white hover:bg-gray-800 focus:ring-black;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
    @apply border-2 border-black text-black hover:bg-black hover:text-white focus:ring-black;
}

/* Form Elements */
.form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm;
}

.form-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm;
}

.form-textarea {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

.form-help {
    @apply text-gray-500 text-sm mt-1;
}

/* Card Components */
.card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Product Cards */
.product-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md hover:scale-105;
}

.product-card-image {
    @apply w-full h-48 object-cover;
}

.product-card-content {
    @apply p-4;
}

.product-card-title {
    @apply text-lg font-semibold text-gray-900 mb-2 line-clamp-2;
}

.product-card-price {
    @apply text-xl font-bold text-black;
}

.product-card-original-price {
    @apply text-sm text-gray-500 line-through ml-2;
}

/* Navigation */
.nav-link {
    @apply text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.nav-link.active {
    @apply text-black bg-gray-100;
}

/* Badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-black text-white;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

/* Alerts */
.alert {
    @apply p-4 rounded-md border;
}

.alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
}

.alert-error {
    @apply bg-red-50 border-red-200 text-red-800;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Tables */
.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table-header {
    @apply bg-gray-50;
}

.table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-row {
    @apply bg-white hover:bg-gray-50 transition-colors duration-200;
}

.table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Responsive Utilities */
.mobile-hidden {
    @apply hidden md:block;
}

.mobile-only {
    @apply block md:hidden;
}

/* Custom xs breakpoint for extra small screens */
@media (min-width: 475px) {
    .xs\:inline {
        display: inline;
    }

    .xs\:hidden {
        display: none;
    }

    .xs\:block {
        display: block;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Skeleton */
.skeleton {
    @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
    @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
    @apply w-10 h-10 bg-gray-200 rounded-full animate-pulse;
}

/* Focus States */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2;
}

/* Text Utilities */
.text-truncate {
    @apply truncate;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}