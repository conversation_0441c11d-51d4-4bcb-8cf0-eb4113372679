<?php

namespace Tests\Performance;

use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabasePerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Enable query logging for performance testing
        DB::enableQueryLog();
    }

    /** @test */
    public function product_listing_queries_are_optimized()
    {
        // Create test data
        $vendors = Vendor::factory()->count(10)->create();
        $categories = Category::factory()->count(5)->create();
        $brands = Brand::factory()->count(5)->create();

        // Create 100 products
        Product::factory()->count(100)->create([
            'vendor_id' => $vendors->random()->id,
            'category_id' => $categories->random()->id,
            'brand_id' => $brands->random()->id,
        ]);

        DB::flushQueryLog();

        // Test product listing with eager loading
        $startTime = microtime(true);
        
        $products = Product::with(['vendor', 'category', 'brand'])
            ->where('is_approved', true)
            ->paginate(20);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(5, $queryCount, 'Product listing should use minimal queries');
        $this->assertLessThan(100, $executionTime, 'Product listing should execute in under 100ms');

        // Verify no N+1 query problems
        $this->assertNotNull($products->first()->vendor->business_name);
        $this->assertNotNull($products->first()->category->name);
        $this->assertNotNull($products->first()->brand->name);
    }

    /** @test */
    public function vendor_dashboard_queries_are_optimized()
    {
        $vendor = Vendor::factory()->create();
        $user = User::factory()->create(['user_type' => 'vendor']);
        $vendor->update(['user_id' => $user->id]);

        // Create test data for vendor
        $products = Product::factory()->count(20)->create(['vendor_id' => $vendor->id]);
        
        foreach ($products->take(10) as $product) {
            Order::factory()->count(2)->create(['vendor_id' => $vendor->id])
                ->each(function ($order) use ($product) {
                    OrderItem::factory()->create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                    ]);
                });
        }

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Simulate vendor dashboard data loading
        $vendorProducts = Product::where('vendor_id', $vendor->id)
            ->with(['category', 'brand'])
            ->paginate(10);

        $recentOrders = Order::where('vendor_id', $vendor->id)
            ->with(['user', 'items.product'])
            ->latest()
            ->limit(5)
            ->get();

        $totalEarnings = Order::where('vendor_id', $vendor->id)
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(10, $queryCount, 'Vendor dashboard should use minimal queries');
        $this->assertLessThan(200, $executionTime, 'Vendor dashboard should load in under 200ms');
    }

    /** @test */
    public function order_processing_queries_are_optimized()
    {
        $vendor = Vendor::factory()->create();
        $customer = User::factory()->create(['user_type' => 'customer']);
        $products = Product::factory()->count(5)->create(['vendor_id' => $vendor->id]);

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Simulate order creation process
        DB::transaction(function () use ($vendor, $customer, $products) {
            $order = Order::create([
                'user_id' => $customer->id,
                'vendor_id' => $vendor->id,
                'total_amount' => 1000.00,
                'payment_status' => 'pending',
                'status' => 'pending',
            ]);

            foreach ($products->take(3) as $product) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'price' => $product->price,
                ]);
            }

            // Update product stock
            foreach ($products->take(3) as $product) {
                $product->decrement('stock_quantity', 1);
            }
        });

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(15, $queryCount, 'Order creation should use reasonable number of queries');
        $this->assertLessThan(300, $executionTime, 'Order creation should complete in under 300ms');
    }

    /** @test */
    public function search_functionality_is_performant()
    {
        // Create test data
        $vendors = Vendor::factory()->count(5)->create();
        $categories = Category::factory()->count(3)->create();
        $brands = Brand::factory()->count(3)->create();

        // Create products with searchable content
        Product::factory()->count(50)->create([
            'vendor_id' => $vendors->random()->id,
            'category_id' => $categories->random()->id,
            'brand_id' => $brands->random()->id,
            'name' => 'Test Product ' . fake()->word(),
            'description' => 'This is a test product description with searchable content',
        ]);

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Test search functionality
        $searchResults = Product::where('name', 'LIKE', '%Test%')
            ->orWhere('description', 'LIKE', '%test%')
            ->with(['vendor', 'category', 'brand'])
            ->paginate(20);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(5, $queryCount, 'Search should use minimal queries');
        $this->assertLessThan(150, $executionTime, 'Search should execute in under 150ms');
        $this->assertGreaterThan(0, $searchResults->count(), 'Search should return results');
    }

    /** @test */
    public function category_filtering_is_optimized()
    {
        $category = Category::factory()->create(['name' => 'Electronics']);
        $vendors = Vendor::factory()->count(3)->create();
        $brands = Brand::factory()->count(3)->create();

        // Create products in the category
        Product::factory()->count(30)->create([
            'category_id' => $category->id,
            'vendor_id' => $vendors->random()->id,
            'brand_id' => $brands->random()->id,
        ]);

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Test category filtering
        $categoryProducts = Product::where('category_id', $category->id)
            ->with(['vendor', 'brand'])
            ->paginate(20);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(4, $queryCount, 'Category filtering should use minimal queries');
        $this->assertLessThan(100, $executionTime, 'Category filtering should execute in under 100ms');
    }

    /** @test */
    public function vendor_earnings_calculation_is_optimized()
    {
        $vendor = Vendor::factory()->create();
        
        // Create multiple orders for the vendor
        $orders = Order::factory()->count(20)->create([
            'vendor_id' => $vendor->id,
            'payment_status' => 'paid',
            'total_amount' => 1000.00,
        ]);

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Calculate vendor earnings using optimized query
        $totalEarnings = Order::where('vendor_id', $vendor->id)
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        $orderCount = Order::where('vendor_id', $vendor->id)
            ->where('payment_status', 'paid')
            ->count();

        $averageOrderValue = $orderCount > 0 ? $totalEarnings / $orderCount : 0;

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(3, $queryCount, 'Earnings calculation should use minimal queries');
        $this->assertLessThan(50, $executionTime, 'Earnings calculation should execute in under 50ms');
        $this->assertEquals(20000.00, $totalEarnings); // 20 orders * 1000 each
    }

    /** @test */
    public function bulk_operations_are_optimized()
    {
        $vendor = Vendor::factory()->create();
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Test bulk product creation
        $productData = [];
        for ($i = 0; $i < 100; $i++) {
            $productData[] = [
                'vendor_id' => $vendor->id,
                'category_id' => $category->id,
                'brand_id' => $brand->id,
                'name' => "Bulk Product {$i}",
                'description' => "Description for bulk product {$i}",
                'price' => 100.00,
                'stock_quantity' => 10,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Use chunk to avoid memory issues
        collect($productData)->chunk(50)->each(function ($chunk) {
            Product::insert($chunk->toArray());
        });

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(5, $queryCount, 'Bulk operations should use minimal queries');
        $this->assertLessThan(500, $executionTime, 'Bulk operations should complete in under 500ms');
        $this->assertEquals(100, Product::where('vendor_id', $vendor->id)->count());
    }

    /** @test */
    public function complex_reporting_queries_are_optimized()
    {
        $vendors = Vendor::factory()->count(5)->create();
        $categories = Category::factory()->count(3)->create();
        $brands = Brand::factory()->count(3)->create();

        // Create products and orders for reporting
        foreach ($vendors as $vendor) {
            $products = Product::factory()->count(5)->create([
                'vendor_id' => $vendor->id,
                'category_id' => $categories->random()->id,
                'brand_id' => $brands->random()->id,
            ]);

            foreach ($products as $product) {
                Order::factory()->count(3)->create([
                    'vendor_id' => $vendor->id,
                    'payment_status' => 'paid',
                    'total_amount' => 500.00,
                ])->each(function ($order) use ($product) {
                    OrderItem::factory()->create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'quantity' => 1,
                        'price' => 500.00,
                    ]);
                });
            }
        }

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Complex reporting query
        $report = DB::table('orders')
            ->join('vendors', 'orders.vendor_id', '=', 'vendors.id')
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->join('categories', 'products.category_id', '=', 'categories.id')
            ->select(
                'vendors.business_name',
                'categories.name as category_name',
                DB::raw('COUNT(orders.id) as total_orders'),
                DB::raw('SUM(orders.total_amount) as total_revenue'),
                DB::raw('AVG(orders.total_amount) as avg_order_value')
            )
            ->where('orders.payment_status', 'paid')
            ->groupBy('vendors.id', 'vendors.business_name', 'categories.id', 'categories.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        // Performance assertions
        $this->assertLessThan(3, $queryCount, 'Complex reporting should use minimal queries');
        $this->assertLessThan(300, $executionTime, 'Complex reporting should execute in under 300ms');
        $this->assertGreaterThan(0, $report->count(), 'Report should return data');
    }

    /** @test */
    public function database_indexes_are_effective()
    {
        // Create test data
        $vendor = Vendor::factory()->create();
        Product::factory()->count(1000)->create(['vendor_id' => $vendor->id]);

        DB::flushQueryLog();

        $startTime = microtime(true);

        // Test queries that should benefit from indexes
        $productsByVendor = Product::where('vendor_id', $vendor->id)->count();
        $approvedProducts = Product::where('is_approved', true)->count();
        $featuredProducts = Product::where('is_featured', true)->count();

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        // Performance assertions for indexed queries
        $this->assertLessThan(50, $executionTime, 'Indexed queries should execute quickly');
        
        // Verify the queries returned expected results
        $this->assertEquals(1000, $productsByVendor);
    }

    /** @test */
    public function pagination_performance_is_consistent()
    {
        // Create large dataset
        $vendors = Vendor::factory()->count(10)->create();
        $categories = Category::factory()->count(5)->create();
        $brands = Brand::factory()->count(5)->create();

        Product::factory()->count(500)->create([
            'vendor_id' => $vendors->random()->id,
            'category_id' => $categories->random()->id,
            'brand_id' => $brands->random()->id,
        ]);

        // Test pagination performance at different pages
        $pages = [1, 5, 10, 20];
        $executionTimes = [];

        foreach ($pages as $page) {
            DB::flushQueryLog();
            $startTime = microtime(true);

            Product::with(['vendor', 'category', 'brand'])
                ->paginate(25, ['*'], 'page', $page);

            $endTime = microtime(true);
            $executionTimes[$page] = ($endTime - $startTime) * 1000;

            $queries = DB::getQueryLog();
            $this->assertLessThan(5, count($queries), "Page {$page} should use minimal queries");
        }

        // Verify pagination performance is consistent across pages
        $maxTime = max($executionTimes);
        $minTime = min($executionTimes);
        $timeDifference = $maxTime - $minTime;

        $this->assertLessThan(100, $timeDifference, 'Pagination performance should be consistent across pages');
    }
}
