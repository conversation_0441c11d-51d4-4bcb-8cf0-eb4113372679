<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Size;
use App\Models\ProductVariant;
use App\Traits\ProductValidationRules;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{
    use ProductValidationRules;
    /**
     * Display a listing of the vendor's products.
     */
    public function index()
    {
        $products = auth()->user()->vendor->products()->latest()->paginate(10);
        return view('vendor.products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $vendor = auth()->user()->vendor;
        $subscription = $vendor->subscription;

        // Check if the vendor's current plan has an order limit
        if ($subscription && $subscription->subscriptionPlan && $subscription->subscriptionPlan->order_limit !== null) {
            $plan = $subscription->subscriptionPlan;
            $limit = $plan->order_limit;

            // Count orders within the current subscription period
            $orderCount = $vendor->orders()
                ->where('created_at', '>=', $subscription->starts_at)
                ->where('created_at', '<=', $subscription->ends_at)
                ->count();

            if ($orderCount >= $limit) {
                return redirect()->route('vendor.subscription.index')
                    ->with('error', 'You have reached your monthly order limit. Please upgrade your plan to add more products.');
            }
        }

        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        return view('vendor.products.create', compact('categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $vendor = auth()->user()->vendor;

        // LOGIC-CRITICAL-001 FIX: Use standardized validation rules
        $rules = array_merge(
            $this->getProductValidationRules(
                isUpdate: false,
                requireVendor: false // Vendor is auto-assigned
            ),
            $this->getProductImageValidationRules('image', false),
            $this->getProductVariantValidationRules(),
            [
                'specifications' => 'nullable|array',
                'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
                'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
            ]
        );

        $validatedData = $request->validate(
            $rules,
            $this->getProductValidationMessages(),
            $this->getProductValidationAttributes()
        );

        $productData = $request->only(['name', 'category_id', 'description', 'price', 'discount_price', 'weight', 'height', 'width', 'length']);
        $productData['vendor_id'] = $vendor->id;
        if ($vendor->brand) {
            $productData['brand_id'] = $vendor->brand->id; // Auto-assign vendor's brand
        } else {
            $productData['brand_id'] = null; // Set brand_id to null if vendor has no brand
        }
        $productData['is_active'] = $request->has('is_active');
        $productData['slug'] = Str::slug($request->name) . '-' . uniqid();

        $product = Product::create($productData);

        // FIXED: Use Spatie Media Library for consistent file handling
        if ($request->hasFile('image')) {
            $product->addMediaFromRequest('image')
                ->toMediaCollection('product_images');
        }

        if ($request->has('variants')) {
            foreach ($request->variants as $index => $variantData) {
                if (empty($variantData['name']) || empty($variantData['value'])) {
                    continue;
                }

                $newVariantData = [
                    'product_id' => $product->id,
                    'name' => $variantData['name'],
                    'value' => $variantData['value'],
                    'price' => $variantData['price'] ?? null,
                ];

                if ($request->hasFile("variants.{$index}.image")) {
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'filebase');
                    $newVariantData['image_path'] = $variantImagePath;
                }

                $product->variants()->create($newVariantData);
            }
        }

        if ($request->has('specifications')) {
            foreach ($request->specifications as $specData) {
                if (empty($specData['name']) || empty($specData['value'])) {
                    continue;
                }
                $product->specifications()->create($specData);
            }
        }

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        // Use Laravel Policy for authorization
        $this->authorize('update', $product);

        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        $product->load('variants.color', 'variants.size');
        return view('vendor.products.edit', compact('product', 'categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        // Use Laravel Policy for authorization
        $this->authorize('update', $product);

        // CRITICAL FIX DV1/FV1: Add missing stock validation for consistency
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock' => 'required|integer|min:0', // ADDED: Missing stock validation
            'image' => 'nullable|image|max:2048',
            'is_active' => 'sometimes|boolean',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'sku' => 'nullable|string|max:255', // ADDED: Missing SKU validation
            'variants' => 'nullable|array',
            'variants.*.id' => 'nullable|exists:product_variants,id',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
            'specifications' => 'nullable|array',
            'specifications.*.id' => 'nullable|exists:product_specifications,id',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ]);

        // SECURITY FIX: Use validated data and explicitly prevent mass assignment of sensitive fields.
        $productData = $validatedData;

        // When a product is updated, it must be marked as inactive pending admin review.
        $productData['is_active'] = false;
        $productData['admin_status'] = 'pending';

        $product->update($productData);

        // FIXED: Use Spatie Media Library for consistent file handling
        if ($request->hasFile('image')) {
            // Clear existing images and add new one
            $product->clearMediaCollection('product_images');
            $product->addMediaFromRequest('image')
                ->toMediaCollection('product_images');
        }

        // Handle Variants
        $submittedVariantIds = [];
        if ($request->has('variants')) {
            foreach ($request->variants as $index => $variantData) {
                if (empty($variantData['name']) || empty($variantData['value'])) {
                    continue;
                }

                $variantDetails = [
                    'name' => $variantData['name'],
                    'value' => $variantData['value'],
                    'price' => $variantData['price'] ?? null,
                ];

                $variant = null;
                if (!empty($variantData['id'])) {
                    $variant = $product->variants()->find($variantData['id']);
                }

                if ($request->hasFile("variants.{$index}.image")) {
                    if ($variant && $variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                        Storage::disk('filebase')->delete($variant->image_path);
                    }
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'filebase');
                    $variantDetails['image_path'] = $variantImagePath;
                }

                if ($variant) { // Update existing variant
                    $variant->update($variantDetails);
                    $submittedVariantIds[] = $variant->id;
                } else { // Create new variant
                    $newVariant = $product->variants()->create($variantDetails);
                    $submittedVariantIds[] = $newVariant->id;
                }
            }
        }
        // Delete variants that were not in the submission
        $variantsToDelete = $product->variants()->whereNotIn('id', $submittedVariantIds);
        foreach ($variantsToDelete->get() as $variant) {
            if ($variant->image_path && Storage::disk('filebase')->exists($variant->image_path)) {
                Storage::disk('filebase')->delete($variant->image_path);
            }
            $variant->delete();
        }


        // Handle Specifications
        $submittedSpecIds = [];
        if ($request->has('specifications')) {
            foreach ($request->specifications as $specData) {
                if (empty($specData['name']) || empty($specData['value'])) {
                    continue;
                }

                if (!empty($specData['id'])) { // Update existing
                    $spec = $product->specifications()->find($specData['id']);
                    if ($spec) {
                        $spec->update($specData);
                        $submittedSpecIds[] = $spec->id;
                    }
                } else { // Create new
                    $newSpec = $product->specifications()->create($specData);
                    $submittedSpecIds[] = $newSpec->id;
                }
            }
        }
        // Delete specifications that were not in the submission
        $product->specifications()->whereNotIn('id', $submittedSpecIds)->delete();


        return redirect()->route('vendor.products.index')
            ->with('success', 'Product updated successfully.');
    }



    /**
     * Remove the specified product from storage.
     * FIXED: Use soft delete instead of hard delete to preserve data integrity.
     */
    public function destroy(Product $product)
    {
        // Use Laravel Policy for authorization
        $this->authorize('delete', $product);

        // FIXED: Use soft delete instead of hard delete
        // This preserves the product data for orders and analytics while hiding it from public view
        $product->delete(); // This is actually a soft delete because Product model uses SoftDeletes trait

        // Note: We don't delete the product image files when soft deleting
        // as the product might need to be restored later, and the images are still
        // referenced in existing orders

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product deleted successfully. It can be restored if needed.');
    }
}
