@extends('layouts.customer')

@section('customer-content')
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold">My Wishlist</h2>
        <p class="text-gray-500 mt-1">Your saved items and favorites.</p>
    </div>
    <div class="p-6">
        @php
            $wishlistItems = auth()->user()->wishlist()->with('product')->get();
            $products = $wishlistItems->map(function ($item) {
                return $item->product;
            })->filter()->unique('id');
        @endphp

        @if($products->isNotEmpty())
            <div class="mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($products as $product)
                        <livewire:product-card :product="$product" :key="$product->id" />
                    @endforeach
                </div>
            </div>

            <div class="flex justify-end mt-6 pt-6 border-t border-gray-200">
                <form action="{{ route('wishlist.clear') }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150">
                        Clear Wishlist
                    </button>
                </form>
            </div>
        @else
            <div class="text-center py-16">
                <i class="far fa-heart fa-4x text-gray-300 mb-4"></i>
                <h4 class="font-bold text-xl mt-4">Your Wishlist is Empty</h4>
                <p class="text-gray-500 mt-2">Looks like you haven't added anything to your wishlist yet. <br>Start exploring and save your favorites!</p>
                <a href="{{ route('products.index') }}" class="mt-6 inline-flex items-center px-4 py-2 bg-black text-white border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-800 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    Start Shopping
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
