<?php

namespace App\Livewire\Vendor\Products;

use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $status = 'all'; // all, active, inactive
    public $category = 'all';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';

    // Bulk operations
    public $selectedProducts = [];
    public $selectAll = false;

    public function mount()
    {

    }

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => 'all'],
        'category' => ['except' => 'all'],
    ];

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatus()
    {
        $this->resetPage();
    }

    public function updatedCategory()
    {
        $this->resetPage();
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedProducts = $this->getProducts()->pluck('id')->toArray();
        } else {
            $this->selectedProducts = [];
        }
    }



    public function toggleProductStatus($productId)
    {
        $product = Auth::user()->vendor->products()->findOrFail($productId);
        $product->update(['is_active' => !$product->is_active]);

        $status = $product->is_active ? 'activated' : 'deactivated';
        session()->flash('success', "Product {$status} successfully.");
    }

    public function bulkDelete()
    {
        if (empty($this->selectedProducts)) {
            session()->flash('error', 'Please select products to delete.');
            return;
        }

        Auth::user()->vendor->products()->whereIn('id', $this->selectedProducts)->delete();

        $count = count($this->selectedProducts);
        $this->selectedProducts = [];
        $this->selectAll = false;

        session()->flash('success', "{$count} products deleted successfully.");
    }

    public function bulkToggleStatus($status)
    {
        if (empty($this->selectedProducts)) {
            session()->flash('error', 'Please select products to update.');
            return;
        }

        Auth::user()->vendor->products()->whereIn('id', $this->selectedProducts)
            ->update(['is_active' => $status === 'activate']);

        $count = count($this->selectedProducts);
        $action = $status === 'activate' ? 'activated' : 'deactivated';
        $this->selectedProducts = [];
        $this->selectAll = false;

        session()->flash('success', "{$count} products {$action} successfully.");
    }

    private function getProducts()
    {
        $query = Auth::user()->vendor->products()->with(['category', 'variants']);

        // Apply search filter
        if (!empty($this->search)) {
            $searchTerm = '%' . $this->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm)
                  ->orWhereHas('category', function ($categoryQuery) use ($searchTerm) {
                      $categoryQuery->where('name', 'like', $searchTerm);
                  });
            });
        }

        // Apply status filter
        if ($this->status !== 'all') {
            $query->where('is_active', $this->status === 'active');
        }

        // Apply category filter
        if ($this->category !== 'all') {
            $query->where('category_id', $this->category);
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query;
    }

    /**
     * Delete a product after authorization.
     */
    public function deleteProduct(Product $product)
    {
        try {
            // Authorize the action
            $this->authorize('delete', $product);

            // Delete the product
            $product->delete();

            // Flash a success message
            session()->flash('success', 'Product "' . $product->name . '" has been deleted successfully.');

        } catch (AuthorizationException $e) {
            // User is not authorized to delete the product
            session()->flash('error', 'You are not authorized to delete this product.');
        } catch (\Exception $e) {
            // Handle any other exceptions
            session()->flash('error', 'An error occurred while deleting the product.');
            Log::error('Error deleting product: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $products = $this->getProducts()->paginate(10);

        // PERFORMANCE FIX: Efficiently retrieve categories for the filter dropdown.
        $categories = \App\Models\Category::whereHas('products', function ($query) {
            $query->where('vendor_id', Auth::user()->vendor->id);
        })
        ->select('id', 'name')
        ->distinct()
        ->orderBy('name')
        ->get();

        return view('livewire.vendor.products.index', [
            'products' => $products,
            'categories' => $categories,
        ]);
    }
}
