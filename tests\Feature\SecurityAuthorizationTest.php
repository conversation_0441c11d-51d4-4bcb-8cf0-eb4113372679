<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Order;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Size;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SecurityAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $vendorUser;
    protected $customerUser;
    protected $vendor;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create users with different roles
        $this->adminUser = User::factory()->create(['user_type' => 'admin']);
        $this->vendorUser = User::factory()->create(['user_type' => 'vendor']);
        $this->customerUser = User::factory()->create(['user_type' => 'customer']);

        // Create vendor and associate with vendor user
        $this->vendor = Vendor::factory()->create(['user_id' => $this->vendorUser->id]);

        // Create test product
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $category->id,
            'brand_id' => $brand->id,
        ]);
    }

    /** @test */
    public function admin_can_access_admin_panel()
    {
        $response = $this->actingAs($this->adminUser)->get('/admin/dashboard');
        $response->assertStatus(200);
    }

    /** @test */
    public function vendor_cannot_access_admin_panel()
    {
        $response = $this->actingAs($this->vendorUser)->get('/admin/dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function customer_cannot_access_admin_panel()
    {
        $response = $this->actingAs($this->customerUser)->get('/admin/dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_admin_panel()
    {
        $response = $this->get('/admin/dashboard');
        $response->assertRedirect('/login');
    }

    /** @test */
    public function vendor_can_access_own_vendor_dashboard()
    {
        $response = $this->actingAs($this->vendorUser)->get('/vendor/dashboard');
        $response->assertStatus(200);
    }

    /** @test */
    public function customer_cannot_access_vendor_dashboard()
    {
        $response = $this->actingAs($this->customerUser)->get('/vendor/dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function vendor_can_only_edit_own_products()
    {
        // Create another vendor and product
        $otherVendor = Vendor::factory()->create();
        $otherProduct = Product::factory()->create([
            'vendor_id' => $otherVendor->id,
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
        ]);

        // Vendor can edit own product
        $response = $this->actingAs($this->vendorUser)->get("/vendor/products/{$this->product->id}/edit");
        $response->assertStatus(200);

        // Vendor cannot edit other vendor's product
        $response = $this->actingAs($this->vendorUser)->get("/vendor/products/{$otherProduct->id}/edit");
        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_manage_all_products()
    {
        // Create another vendor and product
        $otherVendor = Vendor::factory()->create();
        $otherProduct = Product::factory()->create([
            'vendor_id' => $otherVendor->id,
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
        ]);

        // Admin can manage any product
        $response = $this->actingAs($this->adminUser)->get("/admin/products/{$this->product->id}");
        $response->assertStatus(200);

        $response = $this->actingAs($this->adminUser)->get("/admin/products/{$otherProduct->id}");
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_manage_colors_and_sizes()
    {
        // Test Colors management
        $response = $this->actingAs($this->adminUser)->get('/admin/colors');
        $response->assertStatus(200);

        $response = $this->actingAs($this->adminUser)->post('/admin/colors', [
            'name' => 'Test Color',
            'hex_code' => '#FF0000',
        ]);
        $response->assertStatus(302); // Redirect after creation

        // Test Sizes management
        $response = $this->actingAs($this->adminUser)->get('/admin/sizes');
        $response->assertStatus(200);

        $response = $this->actingAs($this->adminUser)->post('/admin/sizes', [
            'name' => 'Test Size',
            'abbreviation' => 'TS',
        ]);
        $response->assertStatus(302); // Redirect after creation
    }

    /** @test */
    public function vendor_cannot_manage_colors_and_sizes()
    {
        $response = $this->actingAs($this->vendorUser)->get('/admin/colors');
        $response->assertStatus(403);

        $response = $this->actingAs($this->vendorUser)->get('/admin/sizes');
        $response->assertStatus(403);

        $response = $this->actingAs($this->vendorUser)->post('/admin/colors', [
            'name' => 'Test Color',
            'hex_code' => '#FF0000',
        ]);
        $response->assertStatus(403);
    }

    /** @test */
    public function vendor_can_only_view_own_orders()
    {
        // Create orders for different vendors
        $ownOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'user_id' => $this->customerUser->id,
        ]);

        $otherVendor = Vendor::factory()->create();
        $otherOrder = Order::factory()->create([
            'vendor_id' => $otherVendor->id,
            'user_id' => $this->customerUser->id,
        ]);

        // Vendor can view own order
        $response = $this->actingAs($this->vendorUser)->get("/vendor/orders/{$ownOrder->id}");
        $response->assertStatus(200);

        // Vendor cannot view other vendor's order
        $response = $this->actingAs($this->vendorUser)->get("/vendor/orders/{$otherOrder->id}");
        $response->assertStatus(403);
    }

    /** @test */
    public function customer_can_only_view_own_orders()
    {
        $ownOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'user_id' => $this->customerUser->id,
        ]);

        $otherCustomer = User::factory()->create(['user_type' => 'customer']);
        $otherOrder = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'user_id' => $otherCustomer->id,
        ]);

        // Customer can view own order
        $response = $this->actingAs($this->customerUser)->get("/orders/{$ownOrder->id}");
        $response->assertStatus(200);

        // Customer cannot view other customer's order
        $response = $this->actingAs($this->customerUser)->get("/orders/{$otherOrder->id}");
        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_view_all_orders()
    {
        $order1 = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'user_id' => $this->customerUser->id,
        ]);

        $order2 = Order::factory()->create([
            'vendor_id' => $this->vendor->id,
            'user_id' => User::factory()->create()->id,
        ]);

        // Admin can view any order
        $response = $this->actingAs($this->adminUser)->get("/admin/orders/{$order1->id}");
        $response->assertStatus(200);

        $response = $this->actingAs($this->adminUser)->get("/admin/orders/{$order2->id}");
        $response->assertStatus(200);
    }

    /** @test */
    public function vendor_cannot_access_other_vendor_financial_data()
    {
        $otherVendor = Vendor::factory()->create();
        $otherVendorUser = User::factory()->create(['user_type' => 'vendor']);
        $otherVendor->update(['user_id' => $otherVendorUser->id]);

        // Vendor can access own financial data
        $response = $this->actingAs($this->vendorUser)->get('/vendor/earnings');
        $response->assertStatus(200);

        // Try to access other vendor's financial data (should be prevented by middleware)
        $response = $this->actingAs($this->vendorUser)->get("/vendor/{$otherVendor->id}/earnings");
        $response->assertStatus(404); // Route not found or 403 Forbidden
    }

    /** @test */
    public function api_endpoints_require_proper_authentication()
    {
        // Test API endpoints without authentication
        $response = $this->getJson('/api/products');
        $response->assertStatus(401);

        $response = $this->postJson('/api/cart/add', [
            'product_id' => $this->product->id,
            'quantity' => 1,
        ]);
        $response->assertStatus(401);

        // Test with authentication
        $response = $this->actingAs($this->customerUser, 'api')->getJson('/api/products');
        $response->assertStatus(200);
    }

    /** @test */
    public function csrf_protection_is_enforced_on_state_changing_requests()
    {
        // Test POST request without CSRF token
        $response = $this->post('/vendor/products', [
            'name' => 'Test Product',
            'price' => 100,
        ]);
        $response->assertStatus(419); // CSRF token mismatch

        // Test with proper CSRF token
        $response = $this->actingAs($this->vendorUser)
            ->withSession(['_token' => 'test-token'])
            ->post('/vendor/products', [
                '_token' => 'test-token',
                'name' => 'Test Product',
                'price' => 100,
            ]);
        // Should not get 419 error (might get validation errors instead)
        $response->assertStatus(302); // Redirect or other non-419 status
    }

    /** @test */
    public function sensitive_routes_are_protected_from_unauthorized_access()
    {
        $sensitiveRoutes = [
            '/admin/users',
            '/admin/vendors',
            '/admin/settings',
            '/admin/reports',
            '/vendor/earnings',
            '/vendor/withdrawals',
        ];

        foreach ($sensitiveRoutes as $route) {
            // Unauthenticated access
            $response = $this->get($route);
            $this->assertContains($response->status(), [302, 401, 403], 
                "Route {$route} should be protected from unauthenticated access");

            // Customer access to admin/vendor routes
            if (str_starts_with($route, '/admin') || str_starts_with($route, '/vendor')) {
                $response = $this->actingAs($this->customerUser)->get($route);
                $this->assertContains($response->status(), [403, 404], 
                    "Route {$route} should be protected from customer access");
            }
        }
    }

    /** @test */
    public function file_upload_endpoints_validate_file_types()
    {
        // Test product image upload with invalid file type
        $response = $this->actingAs($this->vendorUser)
            ->post("/vendor/products/{$this->product->id}/images", [
                'image' => \Illuminate\Http\UploadedFile::fake()->create('malicious.exe', 1000),
            ]);
        
        $response->assertStatus(422); // Validation error
    }

    /** @test */
    public function rate_limiting_is_applied_to_sensitive_endpoints()
    {
        // Test login endpoint rate limiting
        for ($i = 0; $i < 10; $i++) {
            $response = $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);
        }

        // After multiple failed attempts, should be rate limited
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);
        
        $this->assertContains($response->status(), [429, 422]); // Too many requests or validation error
    }

    /** @test */
    public function sql_injection_attempts_are_prevented()
    {
        // Test SQL injection in search parameters
        $maliciousInput = "'; DROP TABLE products; --";
        
        $response = $this->get("/products?search=" . urlencode($maliciousInput));
        $response->assertStatus(200); // Should not crash

        // Verify products table still exists
        $this->assertDatabaseHas('products', ['id' => $this->product->id]);
    }

    /** @test */
    public function xss_attempts_are_sanitized()
    {
        // Test XSS in product creation
        $xssPayload = '<script>alert("XSS")</script>';
        
        $response = $this->actingAs($this->vendorUser)->post('/vendor/products', [
            'name' => $xssPayload,
            'description' => $xssPayload,
            'price' => 100,
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
        ]);

        // Check that script tags are not stored or are properly escaped
        if ($response->status() === 302) { // Successful creation
            $product = Product::latest()->first();
            $this->assertStringNotContainsString('<script>', $product->name);
            $this->assertStringNotContainsString('<script>', $product->description);
        }
    }

    /** @test */
    public function mass_assignment_vulnerabilities_are_prevented()
    {
        // Try to mass assign protected attributes
        $response = $this->actingAs($this->vendorUser)->post('/vendor/products', [
            'name' => 'Test Product',
            'price' => 100,
            'is_approved' => true, // Should not be mass assignable
            'featured' => true, // Should not be mass assignable
            'vendor_id' => 999, // Should not be mass assignable
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
        ]);

        if ($response->status() === 302) { // Successful creation
            $product = Product::latest()->first();
            $this->assertFalse($product->is_approved); // Should remain false
            $this->assertEquals($this->vendor->id, $product->vendor_id); // Should be set to current vendor
        }
    }
}
