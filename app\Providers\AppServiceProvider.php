<?php

namespace App\Providers;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Enhanced currency directive with consistent formatting
        Blade::directive('currency', function ($expression) {
            $currencySymbol = config('brandify.currency.symbol', '₦');
            return "<?php echo '{$currencySymbol}' . number_format(floatval($expression), 2, '.', ','); ?>";
        });

        // Additional currency directive for whole numbers (no decimals)
        Blade::directive('currencyWhole', function ($expression) {
            $currencySymbol = config('brandify.currency.symbol', '₦');
            return "<?php echo '{$currencySymbol}' . number_format(floatval($expression), 0, '.', ','); ?>";
        });

        Paginator::useTailwind();

        // Register a safe array_key_exists helper
        if (!function_exists('safe_array_key_exists')) {
            function safe_array_key_exists($key, $array) {
                if (!is_array($array)) {
                    \Log::warning('safe_array_key_exists called with non-array', [
                        'key' => $key,
                        'array_type' => gettype($array),
                        'array_value' => $array,
                        'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
                    ]);
                    return false;
                }
                return array_key_exists($key, $array);
            }
        }

        // Add comprehensive error handler for array_key_exists errors
        if (app()->environment('local', 'staging')) {
            // Override the global error handler to catch array_key_exists errors
            set_error_handler(function($severity, $message, $file, $line, $context = []) {
                if (strpos($message, 'array_key_exists') !== false) {
                    $trace = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 20);

                    \Log::error('CRITICAL: array_key_exists error detected', [
                        'message' => $message,
                        'file' => $file,
                        'line' => $line,
                        'severity' => $severity,
                        'context' => $context,
                        'full_trace' => $trace,
                        'request_url' => request()->fullUrl(),
                        'request_method' => request()->method(),
                        'user_id' => auth()->id(),
                        'session_data' => session()->all()
                    ]);

                    // Try to identify the exact function call
                    foreach ($trace as $index => $frame) {
                        if (isset($frame['function']) && $frame['function'] === 'array_key_exists') {
                            \Log::error('Found array_key_exists call', [
                                'frame_index' => $index,
                                'frame' => $frame,
                                'args' => $frame['args'] ?? 'no_args'
                            ]);
                            break;
                        }
                    }

                    // Don't let the error propagate in production-like environments
                    if (app()->environment('local')) {
                        throw new \ErrorException($message, 0, $severity, $file, $line);
                    }
                    return true; // Suppress the error
                }
                return false; // Let PHP handle other errors normally
            });
        }
    }
}
