<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;

class Role extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    /**
     * SECURITY FIX: Define allowed role names to prevent invalid roles
     */
    const ALLOWED_ROLES = ['admin', 'vendor', 'customer'];

    /**
     * SECURITY FIX: Boot method to enforce role validation
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($role) {
            // Normalize role name
            $role->name = strtolower(trim($role->name));

            // Validate role name
            if (!in_array($role->name, self::ALLOWED_ROLES)) {
                throw new \InvalidArgumentException('Invalid role name: ' . $role->name);
            }
        });

        static::updating(function ($role) {
            // Normalize role name
            $role->name = strtolower(trim($role->name));

            // Validate role name
            if (!in_array($role->name, self::ALLOWED_ROLES)) {
                throw new \InvalidArgumentException('Invalid role name: ' . $role->name);
            }
        });
    }

    /**
     * SECURITY FIX: Validation rules for role creation/update
     */
    public static function validationRules($id = null)
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::in(self::ALLOWED_ROLES),
                Rule::unique('roles', 'name')->ignore($id)
            ]
        ];
    }

    /**
     * SECURITY FIX: Check if role name is valid
     */
    public static function isValidRole(string $roleName): bool
    {
        return in_array(strtolower(trim($roleName)), self::ALLOWED_ROLES);
    }

    /**
     * SECURITY FIX: Get all allowed roles
     */
    public static function getAllowedRoles(): array
    {
        return self::ALLOWED_ROLES;
    }

    /**
     * Relationship: Users with this role
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * SECURITY FIX: Scope for safe role queries
     */
    public function scopeAllowed($query)
    {
        return $query->whereIn('name', self::ALLOWED_ROLES);
    }
}
