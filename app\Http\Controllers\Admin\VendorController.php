<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class VendorController extends Controller
{
    /**
     * Show the form for creating a new vendor
     */
    public function create()
    {
        $roles = Role::where('name', '!=', 'admin')->get();
        return view('admin.vendors.create', compact('roles'));
    }

    /**
     * Show the form for editing the specified vendor
     */
    public function edit(Vendor $vendor)
    {
        // Load vendor with user relationship
        $vendor->load('user');

        // Load roles for dropdown
        $roles = Role::where('name', '!=', 'admin')->get();

        return view('admin.vendors.edit', compact('vendor', 'roles'));
    }

    /**
     * SECURITY FIX: Secure vendor deletion with proper authorization and data integrity checks
     */
    public function destroy(Vendor $vendor)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // SECURITY FIX: Additional gate-based authorization
        if (!Gate::allows('manage-users')) {
            abort(403, 'Unauthorized to delete vendors.');
        }

        // BUSINESS LOGIC FIX: Check for dependent data before deletion
        $hasActiveOrders = $vendor->orders()->whereIn('status', ['pending', 'processing', 'shipped'])->exists();
        $hasActiveProducts = $vendor->products()->where('is_active', true)->exists();
        $hasActiveSubscription = $vendor->subscription && $vendor->subscription->status === 'active';
        $hasPendingWithdrawals = $vendor->withdrawals()->where('status', 'pending')->exists();

        if ($hasActiveOrders || $hasActiveProducts || $hasActiveSubscription || $hasPendingWithdrawals) {
            $reasons = [];
            if ($hasActiveOrders) $reasons[] = 'active orders';
            if ($hasActiveProducts) $reasons[] = 'active products';
            if ($hasActiveSubscription) $reasons[] = 'active subscription';
            if ($hasPendingWithdrawals) $reasons[] = 'pending withdrawals';

            return redirect()->route('admin.vendors.index')
                ->with('error', 'Cannot delete vendor with ' . implode(', ', $reasons) . '. Please resolve these first.');
        }

        // SECURITY FIX: Handle cascading effects properly with transaction
        DB::transaction(function () use ($vendor) {
            // Log the deletion attempt for audit trail
            \Log::warning('Vendor deletion initiated by admin', [
                'admin_user_id' => Auth::id(),
                'admin_email' => Auth::user()->email,
                'vendor_id' => $vendor->id,
                'vendor_shop_name' => $vendor->shop_name,
                'vendor_user_id' => $vendor->user_id,
                'timestamp' => now()
            ]);

            // Archive vendor financial data before deletion
            if ($vendor->total_earnings > 0 || $vendor->available_balance > 0) {
                \Log::info('Vendor with financial data being deleted', [
                    'vendor_id' => $vendor->id,
                    'total_earnings' => $vendor->total_earnings,
                    'available_balance' => $vendor->available_balance,
                    'pending_balance' => $vendor->pending_balance,
                    'withdrawn_amount' => $vendor->withdrawn_amount
                ]);
            }

            // Soft delete related products first (they use SoftDeletes trait)
            $vendor->products()->delete();

            // Archive vendor transactions
            $vendor->transactions()->update(['archived_at' => now()]);

            // Delete the vendor (this will also handle the user relationship if needed)
            $vendor->delete();

            \Log::info('Vendor successfully deleted by admin', [
                'admin_user_id' => Auth::id(),
                'deleted_vendor_id' => $vendor->id,
                'timestamp' => now()
            ]);
        });

        return redirect()->route('admin.vendors.index')
            ->with('success', 'Vendor deleted successfully. All related data has been properly archived.');
    }
}
