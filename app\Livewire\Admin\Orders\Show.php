<?php

namespace App\Livewire\Admin\Orders;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Show extends Component
{

    public Order $order;
    public string $status;

    public function mount(Order $order)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        $this->order = $order->load(['user', 'items.product.featured_image']);
        $this->status = $this->order->status;
    }

    public function updateStatus()
    {
        $this->validate(['status' => 'required|in:pending,processing,shipped,delivered,cancelled']);

        // SECURITY FIX: Use direct assignment instead of mass assignment
        // since status is in the guarded array for security
        $this->order->status = $this->status;
        $this->order->save();

        // Optionally, send a notification to the user
        // Mail::to($this->order->user)->send(new OrderStatusUpdated($this->order));

        session()->flash('success', 'Order status updated successfully.');
    }

    public function render()
    {
        return view('livewire.admin.orders.show');
    }
}
