<?php

namespace App\Livewire\Auth;

use App\Models\User;
use App\Models\Role;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')] // CRITICAL FIX NA1/AL1: Use traditional layout
class Register extends Component
{
    public string $name = '';

    public string $email = '';

    public string $password = '';

    public string $password_confirmation = '';

        public $registering = false;

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $this->registering = true;
        
        try {
            $validated = $this->validate([
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
                'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            ]);

            $validated['password'] = Hash::make($validated['password']);
            
            // FIXED: Assign customer role by default with proper error handling
            $customerRole = Role::where('name', 'customer')->first();
            if (!$customerRole) {
                throw new \Exception('Customer role not found. Please contact support.');
            }
            $validated['role_id'] = $customerRole->id;

            // Ensure role_id is explicitly included in the create method
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => $validated['password'],
                'role_id' => $validated['role_id']
            ]);

            event(new Registered($user));

            Auth::login($user);
            
            session()->flash('success', 'Registration successful! Welcome to Brandify.');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
            $this->registering = false;
            return;
        }

        $this->registering = false;

        // CRITICAL FIX AL4: Use consistent redirect logic matching login behavior with debugging
        $user = auth()->user();
        $redirectRoute = 'dashboard';

        // DEBUGGING: Log registration redirect details
        \Log::info('Registration redirect debug', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'is_vendor' => $user->isVendor(),
            'is_admin' => $user->isAdmin(),
            'role_id' => $user->role_id,
            'role_name' => $user->role?->name,
        ]);

        if ($user->isVendor()) {
            $redirectRoute = 'vendor.dashboard';
        } elseif ($user->isAdmin()) {
            $redirectRoute = 'admin.dashboard';
        }

        // DEBUGGING: Log final redirect decision
        \Log::info('Registration redirect decision', [
            'user_id' => $user->id,
            'redirect_route' => $redirectRoute,
            'route_url' => route($redirectRoute),
        ]);

        // CRITICAL FIX: Use correct Livewire redirect method based on official docs
        try {
            // Primary approach: Use redirectRoute for named routes (recommended)
            $this->redirectRoute($redirectRoute);
        } catch (\Exception $e) {
            \Log::error('Registration Livewire redirect failed, trying alternative', [
                'error' => $e->getMessage(),
                'redirect_route' => $redirectRoute,
                'user_id' => $user->id,
            ]);

            // Fallback approach: Use basic redirect with URL
            $this->redirect(route($redirectRoute));
        }
    }
}
