<?php

namespace App\Policies;

use App\Models\User;
use App\Models\VendorSubscription;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriptionPolicy
{
    use HandlesAuthorization;

    /**
     * Perform pre-authorization checks.
     */
    public function before(User $user, $ability)
    {
        if ($user->hasRole('admin')) {
            return true;
        }
    }

    /**
     * Determine whether the user can view any subscriptions.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('vendor') && $user->vendor?->is_approved;
    }

    /**
     * Determine whether the user can create a subscription.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('vendor') && $user->vendor?->is_approved;
    }

    /**
     * Determine whether the user can cancel a subscription.
     */
    public function cancel(User $user, VendorSubscription $subscription): bool
    {
        return $user->id === $subscription->vendor->user_id && $subscription->isCancellable();
    }

    /**
     * Determine whether the user can reactivate a subscription.
     */
    public function reactivate(User $user, VendorSubscription $subscription): bool
    {
        return $user->id === $subscription->vendor->user_id && $subscription->status === 'cancelled';
    }
}
