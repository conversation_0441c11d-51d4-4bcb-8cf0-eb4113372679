<?php

namespace App\Console\Commands;

use App\Models\Brand;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Console\Command;

class CheckVendorBrandData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:vendor-brand-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check vendor-brand data relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VENDOR-BRAND DATA CHECK ===');
        
        // Check vendors
        $this->info("\n--- VENDORS ---");
        $vendors = Vendor::with('brand')->get();
        foreach ($vendors as $vendor) {
            $brandInfo = $vendor->brand ? "Brand: {$vendor->brand->name} (ID: {$vendor->brand->id})" : "No brand";
            $this->info("Vendor: {$vendor->shop_name} (ID: {$vendor->id}) - {$brandInfo}");
        }
        
        // Check brands
        $this->info("\n--- BRANDS ---");
        $brands = Brand::with('vendor')->get();
        foreach ($brands as $brand) {
            $vendorInfo = $brand->vendor ? "Vendor: {$brand->vendor->shop_name} (ID: {$brand->vendor->id})" : "No vendor";
            $vendorIdDisplay = $brand->vendor_id ?? 'NULL';
            $this->info("Brand: {$brand->name} (ID: {$brand->id}) - Vendor ID: {$vendorIdDisplay} - {$vendorInfo}");
        }
        
        // Check products
        $this->info("\n--- PRODUCTS (first 10) ---");
        $products = Product::with(['vendor', 'brand'])->limit(10)->get();
        foreach ($products as $product) {
            $vendorInfo = $product->vendor ? "Vendor: {$product->vendor->shop_name}" : "No vendor";
            $brandInfo = $product->brand ? "Brand: {$product->brand->name}" : "No brand";
            $this->info("Product: {$product->name} - {$vendorInfo} - {$brandInfo}");
        }
        
        return 0;
    }
}
