<?php

namespace App\Livewire\Vendor\Onboarding;

use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    use WithFileUploads;

    public Vendor $vendor;
    public string $step = 'business';

    // Step 1: Business Information & Logo
    public $business_name;
    public $phone;
    public $business_description;
    public $logo;

    // Step 2: Shipping Address Information
    public $shipping_address;
    public $shipping_city;
    public $shipping_state;
    public $shipping_postal_code;
    public $shipping_country = 'Nigeria';

    public function mount()
    {
        \Log::info('Vendor Onboarding Mount Started', [
            'user_id' => Auth::id(),
            'user_email' => Auth::user()->email,
            'timestamp' => now()->toISOString()
        ]);

        $this->vendor = Auth::user()->vendor;

        if ($this->vendor->has_completed_onboarding) {
            \Log::info('Vendor Onboarding - Already Completed, Redirecting', [
                'vendor_id' => $this->vendor->id
            ]);
            return redirect()->route('vendor.dashboard');
        }

        // Pre-fill properties from vendor model
        $this->business_name = $this->vendor->business_name;
        $this->phone = $this->vendor->phone;
        $this->business_description = $this->vendor->business_description;

        // Pre-fill shipping address from vendor's existing address fields
        $this->shipping_address = $this->vendor->business_address;
        $this->shipping_city = $this->vendor->city;
        $this->shipping_state = $this->vendor->state;
        $this->shipping_postal_code = $this->vendor->postal_code ?? '';
        $this->shipping_country = $this->vendor->country ?? 'Nigeria';

        // Determine the current step - simplified 2-step process
        if ($this->vendor->phone && $this->vendor->business_description) {
            $this->step = 'shipping';
        }
    }

    public function saveBusinessInfo()
    {
        $this->validate([
            'business_name' => 'required|string|max:255|unique:vendors,business_name,' . $this->vendor->id,
            'phone' => 'required|string|max:20',
            'business_description' => 'required|string|max:1000',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
        ]);

        $this->vendor->update([
            'business_name' => $this->business_name,
            'slug' => Str::slug($this->business_name),
            'phone' => $this->phone,
            'business_description' => $this->business_description,
        ]);

        // Handle logo upload using Spatie Media Library (consistent with profile page)
        if ($this->logo) {
            try {
                // Clear existing logo from media library
                $this->vendor->clearMediaCollection('logo');

                // Add new logo using consistent method from profile page
                $this->vendor->addMedia($this->logo->getRealPath())
                    ->usingName($this->vendor->business_name . ' Logo')
                    ->usingFileName($this->logo->getClientOriginalName())
                    ->toMediaCollection('logo');

                // Reset the logo property to clear the temporary file
                $this->logo = null;
            } catch (\Exception $e) {
                session()->flash('error', 'Failed to upload logo: ' . $e->getMessage());
                return;
            }
        }

        $this->step = 'shipping';
        session()->flash('success', 'Business information and logo saved!');
    }

    public function saveShippingInfo()
    {
        $this->validate([
            'shipping_address' => 'required|string|max:500',
            'shipping_city' => 'required|string|max:255',
            'shipping_state' => 'required|string|max:255',
            'shipping_postal_code' => 'nullable|string|max:20',
            'shipping_country' => 'required|string|max:255',
        ]);

        $this->vendor->update([
            'business_address' => $this->shipping_address, // Store as business_address for compatibility
            'city' => $this->shipping_city,
            'state' => $this->shipping_state,
            'postal_code' => $this->shipping_postal_code,
            'country' => $this->shipping_country,
            'has_completed_onboarding' => true,
        ]);

        return redirect()->route('vendor.dashboard')->with('success', 'Your vendor profile has been completed! Your application is now pending approval.');
    }

    public function render()
    {
        return view('livewire.vendor.onboarding.index');
    }
}
