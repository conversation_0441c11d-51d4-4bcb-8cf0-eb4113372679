<div class="container mx-auto py-8">
    <div class="bg-white p-6 rounded-lg shadow">
        <h1 class="text-2xl font-bold mb-4">Debug Checkout Component</h1>
        
        <!-- Cart Items Debug -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Cart Items Debug</h2>
            <p><strong>Cart Items Type:</strong> {{ gettype($cartItems) }}</p>
            <p><strong>Cart Items Count:</strong> {{ is_array($cartItems) ? count($cartItems) : 'N/A' }}</p>
            
            @if(is_array($cartItems) && count($cartItems) > 0)
                <div class="mt-2">
                    <h3 class="font-medium">Cart Items:</h3>
                    @foreach($cartItems as $key => $item)
                        <div class="ml-4 mt-1">
                            <p><strong>Item {{ $key }}:</strong> {{ gettype($item) }}</p>
                            @if(is_array($item))
                                <div class="ml-4">
                                    @foreach($item as $itemKey => $itemValue)
                                        <p>{{ $itemKey }}: {{ is_string($itemValue) ? $itemValue : gettype($itemValue) }}</p>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500">No cart items or cart is not an array</p>
            @endif
        </div>
        
        <!-- Debug Info -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Debug Information</h2>
            @if(count($debugInfo) > 0)
                <ul class="list-disc list-inside">
                    @foreach($debugInfo as $info)
                        <li class="text-sm">{{ $info }}</li>
                    @endforeach
                </ul>
            @else
                <p class="text-gray-500">No debug information available</p>
            @endif
        </div>
        
        <!-- Test Button -->
        <div class="mb-6">
            <button wire:click="testCheckoutProcess" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Test Checkout Process
            </button>
        </div>
        
        <!-- Loading State -->
        <div wire:loading class="text-blue-500">
            Processing...
        </div>
    </div>
</div>
