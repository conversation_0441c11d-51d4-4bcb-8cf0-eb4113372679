<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * Structured Logging Service
 * 
 * Provides consistent, structured logging across the application
 * with contextual information and performance metrics.
 */
class StructuredLoggingService
{
    /**
     * Log business event with structured context
     *
     * @param string $event Event name
     * @param array $context Event context data
     * @param string $level Log level (info, warning, error, etc.)
     * @param array $metrics Performance metrics
     */
    public function logBusinessEvent(
        string $event,
        array $context = [],
        string $level = 'info',
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext($event, $context, $metrics);
        
        Log::log($level, $event, $logData);
    }

    /**
     * Log financial transaction
     *
     * @param string $transactionType Type of transaction
     * @param float $amount Transaction amount
     * @param array $context Additional context
     * @param array $metrics Performance metrics
     */
    public function logFinancialTransaction(
        string $transactionType,
        float $amount,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("financial_transaction.{$transactionType}", array_merge($context, [
            'transaction_type' => $transactionType,
            'amount' => $amount,
            'currency' => 'NGN',
        ]), $metrics);

        Log::info("Financial transaction: {$transactionType}", $logData);
    }

    /**
     * Log API call with performance metrics
     *
     * @param string $service Service name (e.g., 'paystack', 'shipbubble')
     * @param string $endpoint API endpoint
     * @param string $method HTTP method
     * @param int $responseCode HTTP response code
     * @param float $duration Request duration in milliseconds
     * @param array $context Additional context
     */
    public function logApiCall(
        string $service,
        string $endpoint,
        string $method,
        int $responseCode,
        float $duration,
        array $context = []
    ): void {
        $level = $responseCode >= 400 ? 'error' : ($responseCode >= 300 ? 'warning' : 'info');
        
        $logData = $this->buildLogContext("api_call.{$service}", array_merge($context, [
            'service' => $service,
            'endpoint' => $endpoint,
            'method' => $method,
            'response_code' => $responseCode,
            'success' => $responseCode < 400,
        ]), [
            'duration_ms' => $duration,
        ]);

        Log::log($level, "API call to {$service}: {$method} {$endpoint}", $logData);
    }

    /**
     * Log security event
     *
     * @param string $eventType Security event type
     * @param array $context Event context
     * @param string $severity Severity level (low, medium, high, critical)
     */
    public function logSecurityEvent(
        string $eventType,
        array $context = [],
        string $severity = 'medium'
    ): void {
        $level = match($severity) {
            'critical' => 'critical',
            'high' => 'error',
            'medium' => 'warning',
            'low' => 'info',
            default => 'warning',
        };

        $logData = $this->buildLogContext("security.{$eventType}", array_merge($context, [
            'event_type' => $eventType,
            'severity' => $severity,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]));

        Log::log($level, "Security event: {$eventType}", $logData);
    }

    /**
     * Log user action
     *
     * @param string $action Action performed
     * @param array $context Action context
     * @param array $metrics Performance metrics
     */
    public function logUserAction(
        string $action,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("user_action.{$action}", array_merge($context, [
            'action' => $action,
        ]), $metrics);

        Log::info("User action: {$action}", $logData);
    }

    /**
     * Log vendor action
     *
     * @param string $action Action performed
     * @param int $vendorId Vendor ID
     * @param array $context Action context
     * @param array $metrics Performance metrics
     */
    public function logVendorAction(
        string $action,
        int $vendorId,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("vendor_action.{$action}", array_merge($context, [
            'action' => $action,
            'vendor_id' => $vendorId,
        ]), $metrics);

        Log::info("Vendor action: {$action}", $logData);
    }

    /**
     * Log admin action
     *
     * @param string $action Action performed
     * @param array $context Action context
     * @param array $metrics Performance metrics
     */
    public function logAdminAction(
        string $action,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("admin_action.{$action}", array_merge($context, [
            'action' => $action,
            'is_admin_action' => true,
        ]), $metrics);

        Log::info("Admin action: {$action}", $logData);
    }

    /**
     * Log order lifecycle event
     *
     * @param string $event Order event (created, paid, shipped, delivered, etc.)
     * @param int $orderId Order ID
     * @param array $context Event context
     * @param array $metrics Performance metrics
     */
    public function logOrderEvent(
        string $event,
        int $orderId,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("order.{$event}", array_merge($context, [
            'order_id' => $orderId,
            'event' => $event,
        ]), $metrics);

        Log::info("Order event: {$event}", $logData);
    }

    /**
     * Log product lifecycle event
     *
     * @param string $event Product event (created, updated, approved, etc.)
     * @param int $productId Product ID
     * @param array $context Event context
     * @param array $metrics Performance metrics
     */
    public function logProductEvent(
        string $event,
        int $productId,
        array $context = [],
        array $metrics = []
    ): void {
        $logData = $this->buildLogContext("product.{$event}", array_merge($context, [
            'product_id' => $productId,
            'event' => $event,
        ]), $metrics);

        Log::info("Product event: {$event}", $logData);
    }

    /**
     * Log performance metric
     *
     * @param string $metric Metric name
     * @param float $value Metric value
     * @param string $unit Metric unit (ms, seconds, bytes, etc.)
     * @param array $context Additional context
     */
    public function logPerformanceMetric(
        string $metric,
        float $value,
        string $unit = 'ms',
        array $context = []
    ): void {
        $logData = $this->buildLogContext("performance.{$metric}", array_merge($context, [
            'metric' => $metric,
            'value' => $value,
            'unit' => $unit,
        ]));

        Log::info("Performance metric: {$metric}", $logData);
    }

    /**
     * Log error with context
     *
     * @param \Throwable $exception Exception object
     * @param array $context Additional context
     * @param string $category Error category
     */
    public function logError(
        \Throwable $exception,
        array $context = [],
        string $category = 'general'
    ): void {
        $logData = $this->buildLogContext("error.{$category}", array_merge($context, [
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'category' => $category,
        ]));

        Log::error("Error in {$category}: {$exception->getMessage()}", $logData);
    }

    /**
     * Build structured log context
     *
     * @param string $event Event name
     * @param array $context Event context
     * @param array $metrics Performance metrics
     * @return array Structured log data
     */
    protected function buildLogContext(
        string $event,
        array $context = [],
        array $metrics = []
    ): array {
        $user = Auth::user();
        $request = request();

        $baseContext = [
            'event' => $event,
            'timestamp' => Carbon::now()->toISOString(),
            'environment' => app()->environment(),
            'application' => 'brandify-ecommerce',
        ];

        // User context
        if ($user) {
            $baseContext['user'] = [
                'id' => $user->id,
                'email' => $user->email,
                'type' => $user->user_type ?? 'customer',
            ];

            if ($user->vendor) {
                $baseContext['vendor'] = [
                    'id' => $user->vendor->id,
                    'business_name' => $user->vendor->business_name,
                ];
            }
        }

        // Request context
        if ($request) {
            $baseContext['request'] = [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'session_id' => $request->session()->getId(),
            ];

            // Add route information if available
            if ($request->route()) {
                $baseContext['request']['route'] = $request->route()->getName();
                $baseContext['request']['controller'] = $request->route()->getActionName();
            }
        }

        // Performance metrics
        if (!empty($metrics)) {
            $baseContext['metrics'] = $metrics;
        }

        // Memory usage
        $baseContext['memory'] = [
            'usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
        ];

        return array_merge($baseContext, $context);
    }

    /**
     * Create a performance timer
     *
     * @param string $operation Operation name
     * @return array Timer data
     */
    public function startTimer(string $operation): array
    {
        return [
            'operation' => $operation,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
        ];
    }

    /**
     * End a performance timer and log the results
     *
     * @param array $timer Timer data from startTimer()
     * @param array $context Additional context
     */
    public function endTimer(array $timer, array $context = []): void
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $duration = ($endTime - $timer['start_time']) * 1000; // Convert to milliseconds
        $memoryDelta = $endMemory - $timer['start_memory'];

        $this->logPerformanceMetric(
            $timer['operation'],
            $duration,
            'ms',
            array_merge($context, [
                'memory_delta_bytes' => $memoryDelta,
                'memory_delta_mb' => round($memoryDelta / 1024 / 1024, 2),
            ])
        );
    }
}
