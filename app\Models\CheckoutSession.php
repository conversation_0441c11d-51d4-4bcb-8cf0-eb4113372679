<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CheckoutSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'user_id',
        'cart_items',
        'shipping_address',
        'selected_shipping_rate',
        'subtotal',
        'shipping_cost',
        'total',
        'vendor_splits',
        'paystack_reference',
        'status',
        'expires_at',
    ];

    protected $casts = [
        'cart_items' => 'array',
        'shipping_address' => 'array',
        'selected_shipping_rate' => 'array',
        'vendor_splits' => 'array',
        'subtotal' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'total' => 'decimal:2',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the checkout session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the checkout session has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Mark the session as expired.
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Mark the session as processing.
     */
    public function markAsProcessing(): void
    {
        $this->update(['status' => 'processing']);
    }

    /**
     * Mark the session as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Mark the session as failed.
     */
    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);
    }

    /**
     * Scope to get active (non-expired) sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now())
                    ->whereIn('status', ['pending', 'processing']);
    }

    /**
     * Scope to get expired sessions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now())
                    ->orWhere('status', 'expired');
    }

    /**
     * Create a new checkout session with default expiration.
     */
    public static function createSession(array $data): self
    {
        // Ensure required numeric fields are properly set
        $data['subtotal'] = isset($data['subtotal']) ? (float) $data['subtotal'] : 0.0;
        $data['shipping_cost'] = isset($data['shipping_cost']) ? (float) $data['shipping_cost'] : 0.0;
        $data['total'] = isset($data['total']) ? (float) $data['total'] : 0.0;

        $data['expires_at'] = now()->addHours(2); // 2 hour expiration
        $data['status'] = 'pending';

        \Log::info('CheckoutSession::createSession called with data', [
            'shipping_cost' => $data['shipping_cost'],
            'subtotal' => $data['subtotal'],
            'total' => $data['total'],
            'transaction_id' => $data['transaction_id'] ?? 'unknown'
        ]);

        return self::create($data);
    }
}
