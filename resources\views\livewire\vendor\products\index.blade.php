<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Mobile-First Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-4 sm:p-6 lg:p-8 rounded-xl lg:rounded-2xl shadow-2xl mb-6 sm:mb-8">
        <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div class="space-y-1 sm:space-y-2">
                <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Product Management
                </h1>
                <p class="text-gray-300 text-sm sm:text-base lg:text-lg">Manage your store inventory and listings</p>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 lg:space-x-4">
                <a href="{{ route('vendor.products.create') }}"
                   class="group bg-white text-black px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2 text-sm sm:text-base">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Product</span>
                </a>
                <button class="group border-2 border-white text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center justify-center space-x-2 text-sm sm:text-base">
                    <i class="fa-solid fa-download transition-transform duration-300 group-hover:bounce"></i>
                    <span>Export</span>
                </button>
            </div>
        </div>
    </div>

    {{-- Mobile-Optimized Search and Filters --}}
    <div class="bg-white rounded-xl lg:rounded-2xl shadow-lg border border-gray-100 p-4 sm:p-6 mb-6 sm:mb-8">
        <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0 lg:space-x-6">
            {{-- Search --}}
            <div class="flex-1 lg:max-w-md">
                <div class="relative">
                    <input type="text"
                           wire:model.live.debounce.300ms="search"
                           placeholder="Search products..."
                           class="w-full pl-10 sm:pl-12 pr-4 py-2.5 sm:py-3 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-sm sm:text-base">
                    <i class="fas fa-search absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            {{-- Filters --}}
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 lg:space-x-4">
                <select wire:model.live="category" class="px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-sm sm:text-base">
                    <option value="all">All Categories</option>
                    @foreach($categories as $cat)
                        <option value="{{ $cat->id }}">{{ $cat->name }}</option>
                    @endforeach
                </select>
                <select wire:model.live="status" class="px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-sm sm:text-base">
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>

        {{-- Bulk Actions --}}
        @if(count($selectedProducts) > 0)
            <div class="mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div class="flex items-center justify-between">
                    <span class="text-blue-700 font-medium">{{ count($selectedProducts) }} products selected</span>
                    <div class="flex space-x-2">
                        <button wire:click="bulkToggleStatus('activate')"
                                class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-check mr-1"></i> Activate
                        </button>
                        <button wire:click="bulkToggleStatus('deactivate')"
                                class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            <i class="fas fa-pause mr-1"></i> Deactivate
                        </button>
                        <button wire:click="bulkDelete"
                                wire:confirm="Are you sure you want to delete the selected products?"
                                class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-1"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        @endif
    </div>

    {{-- Success Message --}}
    @if (session()->has('success'))
        <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-8 rounded-r-xl shadow-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <p class="text-green-800 font-medium">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    {{-- Modern Products Grid --}}
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        {{-- Table Header --}}
        @if($products->count() > 0)
            <div class="p-6 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <input type="checkbox"
                               wire:model.live="selectAll"
                               class="rounded border-gray-300 text-black focus:ring-black">
                        <span class="text-sm font-medium text-gray-700">Select All</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        {{ $products->total() }} total products
                    </div>
                </div>
            </div>
        @endif

        @forelse ($products as $product)
            {{-- MOBILE RESPONSIVENESS FIX: Improved mobile layout --}}
            <div class="group p-4 sm:p-6 border-b border-gray-100 hover:bg-gray-50 transition-all duration-300 last:border-b-0">
                {{-- Mobile Layout (Stacked) --}}
                <div class="block lg:hidden space-y-4">
                    {{-- Mobile Header --}}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox"
                                   wire:model.live="selectedProducts"
                                   value="{{ $product->id }}"
                                   class="rounded border-gray-300 text-black focus:ring-black">
                            <img src="{{ $product->image_url }}"
                                 alt="{{ $product->name }}"
                                 class="w-12 h-12 rounded-lg object-cover shadow-md">
                            <div>
                                <h3 class="font-semibold text-gray-900 text-sm truncate">{{ $product->name }}</h3>
                                <p class="text-xs text-gray-500">{{ $product->category->name ?? 'Uncategorized' }}</p>
                            </div>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $product->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>

                    {{-- Mobile Details --}}
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Price:</span>
                            <span class="font-semibold text-gray-900 ml-1">@currency($product->price)</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Stock:</span>
                            <span class="font-semibold text-gray-900 ml-1">{{ $product->getStockLevel() }}</span>
                        </div>
                    </div>

                    {{-- Mobile Actions --}}
                    <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                        <span class="text-xs text-gray-500">{{ $product->created_at->format('M d, Y') }}</span>
                        <div class="flex items-center space-x-2">
                            <div class="mt-4 grid grid-cols-2 gap-2 text-center">
                                <a href="{{ route('vendor.products.edit', $product->slug) }}" class="block w-full px-3 py-2 text-xs font-semibold text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors duration-200">Edit</a>
                                <button wire:click="deleteProduct({{ $product->id }})" wire:confirm="Are you sure you want to delete '{{ $product->name }}'?" class="block w-full px-3 py-2 text-xs font-semibold text-red-600 bg-red-100 rounded-lg hover:bg-red-200 transition-colors duration-200">Delete</button>
                                <a href="{{ route('vendor.products.variants', $product->slug) }}" class="block w-full px-3 py-2 text-xs font-semibold text-purple-600 bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors duration-200">Variants</a>
                                <button wire:click="toggleProductStatus({{ $product->id }})" class="block w-full px-3 py-2 text-xs font-semibold {{ $product->is_active ? 'text-yellow-600 bg-yellow-100 hover:bg-yellow-200' : 'text-green-600 bg-green-100 hover:bg-green-200' }} rounded-lg transition-colors duration-200">{{ $product->is_active ? 'Deactivate' : 'Activate' }}</button>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Desktop Layout (Original) --}}
                <div class="hidden lg:flex items-center justify-between">
                    {{-- Checkbox and Product Info --}}
                    <div class="flex items-center space-x-6 flex-1">
                        {{-- Checkbox --}}
                        <input type="checkbox"
                               wire:model.live="selectedProducts"
                               value="{{ $product->id }}"
                               class="rounded border-gray-300 text-black focus:ring-black">
                        {{-- Product Image --}}
                        <div class="relative">
                            <img src="{{ $product->image_url }}"
                                 alt="{{ $product->name }}"
                                 class="w-16 h-16 rounded-xl object-cover shadow-lg group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute -top-2 -right-2 w-6 h-6 {{ $product->is_active ? 'bg-green-500' : 'bg-red-500' }} rounded-full flex items-center justify-center">
                                <i class="fas {{ $product->is_active ? 'fa-check' : 'fa-times' }} text-white text-xs"></i>
                            </div>
                        </div>

                        {{-- Product Details --}}
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between">
                                <div class="space-y-1">
                                    <h3 class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300 truncate">
                                        {{ $product->name }}
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ $product->category->name ?? 'Uncategorized' }}
                                    </p>
                                    <div class="flex items-center space-x-4">
                                        <span class="text-xl font-bold text-gray-900">
                                            @currency($product->price)
                                        </span>
                                        @if($product->discount_price)
                                            <span class="text-sm text-gray-500 line-through">
                                                @currency($product->discount_price)
                                            </span>
                                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded-full">
                                                Sale
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Actions --}}
                    <div class="flex items-center space-x-3">
                        {{-- Stock Info --}}
                        <div class="text-center">
                            <div class="flex items-center space-x-2 mb-1">
                                @php
                                    $stockLevel = $product->getStockLevel();
                                @endphp
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $stockLevel > 10 ? 'bg-green-100 text-green-800' : ($stockLevel > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    <i class="fas fa-boxes mr-1"></i>
                                    {{ $stockLevel }} in stock
                                </span>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <p class="text-xs text-gray-500 mt-1">
                                Created {{ $product->created_at->format('M d, Y') }}
                            </p>
                        </div>

                        {{-- Action Buttons --}}
                        <div class="flex items-center space-x-2">
                            <button wire:click="toggleProductStatus({{ $product->id }})" class="group p-3 {{ $product->is_active ? 'bg-yellow-50 text-yellow-600 hover:bg-yellow-100' : 'bg-green-50 text-green-600 hover:bg-green-100' }} rounded-xl hover:scale-105 transition-all duration-300" title="{{ $product->is_active ? 'Deactivate Product' : 'Activate Product' }}">
                                <i class="fas {{ $product->is_active ? 'fa-pause' : 'fa-play' }} group-hover:scale-110 transition-transform duration-300"></i>
                            </button>
                            <a href="{{ route('vendor.products.variants', $product->slug) }}" class="group p-3 bg-purple-50 text-purple-600 rounded-xl hover:bg-purple-100 hover:scale-105 transition-all duration-300" title="Manage Variants">
                                <i class="fas fa-palette group-hover:scale-110 transition-transform duration-300"></i>
                            </a>
                            <a href="{{ route('vendor.products.edit', $product->slug) }}" class="group p-3 bg-blue-50 text-blue-600 rounded-xl hover:bg-blue-100 hover:scale-105 transition-all duration-300" title="Edit Product">
                                <i class="fas fa-edit group-hover:scale-110 transition-transform duration-300"></i>
                            </a>
                            <button wire:click="deleteProduct({{ $product->id }})" wire:confirm="Are you sure you want to delete '{{ $product->name }}'? This action cannot be undone." class="group p-3 bg-red-50 text-red-600 rounded-xl hover:bg-red-100 hover:scale-105 transition-all duration-300" title="Delete Product">
                                <i class="fas fa-trash group-hover:scale-110 transition-transform duration-300"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {{-- End Desktop Layout --}}
            </div>
        @empty
            {{-- Empty State --}}
            <div class="text-center py-20">
                <div class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-box-open text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Products Yet</h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    Start building your store by adding your first product. You can add images, descriptions, pricing, and more.
                </p>
                <a href="{{ route('vendor.products.create') }}"
                   class="group inline-flex items-center px-6 py-3 bg-black text-white rounded-xl font-semibold hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2 transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add Your First Product</span>
                </a>
            </div>
        @endforelse

        {{-- Pagination --}}
        @if ($products->hasPages())
            <div class="p-6 border-t border-gray-100 bg-gray-50">
                {{ $products->links() }}
            </div>
        @endif
    </div>
</div>
