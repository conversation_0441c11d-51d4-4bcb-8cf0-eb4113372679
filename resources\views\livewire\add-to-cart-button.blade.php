<div class="w-full">
    @if($wasAdded)
        <div class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold py-3 sm:py-4 px-6 rounded-xl flex items-center justify-center text-sm sm:text-base shadow-lg transform scale-105 animate-pulse min-h-[44px]">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-bold">Added to Cart!</span>
        </div>
    @else
        <x-button.primary
            wire:click.prevent="addToCart"
            wire:target="addToCart"
            :loading="true"
            class="w-full"
            size="lg">
            <span class="flex items-center space-x-3">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <span class="font-bold">Add to Cart</span>
            </span>
        </x-button.primary>
    @endif
</div>
