<?php

namespace App\Livewire\Admin\Orders;

use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public string $search = '';
    public string $status = '';

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        // SECURITY FIX: Sanitize search input
        $searchTerm = $this->sanitizeSearchInput($this->search);
        $statusFilter = $this->sanitizeStatusInput($this->status);

        // PERFORMANCE FIX: Enhanced eager loading to prevent N+1 queries
        $orders = Order::with([
            'user:id,name,email',
            'items' => function($query) {
                $query->with('product:id,name,price,vendor_id');
            },
            'items.product.vendor:id,shop_name', // CRITICAL FIX: Use shop_name instead of name
        ])
            ->when($searchTerm, function ($query) use ($searchTerm) {
                // SECURITY FIX: Use parameterized queries to prevent SQL injection
                $query->where('order_number', 'like', '%' . $searchTerm . '%')
                    ->orWhereHas('user', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('email', 'like', '%' . $searchTerm . '%');
                    });
            })
            ->when($statusFilter, function ($query) use ($statusFilter) {
                $query->where('status', $statusFilter);
            })
            ->latest()
            ->paginate(15);

        return view('livewire.admin.orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * SECURITY FIX: Sanitize search input to prevent SQL injection
     */
    private function sanitizeSearchInput(string $input): string
    {
        // Remove potentially dangerous characters
        $input = trim($input);

        // Remove SQL injection patterns
        $input = preg_replace('/[\'";\\\\]/', '', $input);

        // Remove excessive whitespace
        $input = preg_replace('/\s+/', ' ', $input);

        // Limit length
        $input = substr($input, 0, 50);

        // Remove HTML tags
        $input = strip_tags($input);

        return $input;
    }

    /**
     * SECURITY FIX: Validate status input
     */
    private function sanitizeStatusInput(string $status): string
    {
        $allowedStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];

        if (in_array($status, $allowedStatuses)) {
            return $status;
        }

        return '';
    }
}
