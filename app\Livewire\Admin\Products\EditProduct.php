<?php

namespace App\Livewire\Admin\Products;

use App\Livewire\Shared\ProductForm as BaseProductForm;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Livewire\Attributes\Layout;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('layouts.admin')]
class EditProduct extends BaseProductForm
{
    // CRITICAL FIX: Individual properties for proper Livewire binding (legacy model binding disabled)
    public $name = '';
    public $description = '';
    public $price;
    public $discount_price;
    public $stock;
    public $category_id;
    public $vendor_id;
    public $is_active = true;
    public $is_featured = false;
    public $is_best_seller = false;
    public $weight;
    public $length;
    public $width;
    public $height;

    // Additional properties specific to admin edit
    public array $vendors = [];
    public array $categories = [];
    public array $brands = [];

    public function mount(Product $product)
    {
        // CRITICAL FIX: Product Edit Form Data Pre-population Failure
        if (!$product->exists) {
            return redirect()->route('admin.products.index')->with('error', 'Product not found for editing.');
        }

        // Load the product with all necessary relationships
        $this->product = $product->load(['variants', 'specifications', 'category', 'brand', 'vendor']);

        // Ensure the product model is fresh and properly hydrated for Livewire
        $this->product->refresh();

        // CRITICAL FIX: Make all product attributes accessible to Livewire wire:model bindings
        $this->product->makeVisible([
            'name', 'description', 'price', 'discount_price', 'stock',
            'sku', 'category_id', 'brand_id', 'vendor_id', 'is_active',
            'is_featured', 'is_best_seller', 'weight', 'height', 'width', 'length'
        ]);

        // CRITICAL FIX: Populate individual properties from product model
        $this->populatePropertiesFromProduct();

        // CRITICAL FIX: Ensure all product properties are accessible to Livewire
        $this->ensureProductPropertiesAccessible();

        // Load variants
        foreach ($this->product->variants as $variant) {
            $this->variants[] = [
                'id' => $variant->id,
                'name' => $variant->name,
                'value' => $variant->value,
                'price' => $variant->price,
                'existing_image_url' => $variant->image_url, // Use the accessor
                'image' => null, // For new uploads
            ];
        }

        // Load specifications
        foreach ($this->product->specifications as $specification) {
            $this->specifications[] = [
                'id' => $specification->id,
                'name' => $specification->key, // Note: Using 'name' instead of 'key' to match trait
                'value' => $specification->value,
            ];
        }

        // Load dropdown data
        $this->vendors = Vendor::orderBy('shop_name')->get(['id', 'shop_name'])->toArray();
        $this->categories = Category::orderBy('name')->get(['id', 'name'])->toArray();
        $this->brands = Brand::orderBy('name')->get(['id', 'name'])->toArray();

        // Ensure we have at least one variant and specification
        if (empty($this->variants)) {
            $this->addVariant();
        }
        if (empty($this->specifications)) {
            $this->addSpecification();
        }

        // Call the base initialization method
        $this->initializeProductForm();
    }

    /**
     * CRITICAL FIX: Ensure all product properties are accessible to Livewire
     */
    private function ensureProductPropertiesAccessible()
    {
        // Force all product attributes to be accessible for wire:model bindings
        if ($this->product) {
            // Make sure all attributes are in the attributes array and accessible
            $attributes = [
                'name' => $this->product->name,
                'description' => $this->product->description,
                'price' => $this->product->price,
                'discount_price' => $this->product->discount_price,
                'stock' => $this->product->stock,
                'sku' => $this->product->sku,
                'category_id' => $this->product->category_id,
                'brand_id' => $this->product->brand_id,
                'vendor_id' => $this->product->vendor_id,
                'is_active' => $this->product->is_active,
                'is_featured' => $this->product->is_featured,
                'is_best_seller' => $this->product->is_best_seller,
                'weight' => $this->product->weight,
                'height' => $this->product->height,
                'width' => $this->product->width,
                'length' => $this->product->length,
            ];

            // Set attributes explicitly to ensure Livewire can access them
            foreach ($attributes as $key => $value) {
                $this->product->setAttribute($key, $value);
            }
        }
    }

    /**
     * CRITICAL FIX: Populate individual properties from product model
     */
    private function populatePropertiesFromProduct()
    {
        $this->name = $this->product->name ?? '';
        $this->description = $this->product->description ?? '';
        $this->price = $this->product->price;
        $this->discount_price = $this->product->discount_price;
        $this->stock = $this->product->stock;
        $this->category_id = $this->product->category_id;
        $this->vendor_id = $this->product->vendor_id;
        $this->is_active = $this->product->is_active ?? true;
        $this->is_featured = $this->product->is_featured ?? false;
        $this->is_best_seller = $this->product->is_best_seller ?? false;
        $this->weight = $this->product->weight;
        $this->length = $this->product->length;
        $this->width = $this->product->width;
        $this->height = $this->product->height;
    }

    /**
     * CRITICAL FIX: Update product model from individual properties
     */
    private function updateProductFromProperties()
    {
        $this->product->name = $this->name;
        $this->product->description = $this->description;
        $this->product->price = $this->price;
        $this->product->discount_price = $this->discount_price;
        $this->product->stock = $this->stock;
        $this->product->category_id = $this->category_id;
        $this->product->vendor_id = $this->vendor_id;
        $this->product->is_active = $this->is_active;
        $this->product->is_featured = $this->is_featured;
        $this->product->is_best_seller = $this->is_best_seller;
        $this->product->weight = $this->weight;
        $this->product->length = $this->length;
        $this->product->width = $this->width;
        $this->product->height = $this->height;
    }

    /**
     * CRITICAL FIX: Ensure product is properly hydrated before rendering
     */
    public function hydrate()
    {
        if ($this->product && $this->product->exists) {
            $this->ensureProductPropertiesAccessible();
        }
    }

    /**
     * Get the validation rules for the product form
     *
     * @return array
     */
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|min:10',
            'price' => 'required|numeric|min:0|max:999999.99',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock' => 'required|integer|min:0|max:999999',
            'category_id' => 'required|exists:categories,id',
            'vendor_id' => 'required|exists:vendors,id',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_best_seller' => 'boolean',
            'weight' => 'nullable|numeric|min:0|max:999.99',
            'height' => 'nullable|numeric|min:0|max:999.99',
            'width' => 'nullable|numeric|min:0|max:999.99',
            'length' => 'nullable|numeric|min:0|max:999.99',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ];
    }

    // Variant and specification methods are now in the ManagesVariants and ManagesSpecifications traits

    /**
     * Save the product and related data
     */
    public function save()
    {
        $this->validate();

        // CRITICAL FIX: Update product model from individual properties
        $this->updateProductFromProperties();

        // Save the product and handle media
        $this->saveProduct();

        // Save variants and specifications
        $this->saveVariants($this->product);
        $this->saveSpecifications($this->product);

        session()->flash('success', 'Product updated successfully.');
    }

    /**
     * Delete a media item by ID
     * 
     * @param int $mediaId
     * @return void
     */
    public function deleteMedia($mediaId)
    {
        if (class_exists(Media::class)) {
            $media = Media::find($mediaId);
            if ($media) {
                $media->delete();
                $this->mediaToDelete[] = $mediaId;
            }
        }
    }

    /**
     * Render the component
     */
    public function render()
    {
        // EMERGENCY FIX: Ensure dropdown data is available to the view
        return view('livewire.admin.products.edit-product', [
            'vendors' => $this->vendors,
            'categories' => $this->categories,
            'brands' => $this->brands,
        ]);
    }
}