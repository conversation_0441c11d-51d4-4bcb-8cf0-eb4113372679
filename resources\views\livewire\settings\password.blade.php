<div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
    <div class="p-8 border-b border-gray-100">
        <h3 class="text-2xl font-bold text-gray-900">Update Password</h3>
        <p class="text-gray-500 mt-1">Ensure your account is using a long, random password to stay secure.</p>
    </div>
    <div class="p-8">
        <form wire:submit="updatePassword" class="space-y-6">
            {{-- Success Message --}}
            <div x-data="{ show: false }" x-show="show" x-transition x-init="@this.on('password-updated', () => { show = true; setTimeout(() => show = false, 3000) })">
                <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-500 rounded-full mr-4">
                            <i class="fas fa-check text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-green-800">Password Updated!</h4>
                            <p class="text-sm text-green-700 mt-1">Your password has been changed successfully.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-6">
                {{-- Current Password --}}
                <x-input
                    label="Current Password"
                    type="password"
                    wire:model="current_password"
                    id="current_password"
                    autocomplete="current-password"
                    required
                />

                {{-- New Password --}}
                <x-input
                    label="New Password"
                    type="password"
                    wire:model="password"
                    id="password"
                    autocomplete="new-password"
                    required
                />

                {{-- Confirm New Password --}}
                <x-input
                    label="Confirm New Password"
                    type="password"
                    wire:model="password_confirmation"
                    id="password_confirmation"
                    autocomplete="new-password"
                    required
                />
            </div>

            {{-- Submit Button --}}
            <div class="flex items-center justify-end pt-6 border-t border-gray-100">
                <div class="flex items-center space-x-4">
                    <div wire:loading wire:target="updatePassword" class="flex items-center text-blue-600">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                        <span class="text-sm font-medium">Saving...</span>
                    </div>
                    <x-button
                        wire:loading.attr="disabled"
                        wire:target="updatePassword"
                    >
                        <i class="fas fa-save transition-transform duration-300 group-hover:scale-110"></i>
                        <span>Save Changes</span>
                    </x-button>
                </div>
            </div>
        </form>
    </div>
</div>
