<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductSpecification;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\User;
use App\Models\Review;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductDetailsPageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->vendor = Vendor::factory()->create([
            'is_approved' => true,
            'shop_name' => 'Test Shop'
        ]);
        
        $this->category = Category::factory()->create([
            'name' => 'Test Category'
        ]);
        
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'name' => 'Test Product',
            'description' => 'This is a test product description with multiple lines.\nSecond line of description.',
            'short_description' => 'Short description for test product',
            'price' => 10000,
            'discount_price' => 8000,
            'stock' => 50,
            'sku' => 'TEST-001',
            'is_active' => true
        ]);
    }

    /** @test */
    public function product_details_page_loads_successfully()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee($this->product->name);
        $response->assertSee($this->product->short_description);
        $response->assertSee('₦' . number_format($this->product->price, 0));
        $response->assertSee('₦' . number_format($this->product->discount_price, 0));
    }

    /** @test */
    public function product_specifications_display_correctly()
    {
        // Create specifications for the product
        ProductSpecification::create([
            'product_id' => $this->product->id,
            'name' => 'Material',
            'value' => 'Cotton'
        ]);
        
        ProductSpecification::create([
            'product_id' => $this->product->id,
            'name' => 'Weight',
            'value' => '500g'
        ]);

        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee('Material');
        $response->assertSee('Cotton');
        $response->assertSee('Weight');
        $response->assertSee('500g');
    }

    /** @test */
    public function product_without_specifications_shows_empty_state()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee('No specifications have been added');
    }

    /** @test */
    public function product_reviews_display_correctly()
    {
        $user = User::factory()->create();
        
        Review::create([
            'product_id' => $this->product->id,
            'user_id' => $user->id,
            'rating' => 5,
            'comment' => 'Great product!',
            'is_approved' => true
        ]);

        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee('Great product!');
        $response->assertSee($user->name);
    }

    /** @test */
    public function product_vendor_information_displays()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee($this->vendor->shop_name);
        $response->assertSee('Vendor:');
    }

    /** @test */
    public function product_category_information_displays()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee($this->category->name);
        $response->assertSee('Category:');
    }

    /** @test */
    public function product_stock_status_displays_correctly()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee('In Stock');
        $response->assertSee('50 available');
    }

    /** @test */
    public function out_of_stock_product_shows_correct_status()
    {
        $this->product->update(['stock' => 0]);
        
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        $response->assertSee('Out of Stock');
    }

    /** @test */
    public function product_description_formatting_works()
    {
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        // Check that line breaks are converted to HTML
        $response->assertSee('This is a test product description');
        $response->assertSee('Second line of description');
    }

    /** @test */
    public function product_fallback_to_description_when_no_short_description()
    {
        $this->product->update(['short_description' => null]);
        
        $response = $this->get(route('products.show', $this->product->slug));
        
        $response->assertStatus(200);
        // Should show truncated description
        $response->assertSee('This is a test product description');
    }
}
