.hero-slider {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    transition: opacity 0.5s ease-in-out;
    opacity: 1;
}

.hero-slider.fade-out {
    opacity: 0;
}

.hero-slider__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 1;
}

.hero-slider__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .hero-slider {
        height: 60vh;
    }
}