<?php

namespace App\Livewire\Cart;

use App\Models\Product;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Index extends Component
{
    /** @var \Illuminate\Support\Collection */
    public $cartItems;
    public $subtotal = 0;
    public $total = 0;
    /** @var \Illuminate\Support\Collection */
    public $recommendedProducts;

    protected $listeners = ['cartUpdated' => 'updateCart'];

    public function mount()
    {
        \Log::info('Cart Index Component Mount Started', [
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        $this->cartItems = collect();
        $this->recommendedProducts = collect();
        $this->updateCart();

        \Log::info('Cart Index Component Mount Completed', [
            'cart_items_count' => $this->cartItems->count(),
            'subtotal' => $this->subtotal,
            'total' => $this->total
        ]);
    }

    public function updateCart()
    {
        $cart = session('cart', []);
        $cartWithKeys = collect($cart)->map(function($item, $cartKey) {
            $item['cart_key'] = $cartKey; // Store the cart key for operations
            $item['id'] = $cartKey; // Keep for backward compatibility
            return $item;
        });
        $this->cartItems = $cartWithKeys;
        $this->calculateTotals();
        $this->loadRecommendedProducts();
    }

    /**
     * LOGIC-HIGH-001 FIX: Add stock validation to update quantity
     * This prevents overselling by checking stock availability
     */
    public function updateQuantity($cartKey, $quantity)
    {
        \Log::info('Cart Update Quantity Started', [
            'cart_key' => $cartKey,
            'requested_quantity' => $quantity,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        $quantity = (int)$quantity;
        $cart = session('cart', []);

        \Log::info('Cart Current State for Update', [
            'cart_key' => $cartKey,
            'cart_exists' => isset($cart[$cartKey]),
            'current_quantity' => $cart[$cartKey]['quantity'] ?? 'not_found',
            'total_cart_items' => count($cart)
        ]);

        if (isset($cart[$cartKey])) {
            if ($quantity > 0) {
                // Extract product ID and check stock availability
                $productId = $this->extractProductIdFromCartKey($cartKey);
                $product = \App\Models\Product::find($productId);

                if (!$product) {
                    $this->dispatch('toast',
                        message: 'Product not found. Please refresh your cart.',
                        type: 'error'
                    );
                    return;
                }

                // Check stock availability using the standardized method
                if (!$product->hasSufficientStock($quantity)) {
                    $availableStock = $product->getStockLevel();
                    $this->dispatch('toast',
                        message: "Cannot update quantity. Only {$availableStock} units of {$product->name} are available.",
                        type: 'error'
                    );

                    // Reset to available stock if user tried to exceed it
                    $cart[$cartKey]['quantity'] = $availableStock;
                } else {
                    $cart[$cartKey]['quantity'] = $quantity;
                }
            } else {
                unset($cart[$cartKey]);
            }
            session()->put('cart', $cart);
            $this->updateCart();
            $this->dispatch('cart-updated-total', count($cart)); // For header count
        }
    }

    /**
     * LOGIC-HIGH-001 FIX: Add stock validation to increment quantity
     * This prevents overselling by checking stock availability
     */
    public function incrementQuantity($cartKey)
    {
        $cart = session('cart', []);

        if (isset($cart[$cartKey])) {
            // Extract product ID from cart key (format: product_123 or product_123_variant_456)
            $productId = $this->extractProductIdFromCartKey($cartKey);
            $product = \App\Models\Product::find($productId);

            if (!$product) {
                $this->dispatch('toast',
                    message: 'Product not found. Please refresh your cart.',
                    type: 'error'
                );
                return;
            }

            $newQuantity = $cart[$cartKey]['quantity'] + 1;

            // Check stock availability using the standardized method
            if (!$product->hasSufficientStock($newQuantity)) {
                $availableStock = $product->getStockLevel();
                $this->dispatch('toast',
                    message: "Cannot add more items. Only {$availableStock} units of {$product->name} are available.",
                    type: 'error'
                );
                return;
            }

            $cart[$cartKey]['quantity'] = $newQuantity;
            session()->put('cart', $cart);
            $this->updateCart();
            $this->dispatch('cart-updated-total', count($cart));

            $this->dispatch('toast',
                message: 'Quantity updated successfully.',
                type: 'success'
            );
        }
    }

    public function decrementQuantity($cartKey)
    {
        $cart = session('cart', []);

        if (isset($cart[$cartKey])) {
            if ($cart[$cartKey]['quantity'] > 1) {
                $cart[$cartKey]['quantity']--;
                session()->put('cart', $cart);
                $this->updateCart();
                $this->dispatch('cart-updated-total', count($cart));
            } else {
                // If quantity would become 0, remove the item
                $this->removeItem($cartKey);
            }
        }
    }

    public function removeItem($cartKey)
    {
        \Log::info('Cart Remove Item Started', [
            'cart_key' => $cartKey,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        $cart = session('cart', []);
        $itemExists = isset($cart[$cartKey]);
        $removedItem = $itemExists ? $cart[$cartKey] : null;

        \Log::info('Cart Remove Item Processing', [
            'cart_key' => $cartKey,
            'item_exists' => $itemExists,
            'removed_item' => $removedItem,
            'cart_size_before' => count($cart)
        ]);

        unset($cart[$cartKey]);
        session()->put('cart', $cart);
        $this->updateCart();
        $this->dispatch('cart-updated-total', count($cart)); // For header count

        \Log::info('Cart Remove Item Completed', [
            'cart_key' => $cartKey,
            'cart_size_after' => count($cart),
            'item_was_removed' => $itemExists
        ]);
    }

    public function clearCart()
    {
        session()->forget('cart');
        $this->updateCart();
        $this->dispatch('cart-updated-total', 0); // For header count
    }

    private function calculateTotals()
    {
        $this->subtotal = $this->cartItems->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
        $this->total = $this->subtotal; // Assuming no tax/shipping for now
    }

    private function loadRecommendedProducts()
    {
        if ($this->cartItems->isEmpty()) {
            $this->recommendedProducts = Product::where('is_active', true)
                ->inRandomOrder()
                ->take(4)
                ->get();
            return;
        }

        // Extract product IDs from cart items (handle both old and new cart structure)
        $productIds = $this->cartItems->map(function ($item) {
            return $item['product_id'] ?? $item['id'] ?? null;
        })->filter()->unique()->toArray();

        if (empty($productIds)) {
            $this->recommendedProducts = Product::where('is_active', true)
                ->inRandomOrder()
                ->take(4)
                ->get();
            return;
        }

        $relatedProducts = Product::whereIn('id', $productIds)->get();
        $categoryIds = $relatedProducts->pluck('category_id')->filter()->unique()->toArray();
        $vendorIds = $relatedProducts->pluck('vendor_id')->filter()->unique()->toArray();

        $recommendations = Product::where('is_active', true)
            ->where(function ($query) use ($categoryIds, $vendorIds) {
                if (!empty($categoryIds)) {
                    $query->whereIn('category_id', $categoryIds);
                }
                if (!empty($vendorIds)) {
                    $query->orWhereIn('vendor_id', $vendorIds);
                }
            })
            ->whereNotIn('id', $productIds)
            ->inRandomOrder()
            ->take(4)
            ->get();

        if ($recommendations->count() < 4) {
            $additionalProducts = Product::where('is_active', true)
                ->whereNotIn('id', $productIds)
                ->whereNotIn('id', $recommendations->pluck('id')->toArray())
                ->inRandomOrder()
                ->take(4 - $recommendations->count())
                ->get();

            $recommendations = $recommendations->merge($additionalProducts);
        }

        $this->recommendedProducts = $recommendations;
    }

    /**
     * LOGIC-HIGH-001 FIX: Helper method to extract product ID from cart key
     * Cart keys can be in format: product_123 or product_123_variant_456
     */
    private function extractProductIdFromCartKey(string $cartKey): ?int
    {
        // Handle both formats: product_123 and product_123_variant_456
        if (preg_match('/^product_(\d+)/', $cartKey, $matches)) {
            return (int) $matches[1];
        }

        // Fallback: if cart key is just a number (legacy format)
        if (is_numeric($cartKey)) {
            return (int) $cartKey;
        }

        return null;
    }

    public function render()
    {
        return view('livewire.cart.index');
    }
}
