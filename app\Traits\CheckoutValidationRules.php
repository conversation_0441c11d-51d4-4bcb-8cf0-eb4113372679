<?php

namespace App\Traits;

/**
 * Standardized checkout validation rules trait
 * 
 * This trait provides consistent validation rules for checkout forms
 * to resolve LOGIC-CRITICAL-002: Checkout validation gap
 * 
 * Usage:
 * - Checkout Livewire Components
 * - Payment Controllers
 * - Order Processing
 */
trait CheckoutValidationRules
{
    /**
     * Get standardized checkout validation rules
     * 
     * @param bool $requirePaymentMethod Whether payment method is required
     * @param bool $requireShippingOptions Whether shipping options are required
     * @return array
     */
    protected function getCheckoutValidationRules(bool $requirePaymentMethod = true, bool $requireShippingOptions = true): array
    {
        $rules = [
            // Personal Information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
            
            // Shipping Address
            'street' => 'required|string|min:10|max:255',
            'city' => 'required|string|min:2|max:255',
            'state' => 'required|string|max:255',
            'lga' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
        ];

        // Add payment method validation if required
        if ($requirePaymentMethod) {
            $rules['payment_method'] = 'required|in:paystack,bank_transfer,cash_on_delivery';
        }

        // Add shipping options validation if required
        if ($requireShippingOptions) {
            $rules['shipping_options'] = 'required|array';
            $rules['shipping_options.*'] = 'required|array';
            $rules['shipping_options.*.courier_id'] = 'required|string';
            $rules['shipping_options.*.service_code'] = 'required|string';
            $rules['shipping_options.*.total'] = 'required|numeric|min:0';
        }

        return $rules;
    }

    /**
     * Get validation rules for prefixed checkout fields (e.g., 'shippingAddress.street')
     * Used by Livewire components that use nested object notation
     * 
     * @param string $prefix The field prefix (e.g., 'shippingAddress')
     * @param bool $requirePaymentMethod Whether payment method is required
     * @param bool $requireShippingOptions Whether shipping options are required
     * @return array
     */
    protected function getPrefixedCheckoutValidationRules(string $prefix, bool $requirePaymentMethod = true, bool $requireShippingOptions = true): array
    {
        $baseRules = $this->getCheckoutValidationRules($requirePaymentMethod, $requireShippingOptions);
        $prefixedRules = [];

        // Map standard fields to prefixed fields
        $fieldMapping = [
            'first_name' => 'first_name',
            'last_name' => 'last_name',
            'email' => 'email',
            'phone' => 'phone',
            'street' => 'address', // Note: address field maps to street
            'city' => 'city',
            'state' => 'state',
            'lga' => 'lga',
            'postal_code' => 'postal_code',
            'country' => 'country',
        ];

        foreach ($fieldMapping as $standardField => $prefixedField) {
            if (isset($baseRules[$standardField])) {
                $prefixedRules["{$prefix}.{$prefixedField}"] = $baseRules[$standardField];
            }
        }

        // Add non-prefixed fields
        if ($requirePaymentMethod && isset($baseRules['payment_method'])) {
            $prefixedRules['payment_method'] = $baseRules['payment_method'];
        }

        if ($requireShippingOptions) {
            $prefixedRules['selectedShippingRate'] = 'required|array';
            $prefixedRules['selectedShippingRate.courier_id'] = 'required|string';
            $prefixedRules['selectedShippingRate.service_code'] = 'required|string';
            $prefixedRules['selectedShippingRate.total'] = 'required|numeric|min:0';
        }

        return $prefixedRules;
    }

    /**
     * Get custom validation messages for checkout fields
     * 
     * @param string $prefix Optional prefix for field names
     * @return array
     */
    protected function getCheckoutValidationMessages(string $prefix = ''): array
    {
        $fieldPrefix = $prefix ? "{$prefix}." : '';
        
        return [
            // Personal Information Messages
            "{$fieldPrefix}first_name.required" => 'First name is required.',
            "{$fieldPrefix}first_name.max" => 'First name cannot exceed 255 characters.',
            "{$fieldPrefix}last_name.required" => 'Last name is required.',
            "{$fieldPrefix}last_name.max" => 'Last name cannot exceed 255 characters.',
            "{$fieldPrefix}email.required" => 'Email address is required.',
            "{$fieldPrefix}email.email" => 'Please enter a valid email address.',
            "{$fieldPrefix}email.max" => 'Email address cannot exceed 255 characters.',
            "{$fieldPrefix}phone.required" => 'Phone number is required for delivery.',
            "{$fieldPrefix}phone.regex" => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
            
            // Shipping Address Messages
            "{$fieldPrefix}street.required" => 'Street address is required.',
            "{$fieldPrefix}address.required" => 'Street address is required.',
            "{$fieldPrefix}street.min" => 'Address must be at least 10 characters long for accurate delivery.',
            "{$fieldPrefix}address.min" => 'Address must be at least 10 characters long for accurate delivery.',
            "{$fieldPrefix}street.max" => 'Street address cannot exceed 255 characters.',
            "{$fieldPrefix}address.max" => 'Street address cannot exceed 255 characters.',
            "{$fieldPrefix}city.required" => 'City is required.',
            "{$fieldPrefix}city.min" => 'City name must be at least 2 characters long.',
            "{$fieldPrefix}city.max" => 'City name cannot exceed 255 characters.',
            "{$fieldPrefix}state.required" => 'State is required.',
            "{$fieldPrefix}state.max" => 'State name cannot exceed 255 characters.',
            "{$fieldPrefix}lga.max" => 'LGA name cannot exceed 255 characters.',
            "{$fieldPrefix}postal_code.max" => 'Postal code cannot exceed 20 characters.',
            "{$fieldPrefix}country.required" => 'Country is required.',
            "{$fieldPrefix}country.max" => 'Country name cannot exceed 255 characters.',
            
            // Payment and Shipping Messages
            'payment_method.required' => 'Please select a payment method.',
            'payment_method.in' => 'Please select a valid payment method.',
            'shipping_options.required' => 'Please select shipping options.',
            'shipping_options.array' => 'Invalid shipping options format.',
            'selectedShippingRate.required' => 'Please select a shipping option.',
            'selectedShippingRate.array' => 'Invalid shipping rate format.',
            'selectedShippingRate.courier_id.required' => 'Shipping courier is required.',
            'selectedShippingRate.service_code.required' => 'Shipping service is required.',
            'selectedShippingRate.total.required' => 'Shipping cost is required.',
            'selectedShippingRate.total.numeric' => 'Shipping cost must be a valid number.',
            'selectedShippingRate.total.min' => 'Shipping cost cannot be negative.',
        ];
    }

    /**
     * Get validation attributes for better error messages
     * 
     * @param string $prefix Optional prefix for field names
     * @return array
     */
    protected function getCheckoutValidationAttributes(string $prefix = ''): array
    {
        $fieldPrefix = $prefix ? "{$prefix}." : '';
        
        return [
            "{$fieldPrefix}first_name" => 'first name',
            "{$fieldPrefix}last_name" => 'last name',
            "{$fieldPrefix}email" => 'email address',
            "{$fieldPrefix}phone" => 'phone number',
            "{$fieldPrefix}street" => 'street address',
            "{$fieldPrefix}address" => 'street address',
            "{$fieldPrefix}city" => 'city',
            "{$fieldPrefix}state" => 'state',
            "{$fieldPrefix}lga" => 'LGA',
            "{$fieldPrefix}postal_code" => 'postal code',
            "{$fieldPrefix}country" => 'country',
            'payment_method' => 'payment method',
            'shipping_options' => 'shipping options',
            'selectedShippingRate' => 'shipping rate',
        ];
    }

    /**
     * Validate checkout data using standardized rules
     * 
     * @param array $data Data to validate
     * @param bool $requirePaymentMethod Whether payment method is required
     * @param bool $requireShippingOptions Whether shipping options are required
     * @param string $prefix Optional field prefix
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validateCheckoutData(array $data, bool $requirePaymentMethod = true, bool $requireShippingOptions = true, string $prefix = ''): \Illuminate\Contracts\Validation\Validator
    {
        $rules = $prefix 
            ? $this->getPrefixedCheckoutValidationRules($prefix, $requirePaymentMethod, $requireShippingOptions)
            : $this->getCheckoutValidationRules($requirePaymentMethod, $requireShippingOptions);

        $messages = $this->getCheckoutValidationMessages($prefix);
        $attributes = $this->getCheckoutValidationAttributes($prefix);

        return \Illuminate\Support\Facades\Validator::make($data, $rules, $messages, $attributes);
    }

    /**
     * Get Nigerian phone number validation rule
     * 
     * @return string
     */
    protected function getNigerianPhoneValidationRule(): string
    {
        return 'required|string|regex:/^0[789][01]\\d{8}$/';
    }

    /**
     * Get payment method validation rule
     * 
     * @return string
     */
    protected function getPaymentMethodValidationRule(): string
    {
        return 'required|in:paystack,bank_transfer,cash_on_delivery';
    }
}
