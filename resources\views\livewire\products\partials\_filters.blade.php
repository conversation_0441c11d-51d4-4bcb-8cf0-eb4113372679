<div class="space-y-6">
    <h3 class="text-xl font-semibold text-gray-800">Filters</h3>
    <div class="border-t border-gray-200"></div>

    <!-- Category Filter -->
    <div>
        <h4 class="text-lg font-medium text-gray-700">Categories</h4>
        <div class="mt-2 space-y-1">
            @if(isset($categories) && $categories->count())
                <button wire:click="$set('category', '')" 
                       class="block w-full text-left px-3 py-1 text-sm rounded-md transition-colors duration-150 
                              {{ !$category ? 'bg-black text-white font-semibold' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
                    All Categories
                </button>
                @foreach($categories as $cat)
                    <button wire:click="$set('category', '{{ $cat->slug }}')" 
                       class="block w-full text-left px-3 py-1 text-sm rounded-md transition-colors duration-150 
                              {{ $category == $cat->slug ? 'bg-black text-white font-semibold' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
                        {{ $cat->name }}
                    </button>
                @endforeach
            @else
                <p class="text-sm text-gray-500">No categories available.</p>
            @endif
        </div>
    </div>
    <div class="border-t border-gray-200"></div>

    <!-- Price Filter -->
    <div>
        <h4 class="text-lg font-medium text-gray-700">Price Range</h4>
        <div class="grid grid-cols-2 gap-2 mt-2">
            <input wire:model.live.debounce.500ms="min_price" type="number" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-black focus:border-black" placeholder="Min ₦">
            <input wire:model.live.debounce.500ms="max_price" type="number" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-black focus:border-black" placeholder="Max ₦">
        </div>
    </div>
    <div class="border-t border-gray-200"></div>

    <!-- Sorting -->
    <div>
        <h4 class="text-lg font-medium text-gray-700">Sort By</h4>
        <select wire:model.live="sort_by" class="w-full px-3 py-2 mt-2 text-sm border border-gray-300 rounded-md focus:ring-black focus:border-black">
            <option value="latest">Latest</option>
            <option value="price_asc">Price: Low to High</option>
            <option value="price_desc">Price: High to Low</option>
            <option value="name_asc">Name: A-Z</option>
            <option value="name_desc">Name: Z-A</option>
        </select>
    </div>
    <div class="border-t border-gray-200"></div>

    <div class="flex flex-col space-y-2">
        <button wire:click="clearFilters" class="w-full px-4 py-2 text-sm font-medium text-center text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Clear All Filters</button>
    </div>
</div>
