<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShipBubbleService
{
    protected $apiKey;
    protected $baseUrl;
    protected $maxRetries = 2;
    protected $retryDelay = 1000; // milliseconds
    protected $cacheService;

    // Circuit breaker properties
    protected $circuitBreakerKey = 'shipbubble_circuit_breaker';
    protected $failureThreshold = 5; // Number of failures before opening circuit
    protected $recoveryTimeout = 300; // 5 minutes in seconds

    public function __construct(ShipBubbleCacheService $cacheService = null)
    {
        $this->apiKey = config('services.shipbubble.key');
        $this->baseUrl = config('services.shipbubble.url', 'https://api.shipbubble.com/v1');
        $this->cacheService = $cacheService ?? new ShipBubbleCacheService();
    }

    public function getRates(array $data)
    {
        return $this->sendRequest('post', '/shipping/fetch_rates', $data);
    }

    public function createShipment(array $data)
    {
        return $this->sendRequest('post', '/shipping/labels', $data);
    }

    public function validateAddress(array $data)
    {
        // Check cache first if caching is enabled
        if ($this->cacheService->isCachingEnabled()) {
            $cacheKey = $this->cacheService->generateAddressCacheKey($data);
            $cachedResult = $this->cacheService->getCachedAddressValidation($cacheKey);

            if ($cachedResult !== null) {
                return $cachedResult;
            }
        }

        // Pre-validate data before making API call
        $validationErrors = $this->preValidateAddressData($data);
        if (!empty($validationErrors)) {
            return [
                'status' => 'error',
                'message' => 'Address validation failed: ' . implode(', ', $validationErrors),
                'details' => $validationErrors,
                'type' => 'validation_error'
            ];
        }

        // Build a simple, clean address string using only the essential components.
        $addressParts = [
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
        ];

        // Filter out any null or empty parts.
        $filteredParts = array_filter($addressParts);

        // Join the parts into a single, comma-separated string.
        $addressString = implode(', ', $filteredParts);

        // Append the country for clarity, as the API seems to require it.
        $addressString .= ', Nigeria';

        // Sanitize the final address string to remove any characters the API might reject.
        $sanitizedAddress = preg_replace('/[^\p{L}\p{N}\s,]/u', '', $addressString);

        // Final cleanup to remove potential duplicate commas or leading/trailing commas/spaces.
        $sanitizedAddress = trim(preg_replace('/,{2,}/', ',', $sanitizedAddress), ' ,');

        // Construct the final payload with the clean address.
        $payload = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'address' => $sanitizedAddress,
        ];

        $result = $this->sendRequest('post', '/shipping/address/validate', $payload);

        // Cache the result if successful and caching is enabled
        if ($this->cacheService->isCachingEnabled() && isset($result['status']) && $result['status'] === 'success') {
            $this->cacheService->cacheAddressValidation($cacheKey, $result);
        }

        return $result;
    }

    /**
     * Track a shipment using Shipbubble API
     *
     * @param string $trackingCode
     * @return array
     */
    public function trackShipment(string $trackingCode)
    {
        return $this->sendRequest('get', "/tracking/{$trackingCode}");
    }

    /**
     * Get shipping rates for cart items and address
     * This method formats cart data for Shipbubble API
     *
     * @param array $cartItems
     * @param array $shippingAddress
     * @param \App\Models\Vendor|null $vendor
     * @return array
     */
    public function getShippingRates(array $cartItems, array $shippingAddress, $vendor = null)
    {
        // Check cache first if caching is enabled
        if ($this->cacheService->isCachingEnabled()) {
            $cacheKey = $this->cacheService->generateRatesCacheKey($cartItems, $shippingAddress, $vendor);
            $cachedRates = $this->cacheService->getCachedShippingRates($cacheKey);

            if ($cachedRates !== null) {
                return $cachedRates;
            }
        }

        try {
            // First, we need to validate/create addresses
            $senderAddress = $this->getOrCreateSenderAddress($vendor);
            $receiverAddress = $this->getOrCreateReceiverAddress($shippingAddress);

            if (!$senderAddress || !$receiverAddress) {
                throw new \Exception('Failed to validate addresses');
            }
        } catch (\Exception $e) {
            // Log the error and return a user-friendly message
            Log::error('ShipBubble address validation failed', [
                'error' => $e->getMessage(),
                'vendor_id' => $vendor?->id,
                'shipping_address' => $shippingAddress
            ]);

            return [
                'status' => 'error',
                'message' => 'Unable to validate shipping addresses. Please check your address details and try again.',
                'details' => $e->getMessage(),
                'type' => 'address_validation_error'
            ];
        }

        // Format package items for Shipbubble
        $packageItems = [];
        $totalWeight = 0;
        $totalAmount = 0;

        foreach ($cartItems as $item) {
            $weight = ($item['weight'] ?? 0.5) / 1000; // Convert grams to kg, default 500g
            $amount = $item['price'] ?? 0;
            $quantity = $item['quantity'] ?? 1;

            $packageItems[] = [
                'name' => $item['name'] ?? 'Product',
                'description' => $item['description'] ?? 'Product description',
                'unit_weight' => (string)$weight,
                'unit_amount' => (string)$amount,
                'quantity' => (string)$quantity,
            ];

            $totalWeight += $weight * $quantity;
            $totalAmount += $amount * $quantity;
        }

        // Calculate package dimensions (you may want to make this configurable)
        $packageDimension = [
            'length' => 30, // cm
            'width' => 20,  // cm
            'height' => 15, // cm
        ];

        $rateData = [
            'sender_address_code' => $senderAddress['code'],
            'reciever_address_code' => $receiverAddress['code'],
            'pickup_date' => now()->addDay()->format('Y-m-d'), // Next day pickup
            'category_id' => config('services.shipbubble.default_category_id', 74794423), // Configurable category ID
            'package_items' => $packageItems,
            'package_dimension' => $packageDimension,
            'delivery_instructions' => 'Handle with care',
        ];

        $result = $this->getRates($rateData);

        // Cache the result if successful and caching is enabled
        if ($this->cacheService->isCachingEnabled() && isset($result['status']) && $result['status'] === 'success') {
            $cacheKey = $this->cacheService->generateRatesCacheKey($cartItems, $shippingAddress, $vendor);
            $this->cacheService->cacheShippingRates($cacheKey, $result);
        }

        return $result;
    }

    /**
     * Get or create sender address (business address)
     *
     * @param \App\Models\Vendor|null $vendor
     * @return array|null
     * @throws \Exception
     */
    protected function getOrCreateSenderAddress($vendor = null)
    {
        if (!$vendor) {
            // Fallback to default business address from config
            $defaultAddress = [
                'name' => config('app.name', 'Brandify'),
                'email' => config('mail.from.address', '<EMAIL>'),
                'phone' => $this->formatNigerianPhoneNumber(config('services.shipbubble.default_phone', '+2348000000000')),
                'address' => config('services.shipbubble.default_address', 'Lagos, Nigeria'),
            ];

            $result = $this->validateAddress($defaultAddress);

            if (isset($result['status']) && $result['status'] === 'success') {
                return ['code' => $result['data']['address_code'] ?? null];
            }

            // FIXED: Throw exception instead of returning null
            $errorMessage = $result['message'] ?? 'Default address validation failed';
            Log::error('ShipBubble default sender address validation failed', [
                'address_data' => $defaultAddress,
                'api_response' => $result,
            ]);
            throw new \Exception("Failed to validate default sender address: {$errorMessage}");
        }

        // Use vendor's business address with null checks
        $vendorAddress = [
            'name' => $vendor->user?->name ?? $vendor->business_name,
            'email' => $vendor->user?->email ?? 'noreply@' . config('app.domain', 'example.com'),
            'phone' => $this->formatNigerianPhoneNumber($vendor->phone ?? '+2348000000000'),
            'address' => $this->buildVendorAddress($vendor),
        ];

        $result = $this->validateAddress($vendorAddress);

        if (isset($result['status']) && $result['status'] === 'success') {
            return ['code' => $result['data']['address_code'] ?? null];
        }

        // FIXED: Throw exception instead of returning null
        $errorMessage = $result['message'] ?? 'Vendor address validation failed';
        Log::error('ShipBubble vendor sender address validation failed', [
            'vendor_id' => $vendor->id,
            'address_data' => $vendorAddress,
            'api_response' => $result,
        ]);
        throw new \Exception("Failed to validate vendor sender address for {$vendor->business_name}: {$errorMessage}");
    }

    /**
     * Build a clean vendor address string
     *
     * @param \App\Models\Vendor $vendor
     * @return string
     */
    protected function buildVendorAddress($vendor)
    {
        $addressParts = [
            $vendor->business_address,
            $vendor->city,
            $vendor->state,
            $vendor->country ?? 'Nigeria',
        ];

        // Filter out empty parts and join with commas
        $cleanParts = array_filter($addressParts, function($part) {
            return !empty(trim($part));
        });

        return implode(', ', $cleanParts);
    }

    /**
     * Get or create receiver address from shipping address
     *
     * @param array $shippingAddress
     * @return array|null
     * @throws \Exception
     */
    protected function getOrCreateReceiverAddress(array $shippingAddress)
    {
        // Validate that we have a customer phone number
        if (empty($shippingAddress['phone'])) {
            throw new \Exception('Customer phone number is required for shipping address validation');
        }

        $addressData = [
            'name' => $shippingAddress['name'] ?? 'Customer',
            'email' => $shippingAddress['email'] ?? '<EMAIL>',
            'phone' => $this->formatNigerianPhoneNumber($shippingAddress['phone']),
            'address' => $shippingAddress['address'] ?? '',
            'city' => $shippingAddress['city'] ?? '',
            'state' => $shippingAddress['state'] ?? '',
            'country' => $shippingAddress['country'] ?? 'NG',
        ];

        $result = $this->validateAddress($addressData);

        if (isset($result['status']) && $result['status'] === 'success') {
            return ['code' => $result['data']['address_code'] ?? null];
        }

        // FIXED: Throw exception instead of returning mock address code
        $errorMessage = $result['message'] ?? 'Address validation failed';
        $errorDetails = $result['details'] ?? 'Unknown error';

        Log::error('ShipBubble receiver address validation failed', [
            'address_data' => $addressData,
            'api_response' => $result,
        ]);

        throw new \Exception("Failed to validate receiver address: {$errorMessage}. Details: {$errorDetails}");
    }

    protected function sendRequest(string $method, string $endpoint, array $data = [])
    {
        // Check circuit breaker
        if ($this->isCircuitOpen()) {
            Log::warning('ShipBubble circuit breaker is open, skipping API call');
            return [
                'status' => 'error',
                'message' => 'ShipBubble service is temporarily unavailable. Please try again later.',
                'details' => 'Circuit breaker is open due to repeated failures.',
                'type' => 'circuit_breaker_open'
            ];
        }

        // Validate API key
        if (empty($this->apiKey)) {
            Log::error('ShipBubble API key not configured');
            $this->recordFailure();
            return [
                'status' => 'error',
                'message' => 'ShipBubble API key not configured.',
                'details' => 'Please set SHIPBUBBLE_API_KEY in your environment variables.'
            ];
        }

        Log::info('ShipBubble API Request:', [
            'endpoint' => $endpoint,
            'method' => strtoupper($method),
            'payload_size' => count($data),
            'timestamp' => now()->toISOString()
        ]);

        try {
            $response = Http::withToken($this->apiKey)
                ->acceptJson()
                ->timeout(30) // 30 second timeout
                ->retry($this->maxRetries, $this->retryDelay, function ($exception, $request) {
                    // Only retry on specific conditions to prevent infinite loops
                    if ($exception instanceof \Illuminate\Http\Client\ConnectionException) {
                        return true; // Retry connection errors
                    }

                    if ($exception instanceof \Illuminate\Http\Client\RequestException) {
                        $status = $exception->response?->status();
                        // Only retry on server errors (5xx), not client errors (4xx)
                        return $status >= 500;
                    }

                    return false; // Don't retry other exceptions
                })
                ->{$method}($this->baseUrl . $endpoint, $data);

            $responseData = $response->json();

            if ($response->failed()) {
                $errorMessage = $this->parseErrorMessage($responseData);
                $userFriendlyMessage = $this->formatUserFriendlyErrorMessage($errorMessage);

                // Record failure for circuit breaker
                $this->recordFailure();

                // Enhanced logging with more context
                Log::error('ShipBubble API Error', [
                    'endpoint' => $endpoint,
                    'method' => strtoupper($method),
                    'http_status' => $response->status(),
                    'raw_error_message' => $errorMessage,
                    'user_friendly_message' => $userFriendlyMessage,
                    'request_data' => $this->sanitizeLogData($data),
                    'response_data' => $responseData,
                    'timestamp' => now()->toISOString(),
                    'error_category' => $this->categorizeError($errorMessage),
                    'retry_count' => $this->maxRetries
                ]);

                return [
                    'status' => 'error',
                    'message' => $userFriendlyMessage,
                    'technical_details' => $errorMessage,
                    'response_data' => $responseData,
                    'http_status' => $response->status(),
                    'error_category' => $this->categorizeError($errorMessage)
                ];
            }

            // Record success for circuit breaker
            $this->recordSuccess();

            Log::info('ShipBubble API Success:', [
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'response_status' => $responseData['status'] ?? 'unknown'
            ]);

            return $responseData;

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            // Record failure for circuit breaker
            $this->recordFailure();

            Log::error('ShipBubble Connection Error:', [
                'endpoint' => $endpoint,
                'message' => $e->getMessage(),
                'type' => 'connection_error'
            ]);

            return [
                'status' => 'error',
                'message' => 'Unable to connect to ShipBubble API. Please check your internet connection.',
                'details' => $e->getMessage(),
                'type' => 'connection_error'
            ];

        } catch (\Illuminate\Http\Client\RequestException $e) {
            // Record failure for circuit breaker
            $this->recordFailure();

            Log::error('ShipBubble Request Error:', [
                'endpoint' => $endpoint,
                'message' => $e->getMessage(),
                'type' => 'request_error'
            ]);

            return [
                'status' => 'error',
                'message' => 'ShipBubble API request failed. Please try again.',
                'details' => $e->getMessage(),
                'type' => 'request_error'
            ];

        } catch (\Exception $e) {
            // Record failure for circuit breaker
            $this->recordFailure();

            Log::critical('ShipBubble Service Exception:', [
                'endpoint' => $endpoint,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'status' => 'error',
                'message' => 'An unexpected error occurred while processing your shipping request.',
                'details' => $e->getMessage(),
                'type' => 'service_error'
            ];
        }
    }

    /**
     * Parse error message from ShipBubble API response and provide user-friendly messages
     */
    protected function parseErrorMessage($responseData)
    {
        if (is_array($responseData)) {
            // Check for common error message fields
            $rawMessage = '';

            if (isset($responseData['message'])) {
                $rawMessage = $responseData['message'];
            } elseif (isset($responseData['error'])) {
                $rawMessage = is_string($responseData['error']) ? $responseData['error'] : 'API error occurred';
            } elseif (isset($responseData['errors']) && is_array($responseData['errors'])) {
                $rawMessage = implode(', ', array_values($responseData['errors']));
            }

            // Convert technical error messages to user-friendly ones
            return $this->formatUserFriendlyErrorMessage($rawMessage);
        }

        return 'ShipBubble shipping service is temporarily unavailable. Please try again later.';
    }

    /**
     * Convert technical error messages to user-friendly messages
     */
    protected function formatUserFriendlyErrorMessage($rawMessage)
    {
        $message = strtolower($rawMessage);

        // Address validation errors
        if (str_contains($message, 'invalid address') || str_contains($message, 'address not found')) {
            return 'The shipping address provided is invalid or cannot be found. Please check your address details and try again.';
        }

        if (str_contains($message, 'invalid phone') || str_contains($message, 'phone number')) {
            return 'The phone number provided is invalid. Please enter a valid Nigerian phone number (e.g., +2348012345678).';
        }

        if (str_contains($message, 'invalid state') || str_contains($message, 'state not found')) {
            return 'The state provided is invalid. Please select a valid Nigerian state from the dropdown.';
        }

        if (str_contains($message, 'invalid lga') || str_contains($message, 'lga not found')) {
            return 'The Local Government Area (LGA) provided is invalid. Please select a valid LGA for your state.';
        }

        // Authentication errors
        if (str_contains($message, 'unauthorized') || str_contains($message, 'invalid api key')) {
            return 'Shipping service authentication failed. Please contact support if this issue persists.';
        }

        // Rate limit errors
        if (str_contains($message, 'rate limit') || str_contains($message, 'too many requests')) {
            return 'Too many shipping requests. Please wait a moment and try again.';
        }

        // Service unavailable errors
        if (str_contains($message, 'service unavailable') || str_contains($message, 'server error')) {
            return 'Shipping service is temporarily unavailable. Please try again in a few minutes.';
        }

        // Package/weight errors
        if (str_contains($message, 'weight') || str_contains($message, 'package size')) {
            return 'The package weight or dimensions are invalid. Please check your product specifications.';
        }

        // Distance/location errors
        if (str_contains($message, 'no courier available') || str_contains($message, 'location not serviceable')) {
            return 'Shipping is not available to this location. Please try a different address or contact support.';
        }

        // Payment/billing errors
        if (str_contains($message, 'insufficient funds') || str_contains($message, 'payment')) {
            return 'Shipping payment could not be processed. Please contact support.';
        }

        // Generic network errors
        if (str_contains($message, 'timeout') || str_contains($message, 'connection')) {
            return 'Connection to shipping service timed out. Please check your internet connection and try again.';
        }

        // If no specific pattern matches, return a cleaned version of the original message
        if (!empty($rawMessage)) {
            // Capitalize first letter and ensure it ends with a period
            $cleanMessage = ucfirst(trim($rawMessage));
            if (!str_ends_with($cleanMessage, '.')) {
                $cleanMessage .= '.';
            }
            return $cleanMessage;
        }

        return 'An error occurred while processing your shipping request. Please try again or contact support.';
    }

    /**
     * Categorize error for better tracking and analytics
     */
    protected function categorizeError($errorMessage)
    {
        $message = strtolower($errorMessage);

        if (str_contains($message, 'address') || str_contains($message, 'location')) {
            return 'address_validation';
        }

        if (str_contains($message, 'phone')) {
            return 'phone_validation';
        }

        if (str_contains($message, 'unauthorized') || str_contains($message, 'api key')) {
            return 'authentication';
        }

        if (str_contains($message, 'rate limit') || str_contains($message, 'too many')) {
            return 'rate_limit';
        }

        if (str_contains($message, 'service unavailable') || str_contains($message, 'server error')) {
            return 'service_unavailable';
        }

        if (str_contains($message, 'weight') || str_contains($message, 'package') || str_contains($message, 'dimension')) {
            return 'package_validation';
        }

        if (str_contains($message, 'courier') || str_contains($message, 'serviceable')) {
            return 'courier_availability';
        }

        if (str_contains($message, 'payment') || str_contains($message, 'funds')) {
            return 'payment_issue';
        }

        if (str_contains($message, 'timeout') || str_contains($message, 'connection')) {
            return 'network_issue';
        }

        return 'unknown';
    }

    /**
     * Sanitize sensitive data from logs
     */
    protected function sanitizeLogData($data)
    {
        if (!is_array($data)) {
            return $data;
        }

        $sanitized = $data;

        // Remove or mask sensitive fields
        $sensitiveFields = ['api_key', 'token', 'password', 'secret'];

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '***REDACTED***';
            }
        }

        // Mask phone numbers (keep first 3 and last 2 digits)
        if (isset($sanitized['phone'])) {
            $phone = $sanitized['phone'];
            if (strlen($phone) > 5) {
                $sanitized['phone'] = substr($phone, 0, 3) . '***' . substr($phone, -2);
            }
        }

        // Recursively sanitize nested arrays
        foreach ($sanitized as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeLogData($value);
            }
        }

        return $sanitized;
    }

    /**
     * Format Nigerian phone number for ShipBubble API
     * ShipBubble expects format: 08012345678 (11 digits starting with 0)
     */
    protected function formatNigerianPhoneNumber($phone)
    {
        if (empty($phone)) {
            return '08000000000'; // Default fallback
        }

        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Handle different formats
        if (strlen($phone) === 13 && substr($phone, 0, 3) === '234') {
            // Format: 2348012345678 -> 08012345678
            $phone = '0' . substr($phone, 3);
        } elseif (strlen($phone) === 10 && substr($phone, 0, 1) === '8') {
            // Format: 8012345678 -> 08012345678
            $phone = '0' . $phone;
        } elseif (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
            // Format: 08012345678 -> already correct
            // Do nothing
        } else {
            // Invalid format, use default
            Log::warning('Invalid Nigerian phone number format', [
                'original' => $phone,
                'length' => strlen($phone),
                'using_default' => '08000000000'
            ]);
            return '08000000000';
        }

        // Validate final format
        if (strlen($phone) !== 11 || substr($phone, 0, 1) !== '0') {
            Log::warning('Phone number validation failed after formatting', [
                'formatted' => $phone,
                'using_default' => '08000000000'
            ]);
            return '08000000000';
        }

        return $phone;
    }

    /**
     * Validate if a phone number is in correct Nigerian format for ShipBubble
     */
    protected function isValidNigerianPhoneNumber($phone)
    {
        // Must be exactly 11 digits starting with 0
        return preg_match('/^0[789][01]\d{8}$/', $phone);
    }

    /**
     * Pre-validate address data before API call
     */
    protected function preValidateAddressData($addressData)
    {
        $errors = [];

        if (empty($addressData['name'])) {
            $errors[] = 'Name is required';
        }

        if (empty($addressData['email']) || !filter_var($addressData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid email is required';
        }

        if (empty($addressData['phone'])) {
            $errors[] = 'Phone number is required';
        } elseif (!$this->isValidNigerianPhoneNumber($addressData['phone'])) {
            $errors[] = 'Invalid Nigerian phone number format. Expected format: 08012345678';
        }

        if (empty($addressData['address'])) {
            $errors[] = 'Address is required';
        }

        return $errors;
    }

    /**
     * Check if circuit breaker is open
     */
    protected function isCircuitOpen()
    {
        $circuitData = cache()->get($this->circuitBreakerKey);

        if (!$circuitData) {
            return false; // Circuit is closed (normal operation)
        }

        // Check if recovery timeout has passed
        if (time() - $circuitData['opened_at'] > $this->recoveryTimeout) {
            cache()->forget($this->circuitBreakerKey);
            return false; // Circuit is now closed
        }

        return true; // Circuit is still open
    }

    /**
     * Record a failure and potentially open the circuit
     */
    protected function recordFailure()
    {
        $key = $this->circuitBreakerKey . '_failures';
        $failures = cache()->get($key, 0) + 1;

        cache()->put($key, $failures, now()->addMinutes(10));

        if ($failures >= $this->failureThreshold) {
            // Open the circuit
            cache()->put($this->circuitBreakerKey, [
                'opened_at' => time(),
                'failure_count' => $failures
            ], now()->addMinutes(10));

            Log::warning('ShipBubble circuit breaker opened', [
                'failure_count' => $failures,
                'threshold' => $this->failureThreshold
            ]);
        }
    }

    /**
     * Record a success and reset failure count
     */
    protected function recordSuccess()
    {
        cache()->forget($this->circuitBreakerKey . '_failures');
        cache()->forget($this->circuitBreakerKey);
    }
}
