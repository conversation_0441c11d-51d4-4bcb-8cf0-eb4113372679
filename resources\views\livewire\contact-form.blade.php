<div class="mt-6">
    <form wire:submit.prevent="submit" class="space-y-6">
        @if ($success)
            <div class="rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-green-500 rounded-full mr-4">
                        <i class="fas fa-check text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-green-800">Message Sent Successfully!</h4>
                        <p class="text-sm text-green-700 mt-1">Thank you for contacting us. We will get back to you shortly.</p>
                    </div>
                </div>
            </div>
        @endif

        @if ($error)
            <div class="rounded-2xl bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-500 p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-red-500 rounded-full mr-4">
                        <i class="fas fa-exclamation-triangle text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-red-800">Message Failed to Send</h4>
                        <p class="text-sm text-red-700 mt-1">Sorry, we could not send your message at this time. Please try again later.</p>
                    </div>
                </div>
            </div>
        @endif

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
                <label for="name" class="block text-sm font-bold text-gray-900 dark:text-white mb-2">
                    <i class="fas fa-user mr-2 text-gray-500"></i>Full Name
                </label>
                <div class="relative">
                    <input type="text"
                           wire:model.live.debounce.300ms="name"
                           id="name"
                           autocomplete="name"
                           placeholder="Enter your full name"
                           class="block w-full rounded-xl border-2 border-gray-200 px-4 py-3 text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-white @error('name') border-red-500 focus:border-red-500 @enderror">
                    @error('name')
                        <p class="text-red-500 text-sm flex items-center mt-1">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
            <div>
                <label for="email" class="block text-sm font-bold text-gray-900 dark:text-white mb-2">
                    <i class="fas fa-envelope mr-2 text-gray-500"></i>Email Address
                </label>
                <div class="relative">
                    <input type="email"
                           wire:model.live.debounce.300ms="email"
                           id="email"
                           autocomplete="email"
                           placeholder="Enter your email address"
                           class="block w-full rounded-xl border-2 border-gray-200 px-4 py-3 text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-0 transition-all duration-300 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-white @error('email') border-red-500 focus:border-red-500 @enderror">
                    @error('email')
                        <p class="text-red-500 text-sm flex items-center mt-1">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
            <div class="sm:col-span-2">
                <label for="subject" class="block text-sm font-semibold leading-6 text-gray-900 dark:text-white">Subject</label>
                <div class="mt-2.5">
                    <input type="text" wire:model="subject" id="subject" class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-gray-700 dark:text-white dark:ring-gray-600 dark:focus:ring-indigo-500">
                    @error('subject')
                        <p class="text-red-500 text-sm flex items-center mt-1">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
            <div class="sm:col-span-2">
                <label for="message" class="block text-sm font-semibold leading-6 text-gray-900 dark:text-white">Message</label>
                <div class="mt-2.5">
                    <textarea wire:model="message" id="message" rows="4" class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-gray-700 dark:text-white dark:ring-gray-600 dark:focus:ring-indigo-500"></textarea>
                    @error('message')
                        <p class="text-red-500 text-sm flex items-center mt-1">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
        </div>
        <div class="mt-8 flex justify-end">
            <button type="submit"
                    aria-describedby="submit-help"
                    class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 min-h-[44px] touch-manipulation">
                <div wire:loading.remove wire:target="submit">
                    Send Message
                </div>
                <div wire:loading wire:target="submit" aria-live="polite">
                    Sending...
                </div>
            </button>
            <div id="submit-help" class="sr-only">
                Submit the contact form to send your message
            </div>
        </div>
    </form>
</div>
