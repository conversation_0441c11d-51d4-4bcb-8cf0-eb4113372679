<?php

namespace App\Livewire\Admin;

use App\Models\Vendor;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class VendorEditForm extends Component
{
    use WithFileUploads;

    public Vendor $vendor;
    public $roles = [];

    // User fields
    public $name;
    public $email;
    public $password;
    public $password_confirmation;
    public $selectedRole;

    // Vendor fields
    public $shop_name;
    public $business_name;
    public $business_description;
    public $phone;
    public $business_address;
    public $city;
    public $state;
    public $country;
    public $is_featured;
    public $is_approved;
    public $about;
    public $facebook_url;
    public $twitter_url;
    public $instagram_url;
    public $website_url;
    public $banner;

    // UI state
    public $saving = false;

    public function mount(Vendor $vendor, $roles = [])
    {
        $this->vendor = $vendor;
        $this->roles = $roles;

        // Populate user fields
        $this->name = $vendor->user->name;
        $this->email = $vendor->user->email;
        $this->selectedRole = $vendor->user->role_id;

        // Populate vendor fields
        $this->shop_name = $vendor->shop_name;
        $this->business_name = $vendor->business_name ?? '';
        $this->business_description = $vendor->business_description ?? '';
        $this->phone = $vendor->phone ?? '';
        $this->business_address = $vendor->business_address ?? '';
        $this->city = $vendor->city ?? '';
        $this->state = $vendor->state ?? '';
        $this->country = $vendor->country ?? '';
        $this->is_featured = $vendor->is_featured;
        $this->is_approved = $vendor->is_approved;
        $this->about = $vendor->about ?? '';
        $this->facebook_url = $vendor->facebook_url ?? '';
        $this->twitter_url = $vendor->twitter_url ?? '';
        $this->instagram_url = $vendor->instagram_url ?? '';
        $this->website_url = $vendor->website_url ?? '';
    }

    protected function rules()
    {
        $userId = $this->vendor->user->id;
        
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $userId,
            'selectedRole' => 'required|exists:roles,id',
            'shop_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'business_description' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|regex:/^0[789][01]\d{8}$/',
            'business_address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
            'is_approved' => 'boolean',
            'about' => 'nullable|string|max:5000',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'website_url' => 'nullable|url|max:255',
            'banner' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ];

        // Password is optional for updates
        if ($this->password) {
            $rules['password'] = 'string|min:8|confirmed';
        }

        return $rules;
    }

    protected function messages()
    {
        return [
            'name.required' => 'Full name is required.',
            'email.required' => 'Email address is required.',
            'email.unique' => 'This email address is already registered.',
            'selectedRole.required' => 'Please select a role.',
            'shop_name.required' => 'Shop name is required.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
            'facebook_url.url' => 'Please enter a valid Facebook URL.',
            'twitter_url.url' => 'Please enter a valid Twitter URL.',
            'instagram_url.url' => 'Please enter a valid Instagram URL.',
            'website_url.url' => 'Please enter a valid website URL.',
            'banner.image' => 'Banner must be an image file.',
            'banner.max' => 'Banner file size cannot exceed 2MB.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function updatedEmail($value)
    {
        $this->validateOnly('email');
    }

    public function updatedShopName($value)
    {
        $this->validateOnly('shop_name');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            DB::transaction(function () {
                // Update user
                $userData = [
                    'name' => $this->name,
                    'email' => $this->email,
                    'role_id' => $this->selectedRole,
                ];

                if ($this->password) {
                    $userData['password'] = Hash::make($this->password);
                }

                $this->vendor->user->update($userData);

                // Update vendor
                $this->vendor->update([
                    'shop_name' => $this->shop_name,
                    'business_name' => $this->business_name,
                    'business_description' => $this->business_description,
                    'phone' => $this->phone,
                    'business_address' => $this->business_address,
                    'city' => $this->city,
                    'state' => $this->state,
                    'country' => $this->country,
                    'is_featured' => $this->is_featured,
                    'is_approved' => $this->is_approved,
                    'about' => $this->about,
                    'facebook_url' => $this->facebook_url,
                    'twitter_url' => $this->twitter_url,
                    'instagram_url' => $this->instagram_url,
                    'website_url' => $this->website_url,
                ]);

                // Handle banner upload
                if ($this->banner) {
                    $this->vendor->clearMediaCollection('banner');
                    $this->vendor->addMedia($this->banner->getRealPath())
                        ->usingName($this->vendor->shop_name . ' Banner')
                        ->toMediaCollection('banner');
                }
            });

            $this->dispatch('vendor-updated');
            session()->flash('success', 'Vendor updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update vendor: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.admin.vendor-edit-form');
    }
}
