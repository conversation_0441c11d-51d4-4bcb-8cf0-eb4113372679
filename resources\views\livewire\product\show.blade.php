<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

            {{-- Breadcrumbs --}}
            <nav class="mb-4 text-sm text-gray-500" aria-label="Breadcrumb">
                <ol class="list-none p-0 inline-flex">
                    <li class="flex items-center">
                        <a href="{{ route('home') }}" class="hover:text-gray-900">Home</a>
                        <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                    </li>
                    <li class="flex items-center">
                        <a href="{{ route('products.index') }}" class="hover:text-gray-900">Shop</a>
                        <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                    </li>
                    @if($product->category)
                        <li class="flex items-center">
                            <a href="{{ route('products.category', $product->category->slug) }}" class="hover:text-gray-900">{{ $product->category->name }}</a>
                            <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                        </li>
                    @endif
                    <li>
                        <span class="text-gray-400">{{ $product->name }}</span>
                    </li>
                </ol>
            </nav>

            {{-- Product Details Grid --}}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">

                {{-- Product Gallery --}}
                <div class="order-1">
                    <livewire:product.gallery :product="$product" />
                </div>

                {{-- Product Info, Options, and Actions --}}
                <div class="order-2">
                    <livewire:product.options :product="$product" />
                </div>

            </div>

            {{-- Product Tabs: Description, Specs, Reviews --}}
            <div class="mt-12 lg:mt-16">
                <livewire:product.tabs :product="$product" />
            </div>

            {{-- Related Products --}}
            @if($relatedProducts && $relatedProducts->count() > 0)
                <div class="mt-12 sm:mt-16">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900">Related Products</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                        @foreach($relatedProducts as $relatedProduct)
                            <livewire:product-card :product="$relatedProduct" :key="$relatedProduct->id" />
                        @endforeach
                    </div>
                </div>
            @endif

            {{-- Recently Viewed Products --}}
            @if($recentlyViewedProducts->count() > 0)
                <div class="mt-12 sm:mt-16">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900">Recently Viewed</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                        @foreach($recentlyViewedProducts as $recentProduct)
                            <livewire:product-card :product="$recentProduct" :key="$recentProduct->id" />
                        @endforeach
                    </div>
                </div>
            @endif

        </div>
    </div>
</div>
