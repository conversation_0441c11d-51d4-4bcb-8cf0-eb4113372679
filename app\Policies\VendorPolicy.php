<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Auth\Access\HandlesAuthorization;

class VendorPolicy
{
    use HandlesAuthorization;

    /**
     * Perform pre-authorization checks.
     */
    public function before(User $user, $ability)
    {
        if ($user->hasRole('admin')) {
            return true;
        }
    }

    /**
     * Determine whether the user can view the vendor's financial information.
     */
    public function viewFinances(User $user, Vendor $vendor): bool
    {
        return $user->id === $vendor->user_id && $vendor->is_approved;
    }

    /**
     * Determine whether the user can create a withdrawal for the vendor.
     */
    public function createWithdrawal(User $user, Vendor $vendor): bool
    {
        return $user->id === $vendor->user_id && $vendor->is_approved;
    }

    /**
     * Determine whether the user can view the vendor's profile.
     */
    public function viewProfile(User $user, Vendor $vendor): bool
    {
        return $user->id === $vendor->user_id;
    }

    /**
     * Determine whether the user can update the vendor's profile.
     */
    public function updateProfile(User $user, Vendor $vendor): bool
    {
        return $user->id === $vendor->user_id;
    }
}
