<?php

namespace App\Livewire\Admin\Products;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.admin')]
class BestSellerManager extends Component
{
    use WithPagination;

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    // The search string for filtering products.
    public $search = '';

    // Sync the search property with the URL query string.
    protected $queryString = ['search'];

    /**
     * Toggle the is_best_seller status of a product.
     *
     * @param Product $product The product to update.
     */
    public function toggleBestSeller(Product $product)
    {
        // SECURITY FIX: Use direct assignment instead of mass assignment
        // since is_best_seller is in the guarded array for security
        $product->is_best_seller = !$product->is_best_seller;
        $product->save();

        $status = $product->is_best_seller ? 'added to' : 'removed from';
        session()->flash('message', "Product has been {$status} best sellers successfully.");
    }

    /**
     * Render the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        $products = Product::query()
            ->with(['vendor', 'category'])
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhereHas('vendor', function ($query) {
                        $query->where('shop_name', 'like', '%' . $this->search . '%');
                    });
            })
            ->orderBy('is_best_seller', 'desc')
            ->orderBy('name')
            ->paginate(15);

        return view('livewire.admin.products.best-seller-manager', [
            'products' => $products,
        ]);
    }
}
