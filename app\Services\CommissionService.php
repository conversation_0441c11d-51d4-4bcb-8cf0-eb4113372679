<?php

namespace App\Services;

use App\Models\Commission;
use App\Models\Order;
use App\Models\Vendor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Centralized Commission Service
 * 
 * This service handles all commission-related calculations and operations
 * to ensure consistency across the application and prevent discrepancies.
 */
class CommissionService
{
    /**
     * Default commission rate (2.7%)
     */
    private const DEFAULT_COMMISSION_RATE = 0.027;

    /**
     * Calculate commission amount for a given order total
     *
     * @param float $amount Order total amount
     * @param float|null $rate Custom commission rate (optional)
     * @return float Commission amount rounded to 2 decimal places
     */
    public function calculateCommission(float $amount, ?float $rate = null): float
    {
        $commissionRate = $rate ?? $this->getCommissionRate();
        return round($amount * $commissionRate, 2);
    }

    /**
     * Calculate vendor earnings (order total minus commission)
     *
     * @param float $amount Order total amount
     * @param float|null $rate Custom commission rate (optional)
     * @return float Vendor earnings rounded to 2 decimal places
     */
    public function calculateVendorEarnings(float $amount, ?float $rate = null): float
    {
        $commissionRate = $rate ?? $this->getCommissionRate();
        return round($amount * (1 - $commissionRate), 2);
    }

    /**
     * Get the current commission rate
     *
     * @return float Commission rate as decimal (e.g., 0.027 for 2.7%)
     */
    public function getCommissionRate(): float
    {
        return config('brandify.commission_rate', self::DEFAULT_COMMISSION_RATE);
    }

    /**
     * Create a commission record for an order
     *
     * @param Order $order
     * @return Commission|null
     */
    public function createCommissionForOrder(Order $order): ?Commission
    {
        // Prevent duplicate commission creation
        $existingCommission = Commission::where('order_id', $order->id)->first();
        if ($existingCommission) {
            Log::warning('Attempted to create duplicate commission', [
                'order_id' => $order->id,
                'existing_commission_id' => $existingCommission->id,
            ]);
            return null;
        }

        $commissionAmount = $this->calculateCommission($order->total_amount);

        try {
            $commission = DB::transaction(function () use ($order, $commissionAmount) {
                $commission = Commission::create([
                    'order_id' => $order->id,
                    'vendor_id' => $order->vendor_id,
                    'amount' => $commissionAmount,
                    'status' => 'pending',
                ]);

                Log::info('Commission created for order', [
                    'commission_id' => $commission->id,
                    'order_id' => $order->id,
                    'vendor_id' => $order->vendor_id,
                    'amount' => $commissionAmount,
                    'commission_rate' => $this->getCommissionRate(),
                ]);

                return $commission;
            });

            return $commission;
        } catch (\Exception $e) {
            Log::error('Failed to create commission for order', [
                'order_id' => $order->id,
                'vendor_id' => $order->vendor_id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get total commission amount for a vendor
     *
     * @param Vendor $vendor
     * @param string|null $status Filter by commission status
     * @return float Total commission amount
     */
    public function getTotalCommissionForVendor(Vendor $vendor, ?string $status = null): float
    {
        $query = Commission::where('vendor_id', $vendor->id);

        if ($status) {
            $query->where('status', $status);
        }

        return $query->sum('amount');
    }

    /**
     * Get commission amount for a date range
     *
     * @param Vendor $vendor
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string|null $status
     * @return float Commission amount for the date range
     */
    public function getCommissionForDateRange(
        Vendor $vendor,
        Carbon $startDate,
        Carbon $endDate,
        ?string $status = null
    ): float {
        $query = Commission::where('vendor_id', $vendor->id)
            ->whereBetween('created_at', [$startDate, $endDate]);

        if ($status) {
            $query->where('status', $status);
        }

        return $query->sum('amount');
    }

    /**
     * Mark a commission as paid
     *
     * @param Commission $commission
     * @return bool
     */
    public function markCommissionAsPaid(Commission $commission): bool
    {
        try {
            $commission->update([
                'status' => 'paid',
                'paid_at' => now(),
            ]);

            Log::info('Commission marked as paid', [
                'commission_id' => $commission->id,
                'order_id' => $commission->order_id,
                'vendor_id' => $commission->vendor_id,
                'amount' => $commission->amount,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to mark commission as paid', [
                'commission_id' => $commission->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get pending commissions for a vendor
     *
     * @param Vendor $vendor
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPendingCommissionsForVendor(Vendor $vendor)
    {
        return Commission::where('vendor_id', $vendor->id)
            ->where('status', 'pending')
            ->with('order')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get detailed commission breakdown for an order
     *
     * @param Order $order
     * @return array
     */
    public function getCommissionBreakdown(Order $order): array
    {
        $commissionRate = $this->getCommissionRate();
        $commissionAmount = $this->calculateCommission($order->total_amount);
        $vendorEarnings = $this->calculateVendorEarnings($order->total_amount);

        return [
            'order_total' => $order->total_amount,
            'commission_amount' => $commissionAmount,
            'commission_rate' => $commissionRate,
            'commission_percentage' => number_format($commissionRate * 100, 2) . '%',
            'vendor_earnings' => $vendorEarnings,
            'calculation_timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Validate commission calculations for consistency
     *
     * @param float $orderTotal
     * @return array Validation results
     */
    public function validateCommissionCalculation(float $orderTotal): array
    {
        $commission = $this->calculateCommission($orderTotal);
        $vendorEarnings = $this->calculateVendorEarnings($orderTotal);
        $calculatedTotal = $commission + $vendorEarnings;

        $isValid = abs($calculatedTotal - $orderTotal) < 0.01; // Allow for rounding differences

        return [
            'is_valid' => $isValid,
            'order_total' => $orderTotal,
            'commission' => $commission,
            'vendor_earnings' => $vendorEarnings,
            'calculated_total' => $calculatedTotal,
            'difference' => $calculatedTotal - $orderTotal,
            'commission_rate' => $this->getCommissionRate(),
        ];
    }

    /**
     * Bulk create commissions for multiple orders
     *
     * @param array $orders Array of Order models
     * @return array Results of commission creation
     */
    public function bulkCreateCommissions(array $orders): array
    {
        $results = [
            'created' => 0,
            'skipped' => 0,
            'failed' => 0,
            'details' => [],
        ];

        foreach ($orders as $order) {
            try {
                $commission = $this->createCommissionForOrder($order);
                
                if ($commission) {
                    $results['created']++;
                    $results['details'][] = [
                        'order_id' => $order->id,
                        'commission_id' => $commission->id,
                        'status' => 'created',
                    ];
                } else {
                    $results['skipped']++;
                    $results['details'][] = [
                        'order_id' => $order->id,
                        'status' => 'skipped',
                        'reason' => 'Duplicate or validation failed',
                    ];
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['details'][] = [
                    'order_id' => $order->id,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                ];
            }
        }

        Log::info('Bulk commission creation completed', $results);

        return $results;
    }

    /**
     * Get commission statistics for a vendor
     *
     * @param Vendor $vendor
     * @param int $days Number of days to look back
     * @return array Commission statistics
     */
    public function getCommissionStatistics(Vendor $vendor, int $days = 30): array
    {
        $startDate = now()->subDays($days);
        $endDate = now();

        $totalCommission = $this->getCommissionForDateRange($vendor, $startDate, $endDate);
        $paidCommission = $this->getCommissionForDateRange($vendor, $startDate, $endDate, 'paid');
        $pendingCommission = $this->getCommissionForDateRange($vendor, $startDate, $endDate, 'pending');

        $commissionCount = Commission::where('vendor_id', $vendor->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return [
            'period_days' => $days,
            'total_commission' => $totalCommission,
            'paid_commission' => $paidCommission,
            'pending_commission' => $pendingCommission,
            'commission_count' => $commissionCount,
            'average_commission' => $commissionCount > 0 ? round($totalCommission / $commissionCount, 2) : 0,
            'commission_rate' => $this->getCommissionRate(),
            'generated_at' => now()->toISOString(),
        ];
    }
}
