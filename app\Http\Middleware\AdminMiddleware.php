<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        \Log::info('AdminMiddleware Check Started', [
            'user_id' => auth()->id(),
            'is_authenticated' => auth()->check(),
            'is_admin' => auth()->check() ? auth()->user()->isAdmin() : false,
            'route' => $request->route()?->getName(),
            'url' => $request->url(),
            'timestamp' => now()->toISOString()
        ]);

        if (!auth()->check() || !auth()->user()->isAdmin()) {
            \Log::warning('AdminMiddleware Access Denied', [
                'user_id' => auth()->id(),
                'is_authenticated' => auth()->check(),
                'is_admin' => auth()->check() ? auth()->user()->isAdmin() : false,
                'route' => $request->route()?->getName(),
                'url' => $request->url(),
                'ip_address' => $request->ip()
            ]);
            abort(403, 'Unauthorized action.');
        }

        \Log::info('AdminMiddleware Access Granted', [
            'user_id' => auth()->id(),
            'route' => $request->route()?->getName()
        ]);

        return $next($request);
    }
}
