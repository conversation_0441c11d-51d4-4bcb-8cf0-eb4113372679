<?php

namespace App\Console\Commands;

use App\Services\VendorEarningsService;
use Illuminate\Console\Command;

class ProcessPendingBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendor:process-pending-balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending vendor balances and release funds after hold period';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing pending vendor balances...');
        
        $earningsService = new VendorEarningsService();
        $earningsService->processPendingBalances();
        
        $this->info('Pending balances processed successfully.');
        
        return Command::SUCCESS;
    }
}
