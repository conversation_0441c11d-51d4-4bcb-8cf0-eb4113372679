<?php

namespace App\Livewire\Dashboard;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Product;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Index extends Component
{
    public function render()
    {
        $user = Auth::user();

        // Fetch recent orders
        $recentOrders = $user->orders()->latest()->take(5)->get();

        // Fetch recommended products
        $recommendedProducts = Product::where('is_active', true)
            ->inRandomOrder()
            ->take(4)
            ->get();

        // Stats
        $totalOrders = $user->orders()->count();
        $wishlistItems = $user->wishlist()->count();

        return view('livewire.dashboard.index', [
            'recentOrders' => $recentOrders,
            'recommendedProducts' => $recommendedProducts,
            'totalOrders' => $totalOrders,
            'wishlistItems' => $wishlistItems,
        ]);
    }
}
