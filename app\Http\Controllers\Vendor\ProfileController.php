<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    /**
     * Show the vendor profile edit form
     */
    public function edit()
    {
        $vendor = Auth::user()->vendor;

        // SECURITY: Authorize that the user can view their own profile.
        $this->authorize('viewProfile', $vendor);

        $user = Auth::user();

        // Load vendor with relationships
        $vendor->load('user');

        return view('vendor.profile.edit', compact('vendor', 'user'));
    }

}
