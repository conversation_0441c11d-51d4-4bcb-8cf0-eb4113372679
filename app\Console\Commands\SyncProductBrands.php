<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

class SyncProductBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:sync-brands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync products with their vendor brands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting product-brand synchronization...');
        
        $products = Product::with(['vendor', 'vendor.brand'])->get();
        $updated = 0;
        
        foreach ($products as $product) {
            if ($product->vendor && $product->vendor->brand) {
                $oldBrandId = $product->brand_id;
                $newBrandId = $product->vendor->brand->id;
                
                if ($oldBrandId !== $newBrandId) {
                    $product->update(['brand_id' => $newBrandId]);
                    $this->info("Updated product '{$product->name}' brand from {$oldBrandId} to {$newBrandId}");
                    $updated++;
                } else {
                    $this->info("Product '{$product->name}' already has correct brand");
                }
            } else {
                $this->warn("Product '{$product->name}' has no vendor or vendor has no brand");
            }
        }
        
        $this->info("Synchronization complete!");
        $this->info("Updated: {$updated} products");
        
        return 0;
    }
}
