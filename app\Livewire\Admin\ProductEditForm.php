<?php

namespace App\Livewire\Admin;

use App\Models\Product;
use Livewire\Component;
use Illuminate\Support\Str;

class ProductEditForm extends Component
{
    public Product $product;

    // Dropdown data (passed from controller)
    public $vendors = [];
    public $categories = [];
    public $brands = [];

    // Form fields
    public $name;
    public $description;
    public $price;
    public $discount_price;
    public $stock;
    public $vendor_id;
    public $category_id;
    public $brand_id;
    public $is_active;
    public $is_featured;
    public $is_best_seller;
    public $weight;
    public $length;
    public $width;
    public $height;

    // UI state
    public $saving = false;

    public function mount(Product $product, $vendors = [], $categories = [], $brands = [])
    {
        $this->product = $product;

        // Store dropdown data as component properties
        $this->vendors = $vendors;
        $this->categories = $categories;
        $this->brands = $brands;

        // Populate form fields from product
        $this->name = $product->name;
        $this->description = $product->description;
        $this->price = $product->price;
        $this->discount_price = $product->discount_price;
        $this->stock = $product->stock;
        $this->vendor_id = $product->vendor_id;
        $this->category_id = $product->category_id;
        $this->brand_id = $product->brand_id;
        $this->is_active = $product->is_active;
        $this->is_featured = $product->is_featured;
        $this->is_best_seller = $product->is_best_seller;
        $this->weight = $product->weight;
        $this->length = $product->length;
        $this->width = $product->width;
        $this->height = $product->height;
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|min:10',
            'price' => 'required|numeric|min:0|max:999999.99',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock' => 'required|integer|min:0|max:999999',
            'vendor_id' => 'required|exists:vendors,id',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_best_seller' => 'boolean',
            'weight' => 'nullable|numeric|min:0|max:999.99',
            'length' => 'nullable|numeric|min:0|max:999.99',
            'width' => 'nullable|numeric|min:0|max:999.99',
            'height' => 'nullable|numeric|min:0|max:999.99',
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Product name is required.',
            'description.required' => 'Product description is required.',
            'description.min' => 'Description must be at least 10 characters.',
            'price.required' => 'Price is required.',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price cannot be negative.',
            'discount_price.lt' => 'Discount price must be less than the regular price.',
            'stock.required' => 'Stock quantity is required.',
            'stock.integer' => 'Stock must be a whole number.',
            'vendor_id.required' => 'Please select a vendor.',
            'category_id.required' => 'Please select a category.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function updatedPrice($value)
    {
        $this->validateOnly('price');
    }

    public function updatedDiscountPrice($value)
    {
        $this->validateOnly('discount_price');
    }

    public function updatedStock($value)
    {
        $this->validateOnly('stock');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            $this->product->update([
                'name' => $this->name,
                'slug' => Str::slug($this->name),
                'description' => $this->description,
                'price' => $this->price,
                'discount_price' => $this->discount_price,
                'stock' => $this->stock,
                'vendor_id' => $this->vendor_id,
                'category_id' => $this->category_id,
                'brand_id' => $this->brand_id,
                'is_active' => $this->is_active,
                'is_featured' => $this->is_featured,
                'is_best_seller' => $this->is_best_seller,
                'weight' => $this->weight,
                'length' => $this->length,
                'width' => $this->width,
                'height' => $this->height,
            ]);

            $this->dispatch('product-updated');
            session()->flash('success', 'Product updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update product: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.admin.product-edit-form');
    }
}
