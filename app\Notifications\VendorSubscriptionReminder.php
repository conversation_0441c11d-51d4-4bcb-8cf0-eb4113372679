<?php

namespace App\Notifications;

use App\Models\VendorSubscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VendorSubscriptionReminder extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The vendor subscription instance.
     *
     * @var \App\Models\VendorSubscription
     */
    public $subscription;

    /**
     * Create a new notification instance.
     */
    public function __construct(VendorSubscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $planName = $this->subscription->plan->name;
        $expiresAt = $this->subscription->ends_at->format('F j, Y');

        return (new MailMessage)
                    ->subject('Your Brandify Subscription is Expiring Soon')
                    ->greeting('Hello ' . $notifiable->name . ',')
                    ->line("This is a reminder that your subscription to the '{$planName}' plan is set to expire on {$expiresAt}.")
                    ->line('To ensure uninterrupted service and continue selling on Brandify, please renew your subscription before the expiration date.')
                    ->action('Renew Subscription', route('vendor.subscriptions.index'))
                    ->line('Thank you for being a valued member of our community!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'expires_at' => $this->subscription->ends_at->toIso8601String(),
        ];
    }
}
