<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    User Management
                </h1>
                <p class="text-gray-300 text-lg">Manage all users and their permissions</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('admin.users.create') }}"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 flex items-center space-x-2 shadow-lg">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add New User</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Search and Filters --}}
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input wire:model.live.debounce.300ms="search"
                           type="text"
                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-black focus:border-black transition-all duration-200"
                           placeholder="Search users by name or email...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-500">
                    Showing {{ $users->count() }} of {{ $users->total() }} users
                </div>
            </div>
        </div>
    </div>

    {{-- Mobile Card View (Hidden on Desktop) --}}
    <div class="block lg:hidden space-y-4 mb-6">
        @forelse ($users as $user)
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <div class="p-6 space-y-4">
                    <!-- User Header -->
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                {{ strtoupper(substr($user->name, 0, 1)) }}
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 text-lg truncate">{{ $user->name }}</h3>
                            <p class="text-sm text-gray-500 truncate">{{ $user->email }}</p>
                        </div>
                    </div>

                    <!-- User Details -->
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Role:</span>
                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                {{ $user->role->name === 'admin' ? 'bg-red-100 text-red-800' :
                                   ($user->role->name === 'vendor' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800') }}">
                                {{ ucfirst($user->role->name) }}
                            </span>
                        </div>
                        <div>
                            <span class="text-gray-500">Status:</span>
                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                {{ $user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-500">Joined:</span>
                            <span class="ml-2 text-gray-900">{{ $user->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.users.show', $user) }}"
                               class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded hover:bg-blue-200 transition-colors">
                                <i class="fas fa-eye mr-1"></i>
                                View
                            </a>
                            <a href="{{ route('admin.users.edit', $user) }}"
                               class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded hover:bg-gray-200 transition-colors">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </a>
                        </div>
                        <button wire:click="confirmDelete({{ $user->id }})"
                                class="inline-flex items-center px-3 py-1 bg-red-100 text-red-800 text-xs font-medium rounded hover:bg-red-200 transition-colors">
                            <i class="fas fa-trash mr-1"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        @empty
            <div class="text-center py-12">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No users found.</p>
            </div>
        @endforelse
    </div>

    {{-- Desktop Table View (Hidden on Mobile) --}}
    <div class="hidden lg:block bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        {{-- Table Header --}}
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-200">
            <div class="grid grid-cols-12 gap-4 items-center text-sm font-semibold text-gray-600 uppercase tracking-wide">
                <div class="col-span-3 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('name')">
                    <div class="flex items-center space-x-2">
                        <span>Name</span>
                        @if($sortField === 'name')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-3 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('email')">
                    <div class="flex items-center space-x-2">
                        <span>Email</span>
                        @if($sortField === 'email')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-2">Role</div>
                <div class="col-span-2 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('created_at')">
                    <div class="flex items-center space-x-2">
                        <span>Joined</span>
                        @if($sortField === 'created_at')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-2 text-center">Actions</div>
            </div>
        </div>
        
        <!-- Users Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse ($users as $user)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $user->email }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                @if($user->role && is_object($user->role) && isset($user->role->name))
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-200 text-gray-800">
                                        {{ ucfirst($user->role->name) }}
                                    </span>
                                @elseif($user->role_id)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-200 text-yellow-800">
                                        Role ID: {{ $user->role_id }}
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-200 text-red-800">
                                        No Role
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $user->created_at->format('M d, Y') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">View</a>
                                <a href="{{ route('admin.users.edit', $user) }}" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white mr-3">Edit</a>
                                <button wire:click="confirmDelete({{ $user->id }})" class="text-red-600 hover:text-red-900 dark:hover:text-red-400">Delete</button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">
                                No users found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $users->links() }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    @if($deleting)
        <div class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="$set('deleting', false)"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                    <div class="mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Delete User</h3>
                    </div>

                    <div class="text-center">
                        <p class="text-lg text-gray-600 dark:text-gray-300">Are you sure you want to delete this user?</p>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">This action cannot be undone.</p>
                    </div>

                    <!-- Footer -->
                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button wire:click="deleteUser"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:col-start-2 sm:text-sm">
                            Delete
                        </button>
                        <button wire:click="$set('deleting', false)"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
