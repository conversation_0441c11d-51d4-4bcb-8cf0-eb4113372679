<?php

namespace App\Livewire\Admin\Products;

use App\Livewire\Shared\ProductForm as BaseProductForm;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class CreateProduct extends BaseProductForm
{
    // CRITICAL FIX: Individual properties for proper Livewire binding (legacy model binding disabled)
    public $name = '';
    public $description = '';
    public $price;
    public $discount_price;
    public $stock;
    public $category_id;
    public $is_active = true;
    public $weight;
    public $length;
    public $width;
    public $height;

    // Additional properties for admin-specific fields
    public string $slug = '';
    public ?int $vendor_id = null;
    public bool $is_featured = false;
    public ?int $brand_id = null;
    public ?string $sku = '';

    // Additional data
    public array $vendors = [];
    public array $categories = [];
    public array $brands = [];

    public function mount(): void
    {
        $this->product = new Product();
        $this->product->is_active = true;

        // CRITICAL FIX: Initialize product properties to prevent binding errors
        $this->product->name = '';
        $this->product->description = '';
        $this->product->price = 0;
        $this->product->discount_price = null;
        $this->product->stock = 0;
        $this->product->category_id = null;

        // Initialize with one empty variant and specification
        $this->addVariant();
        $this->addSpecification();

        // Load data for dropdowns
        $this->vendors = Vendor::orderBy('shop_name')->get(['id', 'shop_name'])->toArray();
        $this->categories = Category::orderBy('name')->get(['id', 'name'])->toArray();
        $this->brands = Brand::orderBy('name')->get(['id', 'name'])->toArray();
    }

    /**
     * Get the validation rules for the product form
     */
    public function rules(): array
    {
        return array_merge(parent::getProductRules(), [
            'slug' => 'required|string|max:255|unique:products,slug',
            'vendor_id' => 'required|exists:vendors,id',
            'is_featured' => 'boolean',
            'brand_id' => 'nullable|exists:brands,id',
            'sku' => 'nullable|string|max:255',
            'variants.*.name' => 'required_with:variants.*.value|string|max:255',
            'variants.*.value' => 'required_with:variants.*.name|string|max:255',
            'variants.*.price' => 'nullable|numeric|min:0',
            'specifications.*.name' => 'required_with:specifications.*.value|string|max:255',
            'specifications.*.value' => 'required_with:specifications.*.name|string|max:255',
        ]);
    }

    /**
     * When the product name changes, update the slug
     */
    public function updatedProductName($value)
    {
        $this->slug = Str::slug($value);
    }

    /**
     * CRITICAL FIX: Handle product property updates for proper binding
     */
    public function updatedProduct($value, $key)
    {
        if ($key === 'name') {
            $this->slug = Str::slug($value);
        }
    }

    /**
     * When vendor is selected, auto-set the brand
     */
    public function updatedVendorId($vendorId)
    {
        $vendor = Vendor::find($vendorId);
        if ($vendor) {
            $brand = Brand::firstOrCreate(
                ['name' => $vendor->shop_name], 
                ['slug' => Str::slug($vendor->shop_name)]
            );
            $this->brand_id = $brand->id;
        }
    }

    // Variant and specification methods are now in the ManagesVariants and ManagesSpecifications traits

    /**
     * Save the product and related data
     */
    /**
     * CRITICAL FIX: Populate product model from individual properties
     */
    private function populateProductFromProperties()
    {
        $this->product->name = $this->name;
        $this->product->description = $this->description;
        $this->product->price = $this->price;
        $this->product->discount_price = $this->discount_price;
        $this->product->stock = $this->stock;
        $this->product->category_id = $this->category_id;
        $this->product->is_active = $this->is_active;
        $this->product->weight = $this->weight;
        $this->product->length = $this->length;
        $this->product->width = $this->width;
        $this->product->height = $this->height;
    }

    public function save()
    {
        $this->validate();

        // CRITICAL FIX: Populate product model from individual properties
        $this->populateProductFromProperties();

        // Set additional product properties
        $this->product->slug = $this->slug;
        $this->product->vendor_id = $this->vendor_id;
        $this->product->is_featured = $this->is_featured;
        $this->product->brand_id = $this->brand_id;
        $this->product->sku = $this->sku;

        // Save the product and handle media
        $this->saveProduct();

        // Save variants and specifications
        $this->saveVariants($this->product);
        $this->saveSpecifications($this->product);

        session()->flash('success', 'Product created successfully.');
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.admin.products.create-product');
    }
}