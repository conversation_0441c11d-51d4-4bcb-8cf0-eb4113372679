<?php

namespace App\Livewire\Admin\Vendors;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Livewire\WithFileUploads;

class VendorForm extends Component
{
    use WithFileUploads;
    public ?Vendor $vendor = null;
    public ?User $user = null;

    // User fields
    public string $name = '';
    public string $email = '';
    public ?string $password = null;
    public ?string $password_confirmation = null;

    // Vendor fields
    public string $shop_name = '';
    public string $business_name = '';
    public string $business_description = '';
    public string $phone = '';
    public string $business_address = '';
    public string $city = '';
    public string $state = '';
    public string $country = '';
    public bool $is_featured = false;
    public bool $is_approved = false;

    // Storefront fields
    public $banner = null;
    public string $about = '';
    public string $facebook_url = '';
    public string $twitter_url = '';
    public string $instagram_url = '';

    public function mount(?Vendor $vendor = null): void
    {
        $this->vendor = $vendor;
        if ($this->vendor && $this->vendor->exists) {
            $this->user = $this->vendor->user;
            $this->name = $this->user->name ?? '';
            $this->email = $this->user->email ?? '';
            $this->shop_name = $this->vendor->shop_name ?? '';
            $this->business_name = $this->vendor->business_name ?? '';
            $this->business_description = $this->vendor->business_description ?? '';
            $this->phone = $this->vendor->phone ?? '';
            $this->business_address = $this->vendor->business_address ?? '';
            $this->city = $this->vendor->city ?? '';
            $this->state = $this->vendor->state ?? '';
            $this->country = $this->vendor->country ?? '';
            $this->is_featured = $this->vendor->is_featured ?? false;
            $this->is_approved = $this->vendor->is_approved ?? false;
            $this->about = $this->vendor->about ?? '';
            $this->facebook_url = $this->vendor->facebook_url ?? '';
            $this->twitter_url = $this->vendor->twitter_url ?? '';
            $this->instagram_url = $this->vendor->instagram_url ?? '';
        }
    }

    protected function rules(): array
    {
        $userId = $this->user?->id ?? 'NULL';
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $userId,
            'shop_name' => 'required|string|max:255',
            'business_name' => 'nullable|string|max:255',
            'business_description' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'business_address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'is_featured' => 'boolean',
            'is_approved' => 'boolean',
            'about' => 'nullable|string|max:5000',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'banner' => 'nullable|image|mimes:jpg,jpeg,png|max:2048', // 2MB Max
        ];

        if (!$this->vendor || !$this->vendor->exists) {
            $rules['password'] = 'required|string|min:8|confirmed';
        } else {
            $rules['password'] = 'nullable|string|min:8|confirmed';
        }

        return $rules;
    }

    public function save()
    {
        $this->validate();

        DB::transaction(function () {
            if ($this->vendor && $this->vendor->exists) {
                // Update existing user and vendor
                $this->user->update([
                    'name' => $this->name,
                    'email' => $this->email,
                ]);

                if ($this->password) {
                    $this->user->update(['password' => Hash::make($this->password)]);
                }

                $this->vendor->update([
                    'shop_name' => $this->shop_name,
                    'business_name' => $this->business_name,
                    'business_description' => $this->business_description,
                    'phone' => $this->phone,
                    'business_address' => $this->business_address,
                    'city' => $this->city,
                    'state' => $this->state,
                    'country' => $this->country,
                    'is_featured' => $this->is_featured,
                    'is_approved' => $this->is_approved,
                    'about' => $this->about,
                    'facebook_url' => $this->facebook_url,
                    'twitter_url' => $this->twitter_url,
                    'instagram_url' => $this->instagram_url,
                ]);

                if ($this->banner) {
                    $this->vendor->addMedia($this->banner->getRealPath())->toMediaCollection('banner');
                }

                session()->flash('success', 'Vendor updated successfully.');
            } else {
                // Create new user and vendor
                $newUser = User::create([
                    'name' => $this->name,
                    'email' => $this->email,
                    'password' => Hash::make($this->password),
                    'role_id' => 2, // Vendor role
                ]);

                $newUser->vendor()->create([
                    'shop_name' => $this->shop_name,
                    'slug' => \Illuminate\Support\Str::slug($this->shop_name),
                    'business_name' => $this->business_name,
                    'business_description' => $this->business_description,
                    'phone' => $this->phone,
                    'business_address' => $this->business_address,
                    'city' => $this->city,
                    'state' => $this->state,
                    'country' => $this->country,
                    'is_featured' => $this->is_featured,
                    'is_approved' => $this->is_approved,
                    'about' => $this->about,
                    'facebook_url' => $this->facebook_url,
                    'twitter_url' => $this->twitter_url,
                    'instagram_url' => $this->instagram_url,
                ]);

                if ($this->banner) {
                    $newUser->vendor->addMedia($this->banner->getRealPath())->toMediaCollection('banner');
                }

                session()->flash('success', 'Vendor created successfully.');
            }
        });

        return redirect()->route('admin.vendors.index');
    }

    public function render()
    {
        return view('livewire.admin.vendors.vendor-form')->layout('layouts.admin');
    }
}
