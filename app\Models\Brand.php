<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Brand extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;
    
    protected $fillable = [
        'vendor_id',
        'name',
        'slug',
        'description',
        'logo',
        'is_active',
        'is_featured',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    // Automatically set slug when name is set
    public static function boot()
    {
        parent::boot();
        static::saving(function ($brand) {
            if (empty($brand->slug) && !empty($brand->name)) {
                $brand->slug = Str::slug($brand->name);
            }
        });
    }

    // Scope for only active brands
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    // Scope for featured brands
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
    
    /**
     * Get the vendor associated with this brand.
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the products associated with this brand.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Register media collections for the brand.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();
    }

    /**
     * Register media conversions for the brand.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('logo');
    }

    /**
     * Get the brand's logo URL.
     * FIXED: Updated to use Spatie Media Library with fallback to database field
     *
     * @return string
     */
    public function getLogoUrlAttribute(): string
    {
        // First try to get from media library
        $media = $this->getFirstMedia('logo');
        if ($media) {
            return $media->getUrl();
        }

        // Fallback to database logo if exists
        $logoValue = $this->attributes['logo'] ?? null;
        if ($logoValue && Storage::disk('public')->exists($logoValue)) {
            return Storage::url($logoValue);
        }

        // Final fallback
        return asset('images/default-brand.png');
    }
}
