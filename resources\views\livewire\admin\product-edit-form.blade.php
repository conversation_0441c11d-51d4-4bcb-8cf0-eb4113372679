<form wire:submit.prevent="save" class="space-y-6">
    <!-- Basic Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Name -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Product Name <span class="text-red-500">*</span>
            </label>
            <input type="text" 
                   id="name" 
                   wire:model.blur="name"
                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                   placeholder="Enter product name" 
                   required>
            @error('name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>

        <!-- Stock -->
        <div>
            <label for="stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Stock Quantity <span class="text-red-500">*</span>
            </label>
            <input type="number" 
                   id="stock" 
                   wire:model.blur="stock" 
                   min="0"
                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                   placeholder="0" 
                   required>
            @error('stock') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
    </div>

    <!-- Description -->
    <div>
        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description <span class="text-red-500">*</span>
        </label>
        <textarea id="description" 
                  wire:model.defer="description" 
                  rows="4"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Enter product description" 
                  required></textarea>
        @error('description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
    </div>

    <!-- Pricing -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Price -->
        <div>
            <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Price <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span class="text-gray-500 sm:text-sm">₦</span>
                </div>
                <input type="number" 
                       id="price" 
                       wire:model.blur="price" 
                       step="0.01" 
                       min="0"
                       class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                       placeholder="0.00" 
                       required>
            </div>
            @error('price') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>

        <!-- Discount Price -->
        <div>
            <label for="discount_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Discount Price
            </label>
            <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span class="text-gray-500 sm:text-sm">₦</span>
                </div>
                <input type="number" 
                       id="discount_price" 
                       wire:model.blur="discount_price" 
                       step="0.01" 
                       min="0"
                       class="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                       placeholder="0.00">
            </div>
            @error('discount_price') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
    </div>

    <!-- Dropdowns -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Vendor -->
        <div>
            <label for="vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vendor <span class="text-red-500">*</span>
            </label>
            <select id="vendor_id" 
                    wire:model="vendor_id"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <option value="">Select a vendor</option>
                @foreach($vendors as $vendor)
                    <option value="{{ $vendor->id }}">{{ $vendor->shop_name }}</option>
                @endforeach
            </select>
            @error('vendor_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>

        <!-- Category -->
        <div>
            <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category <span class="text-red-500">*</span>
            </label>
            <select id="category_id" 
                    wire:model="category_id"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <option value="">Select a category</option>
                @foreach($categories as $category)
                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                @endforeach
            </select>
            @error('category_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>

        <!-- Brand -->
        <div>
            <label for="brand_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Brand
            </label>
            <select id="brand_id" 
                    wire:model="brand_id"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <option value="">Select a brand</option>
                @foreach($brands as $brand)
                    <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                @endforeach
            </select>
            @error('brand_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
    </div>

    <!-- Dimensions -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div>
            <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Weight (kg)</label>
            <input type="number" id="weight" wire:model.defer="weight" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="0.00">
            @error('weight') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
        <div>
            <label for="length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Length (cm)</label>
            <input type="number" id="length" wire:model.defer="length" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="0.00">
            @error('length') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
        <div>
            <label for="width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Width (cm)</label>
            <input type="number" id="width" wire:model.defer="width" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="0.00">
            @error('width') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
        <div>
            <label for="height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Height (cm)</label>
            <input type="number" id="height" wire:model.defer="height" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="0.00">
            @error('height') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
        </div>
    </div>

    <!-- Status Toggles -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="flex items-center">
            <input type="checkbox" id="is_active" wire:model="is_active" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-white">Active</label>
        </div>
        <div class="flex items-center">
            <input type="checkbox" id="is_featured" wire:model="is_featured" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
            <label for="is_featured" class="ml-2 block text-sm text-gray-900 dark:text-white">Featured</label>
        </div>
        <div class="flex items-center">
            <input type="checkbox" id="is_best_seller" wire:model="is_best_seller" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
            <label for="is_best_seller" class="ml-2 block text-sm text-gray-900 dark:text-white">Best Seller</label>
        </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
        <button type="submit" 
                wire:loading.attr="disabled"
                wire:target="save"
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <span wire:loading.remove wire:target="save">Update Product</span>
            <span wire:loading wire:target="save" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
            </span>
        </button>
    </div>
</form>
