/**
 * CRITICAL FIX JS4: Unified Cart Manager Module
 * Consolidates all cart functionality into a single, reliable module
 * Replaces multiple conflicting cart interaction files
 */

// Export the CartManager for use in other modules
export const CartManager = {
    init: function() {
        this.bindAddToCartForms();
        this.bindQuantityUpdates();
        this.bindRemoveButtons();
    },

    bindAddToCartForms: function() {
        document.addEventListener('submit', function(event) {
            if (event.target.matches('.ajax-add-to-cart-form')) {
                event.preventDefault();
                CartManager.handleAddToCart(event.target);
            }
        });
    },

    handleAddToCart: function(form) {
        const button = form.querySelector('.add-to-cart-btn');
        const originalButtonContent = button.innerHTML;
        const cartCountIndicator = document.querySelector('.cart-count-indicator');

        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

        const formData = new FormData(form);
        const url = form.action;

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': formData.get('_token')
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => { throw new Error(err.message || 'An unknown error occurred.'); });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.showToast(data.message, 'success');
                if (cartCountIndicator && data.cart && typeof data.cart.count !== 'undefined') {
                    cartCountIndicator.textContent = data.cart.count;
                    cartCountIndicator.classList.remove('d-none');
                }
                button.innerHTML = '<i class="fas fa-check"></i>';
            } else {
                this.showToast(data.message || 'Could not add product to cart.', 'error');
            }
        })
        .catch(error => {
            this.showToast(error.message, 'error');
        })
        .finally(() => {
            setTimeout(() => {
                button.innerHTML = originalButtonContent;
                button.disabled = false;
            }, 1500);
        });
    },

    bindQuantityUpdates: function() {
        document.addEventListener('change', function(event) {
            if (event.target.matches('.quantity-input')) {
                const form = event.target.closest('.cart-update-form');
                if (form) {
                    CartManager.handleQuantityUpdate(form);
                }
            }
        });
    },

    handleQuantityUpdate: function(form) {
        const formData = new FormData(form);
        const itemId = form.dataset.itemId;

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update cart totals if provided
                if (data.cart) {
                    this.updateCartDisplay(data.cart);
                }
                this.showToast('Cart updated successfully', 'success');
            } else {
                this.showToast(data.message || 'Failed to update cart', 'error');
            }
        })
        .catch(error => {
            this.showToast('Error updating cart', 'error');
        });
    },

    bindRemoveButtons: function() {
        document.addEventListener('click', function(event) {
            if (event.target.matches('.remove-from-cart')) {
                event.preventDefault();
                CartManager.handleRemoveItem(event.target);
            }
        });
    },

    handleRemoveItem: function(button) {
        const url = button.href || button.dataset.url;
        
        fetch(url, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the item row
                const itemRow = button.closest('.cart-item');
                if (itemRow) {
                    itemRow.remove();
                }
                
                // Update cart display
                if (data.cart) {
                    this.updateCartDisplay(data.cart);
                }
                
                this.showToast('Item removed from cart', 'success');
            } else {
                this.showToast(data.message || 'Failed to remove item', 'error');
            }
        })
        .catch(error => {
            this.showToast('Error removing item', 'error');
        });
    },

    updateCartDisplay: function(cartData) {
        // Update cart count
        const cartCountIndicator = document.querySelector('.cart-count-indicator');
        if (cartCountIndicator && cartData.count !== undefined) {
            cartCountIndicator.textContent = cartData.count;
            if (cartData.count > 0) {
                cartCountIndicator.classList.remove('d-none');
            } else {
                cartCountIndicator.classList.add('d-none');
            }
        }

        // Update cart totals
        const subtotalElement = document.querySelector('.cart-subtotal');
        if (subtotalElement && cartData.subtotal !== undefined) {
            subtotalElement.textContent = cartData.subtotal;
        }

        const totalElement = document.querySelector('.cart-total');
        if (totalElement && cartData.total !== undefined) {
            totalElement.textContent = cartData.total;
        }
    },

    showToast: function(message, type = 'info') {
        // Try to use existing toast function if available
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // Fallback toast implementation
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    CartManager.init();
});

// Make available globally for backward compatibility
window.CartManager = CartManager;
