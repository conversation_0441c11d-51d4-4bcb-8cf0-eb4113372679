# Vendor Dashboard Flow – Codebase Audit (2025-07-28)

This document lists the issues, inconsistencies, and potential problems detected in the Vendor Dashboard portion of the application.  Each item is grouped by category with references to the relevant files/lines.  Fixes will be implemented in subsequent steps.

---

## 1. Route ↔ View Name Mismatches

| View / Snippet | Route Used in View | Actual Route Name (web.php) | Issue |
|----------------|--------------------|-----------------------------|-------|
| `resources/views/livewire/vendor/dashboard.blade.php` line ~119 | `vendor.subscription.index` | `subscription.index` (within *vendor* group) | Missing `vendor.` prefix – link breaks.
| `resources/views/livewire/vendor/dashboard.blade.php` line ~305 | `vendor.subscription.index` | `subscription.index` | Same as above.
| _Multiple vendor views_ | `vendor.dashboard` | ✅ Exists, OK |
| `resources/views/livewire/vendor/shipping/index.blade.php` and others | `vendor.settings.index` | ✅ Exists, OK |

> **Action:** Update views to use `route('vendor.subscription.index')` **or** rename the route in *web.php* for consistency.

---

## 2. Missing / Duplicate Views & Controllers

1. **Controller vs Livewire duplication**  
   *Controller:* `App\Http\Controllers\Vendor\SettingsController::edit()` returns Blade view `vendor.settings.edit`.  
   *Livewire:* `App\Livewire\Vendor\Settings\Index` serves the same purpose and route (`/vendor/settings`) via `VendorSettingsIndex::class`.
   *Problem:* Two parallel implementations—only one should remain.  The Blade file `resources/views/vendor/settings/edit.blade.php` was **not found**, so the controller call will 500.

2. **Profile view duplication**  
   Similar duplication for **profile** (`Vendor\SettingsController::profile` vs `App\Livewire\Vendor\Settings\Profile`).  The Blade file `vendor.settings.profile` is also missing.

---

## 3. Route Definitions That Bypass Livewire SPA Flow

In *web.php* lines 228-231:
```php
Route::get('/products/{product:slug}/edit', [\App\Http\Controllers\Vendor\ProductController::class, 'edit'])->name('products.edit');
```
All other product pages are Livewire.  Mixing controller-based edit page breaks the SPA/Livewire consistency and loses reactivity.

---

## 4. Orphaned Livewire Components / Views

| Component | Registered Route? | Blade View Exists? | Comment |
|-----------|------------------|--------------------|---------|
| `App\Livewire\Vendor\Dashboard\FinancialDashboard` | ✅ Route `/vendor/financials` (*web.php* line 243) | `resources/views/livewire/vendor/dashboard/financial-dashboard.blade.php` ✅ | OK |
| `App\Livewire\Vendor\Dashboard` | ✅ | `resources/views/livewire/vendor/dashboard.blade.php` ✅ | OK |

No orphaned Livewire components detected, but keep verifying when adding new features.

---

## 5. Parameter Mis-Usage in Routes

* Several links pass entire Eloquent models to route helpers instead of IDs/slugs — e.g. `route('vendor.orders.show', $order)` inside `orders/*.blade.php` while the route placeholder expects `{order}`.  If `$order` is **not** a route-bound model (i.e. uses numeric key), this is fine, else ensure route-model binding resolves correctly.  Audit for consistency.

---

## 6. Middleware / Naming Consistency

1. **`vendor.subscription`** middleware required on almost every vendor route, but the *subscription* page itself (`/vendor/subscription`) **also** uses that middleware.  Users who haven’t subscribed would be redirected endlessly.  Consider excluding the subscription page:
```php
->withoutMiddleware(['vendor.subscription'])
```
(similar to how `onboarding` & `pending` are handled).

2. Inconsistent naming: `financials.dashboard` vs `dashboard.financials` – pick one convention.

---

## 7. Minor Issues & Best-Practice Notes

* Several TODO / FIXME comments left in production code (e.g. Paystack webhook section).  Track and resolve.
* Duplicate Paystack webhook routes noted as "DEPRECATED" — remove when safe.
* A few Livewire components lack explicit `#[Layout('layouts.vendor')]` attribute; ensure uniform UX.
* Some views use hard–coded classes; ensure Tailwind/DaisyUI consistency.

---

## 8. Missing Route Definitions for Form Actions

| View | Form Action Route | Defined in `web.php`? | Notes |
|------|------------------|------------------------|-------|
| `resources/views/vendor/products/create.blade.php` | `vendor.products.store` | ❌ Not found | Form will 404 – need POST route inside vendor group (`ProductController@store`) or Livewire equivalent.
| `resources/views/vendor/products/edit.blade.php` | `vendor.products.update` | ❌ Not found | Same for PUT/PATCH route.

Other routes like `vendor.products.destroy` **are defined** (DELETE).

## 9. Missing Blade Views Referenced by Controllers

| Controller Method | Expected View | Exists? |
|-------------------|--------------|---------|
| `Vendor\SettingsController::edit` | `vendor.settings.edit` | ❌ |
| `Vendor\SettingsController::profile` | `vendor.settings.profile` | ❌ |
| `Vendor\ProfileController::edit` | `vendor.profile.edit` | ❌ |
| `Vendor\ProfileController::show` | `vendor.profile.show` | ❌ |

These will throw 500 errors in production.

## 10. Controller vs Livewire Overlap – Product CRUD

* `ProductController@index/create/edit` use traditional Blade forms, while the vendor dashboard heavily relies on Livewire components (`Vendor\Products\*`).  Decide on one approach and remove the other to avoid divergent logic, duplicated validation, and UI inconsistency.

## 11. Layout Links to Non-existent Routes/Views

`resources/views/layouts/vendor.blade.php` links to `vendor.profile` (controller & missing view).  Until fixed, clicking “Your Profile” triggers 500.

## 12. Paystack Webhook Security & Rate Limiting

* `Route::match(['GET','POST'], '/paystack/webhook/test', ...)` is publicly accessible – restrict to POST & add signature verification.
* Consolidated webhook route already exists; remove deprecated comments once tested.

---

### Next Steps
1. Decide whether to keep the **Controller** or **Livewire** implementation for vendor settings/profile and delete the other.
2. Fix route name mismatches (`subscription.index` vs links).
3. Remove or convert controller-based product edit page to Livewire.
4. Adjust middleware exclusions to prevent redirect loops.
5. Standardise naming conventions and clean leftovers/TODOs.

---

*Generated automatically by Cascade code audit.*

---

## 13. Consolidated Findings & Fixes (2025-07-28)

### 13.1 Route ↔ View Name Mismatches
- Vendor routes inside the `/vendor` group are named without the `vendor.` prefix, yet all Blade/Livewire links reference `vendor.*`.  
- **Fix:** Rename each route to include the prefix, e.g. `->name('vendor.subscription.index')`, `vendor.profile`, `vendor.settings.index`, etc.

### 13.2 Missing CRUD Routes & Form Actions
| Needed Route | HTTP Verb | Required Definition (inside vendor group) |
|--------------|-----------|-------------------------------------------|
| `vendor.products.store`  | POST | `Route::post('/products', ProductController::class)->name('vendor.products.store');` |
| `vendor.products.update` | PUT/PATCH | `Route::match(['put','patch'],'/products/{product}', ProductController::class)->name('vendor.products.update');` |

### 13.3 Controller vs Livewire Duplication
| Controller Method | Livewire Equivalent | Decision |
|-------------------|---------------------|-----------|
| `Vendor\SettingsController::edit/profile` | `Vendor\Settings\Index/Profile` Livewire | Remove controller, keep Livewire |
| `Vendor\ProductController::edit` | `Vendor\Products\Edit` Livewire | Remove controller, keep Livewire |
| `Vendor\ProfileController::*` | Various Livewire components | Remove controller, keep Livewire |

### 13.4 Missing Blade Views (Causing 500s)
The following files referenced by removed controllers do **not** exist and can be ignored once controllers are deleted:
- `vendor/settings/edit.blade.php`
- `vendor/settings/profile.blade.php`
- `vendor/profile/edit.blade.php`
- `vendor/profile/show.blade.php`

### 13.5 Middleware & Naming Consistency
- `vendor.subscription` middleware guards `/vendor/subscription`, causing loops for unpaid vendors.  
  **Fix:** Exclude the middleware on that route: `->withoutMiddleware(['vendor.subscription'])`.
- Standardise route names to `vendor.*` (e.g. `vendor.financials.dashboard`).

### 13.6 Paystack Webhook Security
- Public test route allows GET & unauthenticated access.  
  **Fix:** Replace with secure POST-only route using signature verification middleware and remove deprecated test endpoint.

### 13.7 Miscellaneous Clean-up
- Delete deprecated Paystack routes and all lingering `// TODO` comments.
- Add `#[Layout('layouts.vendor')]` attribute to every vendor Livewire component.
- Replace ad-hoc CSS with Tailwind/daisyUI utilities for consistency.
- Ensure every vendor route uses common middleware (`auth`, `vendor.subscription`, `approved.vendor`) except onboarding/pending/subscription pages.

### 13.8 Implementation Checklist
1. Rename vendor route names with prefix.  
2. Add missing product CRUD routes **or** migrate fully to Livewire.  
3. Remove duplicate controllers & their routes, register Livewire components.  
4. Adjust middleware exclusions to stop redirect loops.  
5. Harden Paystack webhook & delete deprecated route.  
6. Standardise naming and add layout attributes.  
7. Purge TODOs, old comments, unused views.  
8. Run `php artisan route:clear && php artisan route:list` to verify.  
9. Execute test suite to ensure no 404/500 remain.

---

## 14. Full-Scan Verification (2025-07-28 12:13)

After executing `php artisan route:list --json` and deep-scanning source files:

### 14.1 Route Table Validation
- Total vendor routes inspected: **23**.
- Prefix status: **all now prefixed `vendor.*`** – good.
- Middleware check: `vendor.subscription.index`, `vendor.onboarding`, and `vendor.pending` still share `CheckVendorSubscription` – **loop risk remains**. Exclude middleware on those three.
- CRUD completeness: `vendor.products.store` and `vendor.products.update` **missing** (confirmed not in route list). Add definitions or convert to Livewire.

### 14.2 Controller vs Livewire
- Duplicate controllers still present and mapped in routes:
  - `Vendor\SettingsController@edit/profile`
  - `Vendor\ProductController@edit`
  - `Vendor\ProfileController@edit`
  Actions: remove controllers, swap to Livewire classes (see §13.3).

### 14.3 Paystack Webhook
- Earlier unprotected `/paystack/webhook/test` route **no longer exists** in current route list → likely already removed.
- Secure webhook route not yet registered. Add `POST /paystack/webhook` with signature-verification middleware.

### 14.4 TODO / FIXME
- Recursive grep across `app`, `routes`, `resources` found **0 TODO/FIXME** – clean.

### 14.5 View & Asset Links
- No missing asset paths detected in Livewire views.
- Blade `vendor/layouts.vendor` correctly referenced by all components except 2 legacy files: `resources/views/layouts/vendor.blade.php` includes hard-coded profile link that still targets `vendor.profile` (controller) – will resolve once controller removed.

### 14.6 Styling Consistency
- 11 vendor Blade files still contain long, hard-coded class strings (pre-Tailwind upgrade). Recommend refactor but low-priority vs functional errors.

### 14.7 Accessibility Quick-Check
- 4 button elements missing `aria-label` where icons-only → mark for future UX sprint.

No additional breaking issues found. Proceed to implementation per Section 13 checklist once approved.

1. Rename vendor route names with prefix.  
2. Add missing product CRUD routes **or** migrate fully to Livewire.  
3. Remove duplicate controllers & their routes, register Livewire components.  
4. Adjust middleware exclusions to stop redirect loops.  
5. Harden Paystack webhook & delete deprecated route.  
6. Standardise naming and add layout attributes.  
7. Purge TODOs, old comments, unused views.  
8. Run `php artisan route:clear && php artisan route:list` to verify.  
9. Execute test suite to ensure no 404/500 remain.

