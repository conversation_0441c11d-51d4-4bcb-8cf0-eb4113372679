<?php

namespace App\Livewire\Admin;

use App\Models\Category;
use Livewire\Component;
use Illuminate\Support\Str;

class CategoryEditForm extends Component
{
    public Category $category;
    public $parentCategories = [];

    // Form fields
    public $name;
    public $slug;
    public $parent_id;
    public $is_active;

    // UI state
    public $saving = false;

    public function mount(Category $category, $parentCategories = [])
    {
        $this->category = $category;
        $this->parentCategories = $parentCategories;

        // Populate form fields
        $this->name = $category->name;
        $this->slug = $category->slug;
        $this->parent_id = $category->parent_id;
        $this->is_active = $category->is_active;
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:categories,slug,' . $this->category->id,
            'parent_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Category name is required.',
            'name.max' => 'Category name cannot exceed 255 characters.',
            'slug.required' => 'Category slug is required.',
            'slug.unique' => 'This slug is already taken.',
            'parent_id.exists' => 'Selected parent category is invalid.',
        ];
    }

    public function updatedName($value)
    {
        $this->slug = Str::slug($value);
        $this->validateOnly('name');
    }

    public function updatedSlug($value)
    {
        $this->validateOnly('slug');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            $this->category->update([
                'name' => $this->name,
                'slug' => $this->slug,
                'parent_id' => $this->parent_id,
                'is_active' => $this->is_active,
            ]);

            $this->dispatch('category-updated');
            session()->flash('success', 'Category updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update category: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.admin.category-edit-form');
    }
}
