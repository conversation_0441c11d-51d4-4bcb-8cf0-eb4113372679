<?php

namespace App\Livewire\Admin\Brands;

use App\Models\Brand;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithFileUploads, WithPagination;

    public ?Brand $brand = null;
    public $logo;
    public bool $showModal = false;

    public string $name = '';
    public string $description = '';
    public bool $is_active = true;
    public bool $is_featured = false;

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:brands,name,' . ($this->brand?->id ?? ''),
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ];
    }

    public function create()
    {
        $this->resetValidation();
        $this->reset('name', 'description', 'logo', 'is_active', 'is_featured', 'brand');
        $this->brand = new Brand();
        $this->showModal = true;
    }

    public function edit(Brand $brand)
    {
        $this->resetValidation();
        $this->brand = $brand;
        $this->name = $brand->name;
        $this->description = $brand->description;
        $this->is_active = $brand->is_active;
        $this->is_featured = $brand->is_featured;
        $this->logo = null;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'description' => $this->description,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
        ];

        // Separate create and update logic
        if ($this->brand && $this->brand->exists) {
            $this->brand->update($data);

            // FIXED: Use Spatie Media Library for consistent file handling
            if ($this->logo) {
                $this->brand->clearMediaCollection('logo');
                $this->brand->addMedia($this->logo->getRealPath())
                    ->usingName($this->brand->name . ' Logo')
                    ->usingFileName($this->logo->getClientOriginalName())
                    ->toMediaCollection('logo');
            }

            session()->flash('success', 'Brand updated successfully.');
        } else {
            $newBrand = Brand::create($data);

            // FIXED: Use Spatie Media Library for logo upload
            if ($this->logo) {
                $newBrand->addMedia($this->logo->getRealPath())
                    ->usingName($newBrand->name . ' Logo')
                    ->usingFileName($this->logo->getClientOriginalName())
                    ->toMediaCollection('logo');
            }

            $this->brand = $newBrand; // Update the component's brand instance
            session()->flash('success', 'Brand created successfully.');
        }

        $this->showModal = false;
        $this->reset('name', 'description', 'logo', 'is_active', 'is_featured');
    }

    public function delete(Brand $brand)
    {
        if ($brand->products()->count() > 0) {
            session()->flash('error', 'Cannot delete brand with associated products.');
            return;
        }

        // LOGIC FIX: Use Media Library for consistent file deletion
        $brand->clearMediaCollection('logo');

        $brand->delete();
        session()->flash('success', 'Brand deleted successfully.');
    }

    public function toggleFeatured(Brand $brand)
    {
        $brand->update(['is_featured' => !$brand->is_featured]);
        session()->flash('success', 'Featured status updated.');
    }

    public function render()
    {
        return view('livewire.admin.brands.index', [
            'brands' => Brand::withCount('products')->latest()->paginate(10),
        ]);
    }
}
