<?php

namespace Tests\Feature;

use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionPlanTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user for testing
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
    }

    /** @test */
    public function admin_can_view_subscription_plans_index()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.subscription-plans.index'));
        
        $response->assertStatus(200);
        $response->assertSee('Subscription Plans');
    }

    /** @test */
    public function admin_can_create_subscription_plan()
    {
        $this->actingAs($this->admin);
        
        $planData = [
            'name' => 'Test Plan',
            'description' => 'Test plan description',
            'price' => 1000,
            'interval' => 'monthly',
            'duration_days' => 30,
            'features' => 'Feature 1\nFeature 2',
            'is_active' => true,
            'product_limit' => 100,
            'commission_rate' => 2.7,
        ];

        $response = $this->post(route('admin.subscription-plans.index'), $planData);
        
        $this->assertDatabaseHas('subscription_plans', [
            'name' => 'Test Plan',
            'price' => 1000,
            'interval' => 'monthly',
        ]);
    }

    /** @test */
    public function subscription_plan_model_casts_work_correctly()
    {
        $plan = SubscriptionPlan::create([
            'name' => 'Test Plan',
            'description' => 'Test description',
            'price' => 1000.50,
            'interval' => 'monthly',
            'duration_days' => 30,
            'features' => ['Feature 1', 'Feature 2'],
            'is_active' => true,
        ]);

        $this->assertIsFloat($plan->price);
        $this->assertIsArray($plan->features);
        $this->assertEquals(['Feature 1', 'Feature 2'], $plan->features);
    }

    /** @test */
    public function subscription_plan_validation_works()
    {
        $this->actingAs($this->admin);
        
        // Test with invalid data
        $response = $this->post(route('admin.subscription-plans.index'), [
            'name' => '', // Required field empty
            'price' => -100, // Negative price
            'interval' => 'invalid', // Invalid interval
        ]);
        
        $response->assertSessionHasErrors(['name', 'price', 'interval']);
    }

    /** @test */
    public function cannot_delete_plan_with_active_subscriptions()
    {
        $this->actingAs($this->admin);
        
        $plan = SubscriptionPlan::create([
            'name' => 'Test Plan',
            'description' => 'Test description',
            'price' => 1000,
            'interval' => 'monthly',
            'duration_days' => 30,
            'is_active' => true,
        ]);

        // Create a mock active subscription
        $plan->subscriptions()->create([
            'vendor_id' => 1,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
        ]);

        $response = $this->delete(route('admin.subscription-plans.destroy', $plan));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('subscription_plans', ['id' => $plan->id]);
    }
}
