<div>
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">{{ __('Verify Your Email Address') }}</h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
            {{ __('Please check your inbox and click the verification link we just emailed to you.') }}
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
            @if (session('status') == 'verification-link-sent')
                <div class="mb-4 rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">{{ __('A new verification link has been sent to your email address.') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="space-y-6">
                <form wire:submit="sendVerification">
                    <div>
                        <button type="submit" class="flex w-full justify-center rounded-md border border-transparent bg-black py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 dark:hover:bg-gray-700 dark:focus:ring-offset-gray-800" wire:loading.attr="disabled">
                            {{ __('Resend Verification Email') }}
                        </button>
                    </div>
                </form>

                <div class="text-center">
                    <button wire:click="logout" type="button" class="text-sm font-medium text-black hover:text-gray-800 dark:text-white dark:hover:text-gray-200 focus:outline-none">
                        {{ __('Log Out') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
