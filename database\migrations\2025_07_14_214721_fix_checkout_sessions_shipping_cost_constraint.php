<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure shipping_cost column has proper default value and is not nullable
        Schema::table('checkout_sessions', function (Blueprint $table) {
            // Modify the shipping_cost column to ensure it has a default value
            $table->decimal('shipping_cost', 10, 2)->default(0.00)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the change if needed
        Schema::table('checkout_sessions', function (Blueprint $table) {
            $table->decimal('shipping_cost', 10, 2)->nullable()->change();
        });
    }
};
