@props([
    'placeholder' => 'Search...',
    'wire:model' => null,
    'wire:model.live' => null,
    'wire:model.defer' => null,
])

@php
    // Safely get wire:model attributes with proper type checking
    $wireModelBasic = null;
    $wireModelLive = null;
    $wireModelDefer = null;

    try {
        $wireModelBasic = $attributes->has('wire:model') ? $attributes->get('wire:model') : null;
        $wireModelLive = $attributes->has('wire:model.live') ? $attributes->get('wire:model.live') : null;
        $wireModelDefer = $attributes->has('wire:model.defer') ? $attributes->get('wire:model.defer') : null;
    } catch (\Exception $e) {
        \Log::error('Error getting wire:model attributes in search component', [
            'error' => $e->getMessage(),
            'attributes' => $attributes->getAttributes()
        ]);
    }

    $wireModel = $wireModelBasic ?? $wireModelLive ?? $wireModelDefer;
@endphp

<div class="relative">
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>
    </div>
    <input 
        type="text" 
        {{ $attributes->merge([
            'class' => 'block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder' => $placeholder
        ]) }}
        @if($wireModel) wire:model="{{ $wireModel }}" @endif
    />
</div>
