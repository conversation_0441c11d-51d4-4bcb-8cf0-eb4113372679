<?php

namespace App\Livewire\Vendor;

use App\Models\Category;
use App\Models\Product;
use App\Models\Vendor;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Computed;

#[Layout('layouts.app')]
class Storefront extends Component
{
    use WithPagination;

    public Vendor $vendor;

    /**
     * Mount the component and eager load the vendor's media to prevent N+1 issues.
     *
     * @param Vendor $vendor The vendor for the storefront.
     */
    public function mount(Vendor $vendor)
    {
        $this->vendor = $vendor->load('media');
    }

    /**
     * Get the vendor's products, paginated.
     * This is a cached computed property to optimize performance.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    #[Computed(cache: true)]
    public function products()
    {
        return Product::query()
            ->where('vendor_id', $this->vendor->id)
            ->where('is_active', true)
            ->with(['category', 'brand'])
            ->latest()
            ->paginate(12);
    }

    /**
     * Get the categories for the vendor's products, with product counts.
     * This is a cached computed property to optimize performance.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    #[Computed(cache: true)]
    public function categories()
    {
        return Category::whereHas('products', function($query) {
                $query->where('vendor_id', $this->vendor->id);
            })
            ->withCount(['products' => function($query) {
                $query->where('vendor_id', $this->vendor->id);
            }])
            ->get();
    }

    /**
     * Render the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.vendor.storefront', [
            'products' => $this->products,
            'categories' => $this->categories,
        ]);
    }
}
