<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="400" fill="#F3F4F6"/>
  
  <!-- Gradient overlay -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E5E7EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1D5DB;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="400" fill="url(#grad1)"/>
  
  <!-- Image icon -->
  <g transform="translate(150, 150)">
    <svg width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" stroke-width="1.5">
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
      <circle cx="8.5" cy="8.5" r="1.5"/>
      <polyline points="21,15 16,10 5,21"/>
    </svg>
  </g>
  
  <!-- Text -->
  <text x="200" y="280" text-anchor="middle" fill="#6B7280" font-family="Arial, sans-serif" font-size="16" font-weight="500">
    Product Image
  </text>
  <text x="200" y="300" text-anchor="middle" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="12">
    No image available
  </text>
</svg>
