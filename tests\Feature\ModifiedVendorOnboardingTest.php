<?php

namespace Tests\Feature;

use App\Livewire\Vendor\Onboarding\Index as VendorOnboardingIndex;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Tests\TestCase;

class ModifiedVendorOnboardingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    /** @test */
    public function vendor_can_complete_modified_onboarding_flow()
    {
        // Create a vendor user
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        // Step 1: Business Information & Logo
        $logo = UploadedFile::fake()->image('logo.png', 200, 200);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertSet('step', 'business')
            ->set('business_name', 'Test Business LLC')
            ->set('phone', '+234 ************')
            ->set('business_description', 'A test business for testing purposes')
            ->set('logo', $logo)
            ->call('saveBusinessInfo')
            ->assertSet('step', 'shipping')
            ->assertHasNoErrors();

        // Verify business info was saved
        $vendor->refresh();
        $this->assertEquals('Test Business LLC', $vendor->business_name);
        $this->assertEquals('+234 ************', $vendor->phone);
        $this->assertEquals('A test business for testing purposes', $vendor->business_description);
        $this->assertNotNull($vendor->getFirstMedia('logo'));

        // Step 2: Shipping Address
        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertSet('step', 'shipping')
            ->set('shipping_address', '123 Test Street, Apartment 4B')
            ->set('shipping_city', 'Lagos')
            ->set('shipping_state', 'Lagos State')
            ->set('shipping_postal_code', '100001')
            ->set('shipping_country', 'Nigeria')
            ->call('saveShippingInfo')
            ->assertRedirect('/vendor/dashboard');

        // Verify shipping info was saved
        $vendor->refresh();
        $this->assertEquals('123 Test Street, Apartment 4B', $vendor->business_address);
        $this->assertEquals('Lagos', $vendor->city);
        $this->assertEquals('Lagos State', $vendor->state);
        $this->assertEquals('100001', $vendor->postal_code);
        $this->assertEquals('Nigeria', $vendor->country);
        $this->assertTrue($vendor->has_completed_onboarding);
    }

    /** @test */
    public function onboarding_validates_required_business_fields()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', '')
            ->set('phone', '')
            ->set('business_description', '')
            ->call('saveBusinessInfo')
            ->assertHasErrors(['business_name', 'phone', 'business_description']);
    }

    /** @test */
    public function onboarding_validates_business_name_uniqueness()
    {
        $existingVendor = Vendor::factory()->create([
            'business_name' => 'Existing Business'
        ]);

        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', 'Existing Business')
            ->set('phone', '+234 ************')
            ->set('business_description', 'Test description')
            ->call('saveBusinessInfo')
            ->assertHasErrors(['business_name']);
    }

    /** @test */
    public function onboarding_validates_logo_file_type_and_size()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        // Test invalid file type
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', 'Test Business')
            ->set('phone', '+234 ************')
            ->set('business_description', 'Test description')
            ->set('logo', $invalidFile)
            ->call('saveBusinessInfo')
            ->assertHasErrors(['logo']);

        // Test oversized file
        $oversizedFile = UploadedFile::fake()->image('large.png')->size(3000); // 3MB

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('logo', $oversizedFile)
            ->call('saveBusinessInfo')
            ->assertHasErrors(['logo']);
    }

    /** @test */
    public function onboarding_validates_required_shipping_fields()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        // Complete business step first
        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', 'Test Business')
            ->set('phone', '+234 ************')
            ->set('business_description', 'Test description')
            ->call('saveBusinessInfo');

        // Test shipping validation
        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('shipping_address', '')
            ->set('shipping_city', '')
            ->set('shipping_state', '')
            ->set('shipping_country', '')
            ->call('saveShippingInfo')
            ->assertHasErrors(['shipping_address', 'shipping_city', 'shipping_state', 'shipping_country']);
    }

    /** @test */
    public function onboarding_allows_optional_postal_code()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        // Complete business step
        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', 'Test Business')
            ->set('phone', '+234 ************')
            ->set('business_description', 'Test description')
            ->call('saveBusinessInfo');

        // Complete shipping without postal code
        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('shipping_address', '123 Test Street')
            ->set('shipping_city', 'Lagos')
            ->set('shipping_state', 'Lagos State')
            ->set('shipping_postal_code', '') // Empty postal code
            ->set('shipping_country', 'Nigeria')
            ->call('saveShippingInfo')
            ->assertHasNoErrors()
            ->assertRedirect('/vendor/dashboard');

        $vendor->refresh();
        $this->assertNull($vendor->postal_code);
        $this->assertTrue($vendor->has_completed_onboarding);
    }

    /** @test */
    public function onboarding_step_determination_works_correctly()
    {
        $user = User::factory()->create();

        // New vendor should start at business step
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
            'phone' => null,
            'business_description' => null,
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertSet('step', 'business');

        // Vendor with business info should go to shipping step
        $vendor->update([
            'phone' => '+234 ************',
            'business_description' => 'Test description',
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertSet('step', 'shipping');
    }

    /** @test */
    public function completed_vendor_redirects_to_dashboard()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => true,
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertRedirect('/vendor/dashboard');
    }

    /** @test */
    public function logo_upload_clears_existing_logo()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'has_completed_onboarding' => false,
        ]);

        // Add existing logo
        $existingLogo = UploadedFile::fake()->image('existing.png');
        $vendor->addMedia($existingLogo->getRealPath())
            ->usingName('Existing Logo')
            ->toMediaCollection('logo');

        $this->assertCount(1, $vendor->getMedia('logo'));

        // Upload new logo
        $newLogo = UploadedFile::fake()->image('new.png');

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->set('business_name', 'Test Business')
            ->set('phone', '+234 ************')
            ->set('business_description', 'Test description')
            ->set('logo', $newLogo)
            ->call('saveBusinessInfo');

        $vendor->refresh();
        $this->assertCount(1, $vendor->getMedia('logo'));
        $this->assertEquals('Test Business Logo', $vendor->getFirstMedia('logo')->name);
    }

    /** @test */
    public function onboarding_pre_fills_existing_vendor_data()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'business_name' => 'Existing Business',
            'phone' => '+234 ************',
            'business_description' => 'Existing description',
            'business_address' => '456 Existing Street',
            'city' => 'Abuja',
            'state' => 'FCT',
            'postal_code' => '900001',
            'country' => 'Nigeria',
            'has_completed_onboarding' => false,
        ]);

        Livewire::actingAs($user)
            ->test(VendorOnboardingIndex::class)
            ->assertSet('business_name', 'Existing Business')
            ->assertSet('phone', '+234 ************')
            ->assertSet('business_description', 'Existing description')
            ->assertSet('shipping_address', '456 Existing Street')
            ->assertSet('shipping_city', 'Abuja')
            ->assertSet('shipping_state', 'FCT')
            ->assertSet('shipping_postal_code', '900001')
            ->assertSet('shipping_country', 'Nigeria');
    }
}
