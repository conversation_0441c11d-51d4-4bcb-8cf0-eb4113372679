<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Color;
use App\Models\Size;
use Illuminate\Support\Str;

class ProductVariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $colors = Color::all();
        $sizes = Size::all();
        $products = Product::with('vendor')->get();

        if ($colors->isEmpty()) {
            $this->command->error('No colors found. Please run ColorSeeder first.');
            return;
        }

        if ($sizes->isEmpty()) {
            $this->command->error('No sizes found. Please run SizeSeeder first.');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->error('No products found. Please run ProductSeeder first.');
            return;
        }

        // Clear existing variants to avoid conflicts
        $this->command->info('Clearing existing product variants...');
        ProductVariant::truncate();

        // Define which categories should have which variant types
        $categoryVariantMap = [
            'clothing' => ['color', 'size'],
            'mens-clothing' => ['color', 'size'],
            'womens-clothing' => ['color', 'size'],
            'kids-clothing' => ['color', 'size'],
            'activewear' => ['color', 'size'],
            'footwear' => ['color', 'size'],
            'sneakers' => ['color', 'size'],
            'formal-shoes' => ['color', 'size'],
            'sandals' => ['color', 'size'],
            'boots' => ['color', 'size'],
            'bags' => ['color'],
            'jewelry' => ['color'],
            'watches' => ['color'],
            'sunglasses' => ['color'],
            'smartphones' => ['color'],
            'laptops' => ['color'],
            'audio' => ['color'],
            'gaming' => ['color'],
        ];

        foreach ($products as $product) {
            $categorySlug = $product->category->slug ?? 'default';
            $variantTypes = $categoryVariantMap[$categorySlug] ?? ['color'];

            // Determine how many variants to create for this product
            $shouldHaveVariants = rand(1, 100) <= 70; // 70% chance of having variants

            if (!$shouldHaveVariants) {
                continue; // Skip this product
            }

            $this->createVariantsForProduct($product, $variantTypes, $colors, $sizes);
        }

        $this->command->info('Created product variants for applicable products');
    }

    /**
     * Create variants for a specific product
     */
    private function createVariantsForProduct(Product $product, array $variantTypes, $colors, $sizes)
    {
        $hasColor = in_array('color', $variantTypes);
        $hasSize = in_array('size', $variantTypes);

        // Check if variants already exist for this product
        $existingVariants = $product->variants()->get();
        if ($existingVariants->isNotEmpty()) {
            $this->command->info("Product {$product->name} already has variants, skipping...");
            return;
        }

        // Create combinations based on variant types
        $combinations = [];

        if ($hasColor && $hasSize) {
            // Both color and size variants
            $productColors = $colors->random(rand(2, 4));
            $productSizes = $sizes->random(rand(2, 4));

            foreach ($productColors as $color) {
                foreach ($productSizes as $size) {
                    $combinations[] = ['color' => $color, 'size' => $size];
                }
            }
        } elseif ($hasColor) {
            // Only color variants
            $productColors = $colors->random(rand(3, 6));
            foreach ($productColors as $color) {
                $combinations[] = ['color' => $color, 'size' => null];
            }
        } elseif ($hasSize) {
            // Only size variants
            $productSizes = $sizes->random(rand(3, 6));
            foreach ($productSizes as $size) {
                $combinations[] = ['color' => null, 'size' => $size];
            }
        } else {
            // No variants for this product type
            return;
        }

        // Limit total variants and shuffle for randomness
        $combinations = collect($combinations)->shuffle()->take(12)->toArray();

        $variantCount = 0;
        foreach ($combinations as $combination) {
            $color = $combination['color'];
            $size = $combination['size'];

            // Skip if both color and size are null
            if (!$color && !$size) {
                continue;
            }

            // Check if this combination already exists (extra safety)
            $exists = ProductVariant::where('product_id', $product->id)
                ->where('color_id', $color?->id)
                ->where('size_id', $size?->id)
                ->exists();

            if (!$exists) {
                $this->createVariant($product, $color, $size);
                $variantCount++;
            }
        }

        $this->command->info("Created {$variantCount} variants for product: {$product->name}");
    }

    /**
     * Create a single variant
     */
    private function createVariant(Product $product, $color = null, $size = null)
    {
        // Generate SKU
        $skuParts = [
            strtoupper(substr($product->vendor->shop_name, 0, 3)),
            $product->id,
        ];

        if ($color) {
            $skuParts[] = strtoupper(substr($color->name, 0, 3));
        }

        if ($size) {
            $skuParts[] = strtoupper($size->code);
        }

        $sku = implode('-', $skuParts);

        // Ensure SKU is unique
        $originalSku = $sku;
        $counter = 1;
        while (ProductVariant::where('sku', $sku)->exists()) {
            $sku = $originalSku . '-' . $counter;
            $counter++;
        }

        // Random price adjustment (-10% to +20%)
        $priceAdjustment = null;
        if (rand(1, 100) <= 30) { // 30% chance of price adjustment
            $adjustmentPercent = rand(-10, 20) / 100;
            $priceAdjustment = $product->price * $adjustmentPercent;
        }

        // Random stock quantity
        $stockQuantity = rand(5, 50);

        // Some variants might be inactive (5% chance)
        $isActive = rand(1, 100) > 5;

        ProductVariant::updateOrCreate(
            [
                'product_id' => $product->id,
                'color_id' => $color?->id,
                'size_id' => $size?->id,
            ],
            [
                'sku' => $sku,
                'price_adjustment' => $priceAdjustment,
                'stock_quantity' => $stockQuantity,
                'is_active' => $isActive,
            ]
        );
    }
}
