<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Application Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for application monitoring, logging,
    | and alerting systems. It defines thresholds, metrics, and alert rules
    | for comprehensive application health monitoring.
    |
    */

    'enabled' => env('MONITORING_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for performance monitoring including database query times,
    | API response times, and memory usage tracking.
    |
    */
    'performance' => [
        'enabled' => env('PERFORMANCE_MONITORING_ENABLED', true),
        
        'thresholds' => [
            'database_query_time_ms' => [
                'warning' => 100,
                'critical' => 500,
            ],
            'api_response_time_ms' => [
                'warning' => 1000,
                'critical' => 5000,
            ],
            'memory_usage_mb' => [
                'warning' => 256,
                'critical' => 512,
            ],
            'cache_hit_rate_percent' => [
                'warning' => 70,
                'critical' => 50,
            ],
        ],

        'slow_query_threshold_ms' => 200,
        'log_slow_queries' => true,
        'track_memory_usage' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Financial Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for monitoring financial transactions, commission
    | calculations, and vendor balance operations.
    |
    */
    'financial' => [
        'enabled' => env('FINANCIAL_MONITORING_ENABLED', true),
        
        'commission_rate' => 0.027, // 2.7%
        'commission_tolerance' => 0.01, // ₦0.01 tolerance for rounding
        
        'alerts' => [
            'commission_discrepancy' => true,
            'balance_reconciliation_failure' => true,
            'large_transaction_threshold' => 100000.00, // ₦100,000
            'failed_payment_threshold' => 5, // Alert after 5 failed payments
        ],

        'audit_trail' => [
            'enabled' => true,
            'retention_days' => 365,
            'include_metadata' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for security event monitoring, intrusion detection,
    | and suspicious activity tracking.
    |
    */
    'security' => [
        'enabled' => env('SECURITY_MONITORING_ENABLED', true),
        
        'failed_login_threshold' => 5,
        'suspicious_activity_threshold' => 10,
        'rate_limit_threshold' => 100,
        
        'events_to_monitor' => [
            'failed_authentication',
            'unauthorized_access_attempt',
            'privilege_escalation_attempt',
            'suspicious_file_upload',
            'sql_injection_attempt',
            'xss_attempt',
            'csrf_token_mismatch',
            'mass_assignment_attempt',
        ],

        'ip_whitelist' => [
            // Add trusted IP addresses
        ],

        'alert_channels' => [
            'email' => env('SECURITY_ALERT_EMAIL'),
            'slack' => env('SECURITY_ALERT_SLACK_WEBHOOK'),
            'sms' => env('SECURITY_ALERT_SMS_ENABLED', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Metrics Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for tracking business KPIs and operational metrics.
    |
    */
    'business_metrics' => [
        'enabled' => env('BUSINESS_METRICS_ENABLED', true),
        
        'kpis' => [
            'order_conversion_rate' => [
                'target' => 0.15, // 15%
                'warning_threshold' => 0.10, // 10%
            ],
            'vendor_onboarding_completion_rate' => [
                'target' => 0.80, // 80%
                'warning_threshold' => 0.60, // 60%
            ],
            'average_order_value' => [
                'target' => 5000.00, // ₦5,000
                'warning_threshold' => 3000.00, // ₦3,000
            ],
            'customer_satisfaction_score' => [
                'target' => 4.5, // out of 5
                'warning_threshold' => 4.0,
            ],
        ],

        'tracking_intervals' => [
            'real_time' => ['orders', 'payments', 'errors'],
            'hourly' => ['conversion_rates', 'api_calls'],
            'daily' => ['revenue', 'new_users', 'vendor_signups'],
            'weekly' => ['customer_satisfaction', 'product_approvals'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for monitoring external API integrations including
    | Paystack, ShipBubble, and other third-party services.
    |
    */
    'api_monitoring' => [
        'enabled' => env('API_MONITORING_ENABLED', true),
        
        'services' => [
            'paystack' => [
                'timeout_threshold_ms' => 10000,
                'error_rate_threshold' => 0.05, // 5%
                'circuit_breaker_threshold' => 10,
                'health_check_interval' => 300, // 5 minutes
            ],
            'shipbubble' => [
                'timeout_threshold_ms' => 15000,
                'error_rate_threshold' => 0.10, // 10%
                'circuit_breaker_threshold' => 5,
                'health_check_interval' => 600, // 10 minutes
                'cache_hit_rate_target' => 0.80, // 80%
            ],
        ],

        'retry_configuration' => [
            'max_retries' => 3,
            'retry_delay_ms' => 1000,
            'exponential_backoff' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for structured logging including log levels, retention,
    | and formatting options.
    |
    */
    'logging' => [
        'structured_logging' => [
            'enabled' => env('STRUCTURED_LOGGING_ENABLED', true),
            'include_context' => true,
            'include_performance_metrics' => true,
            'include_user_context' => true,
            'include_request_context' => true,
        ],

        'log_levels' => [
            'financial_transactions' => 'info',
            'security_events' => 'warning',
            'api_calls' => 'info',
            'performance_metrics' => 'debug',
            'business_events' => 'info',
            'errors' => 'error',
        ],

        'retention' => [
            'error_logs' => 90, // days
            'security_logs' => 365, // days
            'financial_logs' => 2555, // 7 years
            'performance_logs' => 30, // days
            'general_logs' => 30, // days
        ],

        'sampling' => [
            'enabled' => env('LOG_SAMPLING_ENABLED', false),
            'rate' => 0.1, // 10% sampling for high-volume logs
            'exclude_levels' => ['error', 'critical'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Alert Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for alert rules, notification channels, and escalation
    | procedures for different types of incidents.
    |
    */
    'alerts' => [
        'enabled' => env('ALERTS_ENABLED', true),
        
        'channels' => [
            'email' => [
                'enabled' => env('EMAIL_ALERTS_ENABLED', true),
                'recipients' => [
                    'critical' => explode(',', env('CRITICAL_ALERT_EMAILS', '')),
                    'high' => explode(',', env('HIGH_ALERT_EMAILS', '')),
                    'medium' => explode(',', env('MEDIUM_ALERT_EMAILS', '')),
                ],
            ],
            'slack' => [
                'enabled' => env('SLACK_ALERTS_ENABLED', false),
                'webhook_url' => env('SLACK_WEBHOOK_URL'),
                'channels' => [
                    'critical' => '#alerts-critical',
                    'high' => '#alerts-high',
                    'medium' => '#alerts-medium',
                ],
            ],
            'sms' => [
                'enabled' => env('SMS_ALERTS_ENABLED', false),
                'provider' => env('SMS_PROVIDER', 'twilio'),
                'recipients' => explode(',', env('SMS_ALERT_NUMBERS', '')),
            ],
        ],

        'rules' => [
            'financial_discrepancy' => [
                'severity' => 'critical',
                'cooldown_minutes' => 5,
                'escalation_minutes' => 15,
            ],
            'security_breach' => [
                'severity' => 'critical',
                'cooldown_minutes' => 0,
                'escalation_minutes' => 5,
            ],
            'api_failure' => [
                'severity' => 'high',
                'cooldown_minutes' => 10,
                'escalation_minutes' => 30,
            ],
            'performance_degradation' => [
                'severity' => 'medium',
                'cooldown_minutes' => 15,
                'escalation_minutes' => 60,
            ],
        ],

        'escalation' => [
            'enabled' => true,
            'levels' => [
                1 => 'team_lead',
                2 => 'engineering_manager',
                3 => 'cto',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Health Checks
    |--------------------------------------------------------------------------
    |
    | Configuration for application health checks and status monitoring.
    |
    */
    'health_checks' => [
        'enabled' => env('HEALTH_CHECKS_ENABLED', true),
        'interval_seconds' => 60,
        
        'checks' => [
            'database' => [
                'enabled' => true,
                'timeout_seconds' => 5,
                'query' => 'SELECT 1',
            ],
            'cache' => [
                'enabled' => true,
                'timeout_seconds' => 3,
                'test_key' => 'health_check_test',
            ],
            'storage' => [
                'enabled' => true,
                'timeout_seconds' => 5,
                'test_file' => 'health_check.txt',
            ],
            'external_apis' => [
                'enabled' => true,
                'timeout_seconds' => 10,
                'services' => ['paystack', 'shipbubble'],
            ],
        ],

        'status_page' => [
            'enabled' => env('STATUS_PAGE_ENABLED', true),
            'url' => '/health',
            'include_details' => env('HEALTH_CHECK_DETAILS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Metrics Collection
    |--------------------------------------------------------------------------
    |
    | Configuration for metrics collection and aggregation.
    |
    */
    'metrics' => [
        'enabled' => env('METRICS_ENABLED', true),
        'collection_interval_seconds' => 60,
        
        'collectors' => [
            'system' => [
                'cpu_usage' => true,
                'memory_usage' => true,
                'disk_usage' => true,
                'network_io' => false,
            ],
            'application' => [
                'request_count' => true,
                'response_time' => true,
                'error_rate' => true,
                'active_users' => true,
            ],
            'business' => [
                'orders_per_minute' => true,
                'revenue_per_hour' => true,
                'conversion_rate' => true,
                'vendor_activity' => true,
            ],
        ],

        'storage' => [
            'driver' => env('METRICS_STORAGE_DRIVER', 'database'),
            'retention_days' => 90,
            'aggregation_intervals' => ['1m', '5m', '1h', '1d'],
        ],
    ],
];
