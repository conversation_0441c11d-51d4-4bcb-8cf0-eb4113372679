<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all vendors with their brands
        $vendors = Vendor::with('brand')->get();

        // Get all available categories (should be created by CategorySeeder)
        $categories = Category::where('is_active', true)->get();

        if ($categories->isEmpty()) {
            $this->command->error('No categories found. Please run CategorySeeder first.');
            return;
        }

        // Create products for each vendor
        foreach ($vendors as $vendor) {
            // Skip if vendor doesn't have a brand (shouldn't happen with new structure)
            if (!$vendor->brand) {
                $this->command->warn("Vendor {$vendor->shop_name} has no brand. Skipping products.");
                continue;
            }

            // Create 25 products for each vendor with sample images
            for ($i = 1; $i <= 50; $i++) {
                $name = $vendor->shop_name . ' Product ' . $i;
                $isFeatured = $i <= 5; // First 5 products are featured
                $isBestSeller = in_array($i, [3, 7, 12, 18, 22]); // Mark a few as best sellers
                $randomCategory = $categories->random(); // Assign random category

                $product = Product::updateOrCreate(
                    ['slug' => Str::slug($name)],
                    [
                        'name' => $name,
                        'description' => 'This is a sample product from ' . $vendor->shop_name . '. High quality and affordable pricing with excellent customer service.',
                        'price' => rand(1000, 50000) / 100, // Random price between ₦10 and ₦500
                        'discount_price' => rand(0, 1) ? rand(500, 40000) / 100 : null, // 50% chance of discount price
                        'is_active' => true,
                        'is_best_seller' => $isBestSeller,
                        'stock' => rand(10, 100), // Add random stock quantity
                        'vendor_id' => $vendor->id,
                        'brand_id' => $vendor->brand->id, // Automatically set brand_id from vendor's brand
                        'category_id' => $randomCategory->id, // Use random category
                        'weight' => rand(200, 800) / 1000, // 0.2kg to 0.8kg
                        'length' => rand(30, 40), // 30cm to 40cm
                        'width' => rand(20, 30), // 20cm to 30cm
                        'height' => rand(2, 8), // 2cm to 8cm
                        'created_at' => now()->subDays(rand(1, 30)) // Random creation date in the last 30 days
                    ]
                );

                // Add sample product image
                $this->addSampleProductImage($product, $name);

                // Add sample gallery images for featured products
                if ($isFeatured) {
                    $this->addSampleGalleryImages($product, $name);
                }
            }

            $this->command->info("Created 50 products with images for vendor: {$vendor->shop_name} (Brand: {$vendor->brand->name})");
        }
    }

    /**
     * Add sample product image using Media Library
     */
    private function addSampleProductImage(Product $product, string $productName)
    {
        try {
            // Create a sample product image
            $imageContent = $this->createSampleProductImage(800, 600, $productName);
            $imagePath = storage_path('app/temp/' . Str::slug($productName) . '-main.png');

            // Ensure temp directory exists
            if (!file_exists(dirname($imagePath))) {
                mkdir(dirname($imagePath), 0755, true);
            }

            file_put_contents($imagePath, $imageContent);

            // Add to media library
            $product->addMedia($imagePath)
                ->usingName($productName)
                ->usingFileName(Str::slug($productName) . '-main.png')
                ->toMediaCollection('product_images');

            // Clean up temp file
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        } catch (\Exception $e) {
            $this->command->warn("Could not create main image for {$productName}: " . $e->getMessage());
        }
    }

    /**
     * Add sample gallery images for featured products
     */
    private function addSampleGalleryImages(Product $product, string $productName)
    {
        try {
            // Create 2-3 gallery images
            $galleryCount = rand(2, 3);

            for ($i = 1; $i <= $galleryCount; $i++) {
                $imageContent = $this->createSampleProductImage(800, 600, $productName . " View {$i}");
                $imagePath = storage_path('app/temp/' . Str::slug($productName) . "-gallery-{$i}.png");

                // Ensure temp directory exists
                if (!file_exists(dirname($imagePath))) {
                    mkdir(dirname($imagePath), 0755, true);
                }

                file_put_contents($imagePath, $imageContent);

                // Add to media library
                $product->addMedia($imagePath)
                    ->usingName($productName . " Gallery {$i}")
                    ->usingFileName(Str::slug($productName) . "-gallery-{$i}.png")
                    ->toMediaCollection('product_gallery');

                // Clean up temp file
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
        } catch (\Exception $e) {
            $this->command->warn("Could not create gallery images for {$productName}: " . $e->getMessage());
        }
    }

    /**
     * Create a sample product image
     */
    private function createSampleProductImage(int $width, int $height, string $text): string
    {
        // Create a colorful product image
        $image = imagecreate($width, $height);

        // Random colors for variety
        $colors = [
            [255, 200, 200], // Light red
            [200, 255, 200], // Light green
            [200, 200, 255], // Light blue
            [255, 255, 200], // Light yellow
            [255, 200, 255], // Light magenta
            [200, 255, 255], // Light cyan
        ];

        $colorSet = $colors[array_rand($colors)];
        $backgroundColor = imagecolorallocate($image, $colorSet[0], $colorSet[1], $colorSet[2]);
        $textColor = imagecolorallocate($image, 50, 50, 50); // Dark gray
        $borderColor = imagecolorallocate($image, 150, 150, 150); // Gray

        // Fill background
        imagefill($image, 0, 0, $backgroundColor);

        // Add border
        imagerectangle($image, 0, 0, $width - 1, $height - 1, $borderColor);

        // Add some decorative elements
        for ($i = 0; $i < 10; $i++) {
            $x = rand(50, $width - 50);
            $y = rand(50, $height - 50);
            $size = rand(20, 50);
            $decorColor = imagecolorallocate($image,
                max(0, $colorSet[0] - 50),
                max(0, $colorSet[1] - 50),
                max(0, $colorSet[2] - 50)
            );
            imagefilledellipse($image, $x, $y, $size, $size, $decorColor);
        }

        // Add text
        $lines = explode(' ', $text);
        $lineHeight = 20;
        $startY = ($height - (count($lines) * $lineHeight)) / 2;

        foreach ($lines as $index => $line) {
            $textWidth = strlen($line) * 10; // Approximate width
            $x = ($width - $textWidth) / 2;
            $y = $startY + ($index * $lineHeight);

            imagestring($image, 4, $x, $y, $line, $textColor);
        }

        // Capture output
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();

        // Clean up
        imagedestroy($image);

        return $imageData;
    }
}
