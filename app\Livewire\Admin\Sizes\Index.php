<?php

namespace App\Livewire\Admin\Sizes;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\Size;
use Livewire\WithPagination;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $showCreateModal = false;
    public $showEditModal = false;
    public $editingSize = null;

    // Form fields
    public $name = '';
    public $code = '';
    public $sort_order = 0;
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'code' => 'required|string|max:10|unique:sizes,code',
        'sort_order' => 'integer|min:0',
        'is_active' => 'boolean',
    ];

    public function render()
    {
        $sizes = Size::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('code', 'like', '%' . $this->search . '%');
            })
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return view('livewire.admin.sizes.index', compact('sizes'));
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function create()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        $this->validate();

        Size::create([
            'name' => $this->name,
            'code' => strtoupper($this->code),
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        $this->dispatch('toast', message: 'Size created successfully!', type: 'success');
    }

    public function edit($sizeId)
    {
        $this->editingSize = Size::findOrFail($sizeId);
        $this->name = $this->editingSize->name;
        $this->code = $this->editingSize->code;
        $this->sort_order = $this->editingSize->sort_order;
        $this->is_active = $this->editingSize->is_active;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->rules['code'] = 'required|string|max:10|unique:sizes,code,' . $this->editingSize->id;
        $this->validate();

        $this->editingSize->update([
            'name' => $this->name,
            'code' => strtoupper($this->code),
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        $this->dispatch('toast', message: 'Size updated successfully!', type: 'success');
    }

    public function delete($sizeId)
    {
        $size = Size::findOrFail($sizeId);
        
        // Check if size is used in any variants
        if ($size->variants()->exists()) {
            $this->dispatch('toast', message: 'Cannot delete size that is used in product variants.', type: 'error');
            return;
        }

        $size->delete();
        $this->dispatch('toast', message: 'Size deleted successfully!', type: 'success');
    }

    public function toggleStatus($sizeId)
    {
        $size = Size::findOrFail($sizeId);
        $size->update(['is_active' => !$size->is_active]);
        
        $status = $size->is_active ? 'activated' : 'deactivated';
        $this->dispatch('toast', message: "Size {$status} successfully!", type: 'success');
    }

    private function resetForm()
    {
        $this->name = '';
        $this->code = '';
        $this->sort_order = 0;
        $this->is_active = true;
        $this->editingSize = null;
        $this->resetValidation();
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->resetForm();
    }
}
