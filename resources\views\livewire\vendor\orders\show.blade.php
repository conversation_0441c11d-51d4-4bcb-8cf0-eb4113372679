<div>
    <div class="mb-4">
        <a href="{{ route('vendor.orders.index') }}" class="text-gray-700 hover:text-gray-900 font-medium">
            <i class="fas fa-arrow-left mr-2"></i> Back to Orders
        </a>
    </div>

    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-800">Order #{{ $order->id }}</h2>
            <span class="px-3 py-1 text-sm font-semibold rounded-full text-white 
                {{ $order->status == 'completed' ? 'bg-green-500' : '' }}
                {{ $order->status == 'processing' ? 'bg-yellow-500' : '' }}
                {{ $order->status == 'shipping' ? 'bg-blue-500' : '' }}
                {{ $order->status == 'cancelled' ? 'bg-red-500' : '' }}
                {{ !in_array($order->status, ['completed', 'processing', 'shipping', 'cancelled']) ? 'bg-gray-500' : '' }}
            ">
                {{ ucfirst($order->status) }}
            </span>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Order Information</h3>
                    <p><strong>Order Date:</strong> {{ $order->created_at->format('M d, Y H:i') }}</p>
                    <p><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                    <p><strong>Payment Status:</strong> 
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->payment_status == 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Customer Information</h3>
                    <p><strong>Name:</strong> {{ $order->user->name ?? 'Guest' }}</p>
                    <p><strong>Email:</strong> {{ $order->user->email ?? $order->email }}</p>
                    <p><strong>Phone:</strong> {{ $order->phone ?? 'N/A' }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Shipping Address</h3>
                    @php $shippingAddr = $order->formatted_shipping_address; @endphp
                    <address class="not-italic text-gray-700">
                        @if($shippingAddr['name'])
                            <div class="font-medium">{{ $shippingAddr['name'] }}</div>
                        @endif
                        @if($shippingAddr['address'])
                            <div>{{ $shippingAddr['address'] }}</div>
                        @endif
                        @if($shippingAddr['city'] || $shippingAddr['state'] || $shippingAddr['postal_code'])
                            <div>
                                {{ $shippingAddr['city'] }}@if($shippingAddr['city'] && ($shippingAddr['state'] || $shippingAddr['postal_code'])),@endif
                                {{ $shippingAddr['state'] }} {{ $shippingAddr['postal_code'] }}
                            </div>
                        @endif
                        @if($shippingAddr['lga'])
                            <div class="text-sm text-gray-600">LGA: {{ $shippingAddr['lga'] }}</div>
                        @endif
                        @if($shippingAddr['country'])
                            <div>{{ $shippingAddr['country'] }}</div>
                        @endif
                        @if($shippingAddr['phone'])
                            <div class="mt-2 text-sm">
                                <i class="fas fa-phone mr-1"></i>{{ $shippingAddr['phone'] }}
                            </div>
                        @endif
                        @if(!$shippingAddr['name'] && !$shippingAddr['address'] && !$shippingAddr['city'])
                            <div class="text-gray-500 italic">No shipping address available</div>
                        @endif
                    </address>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Billing Address</h3>
                    @php $billingAddr = $order->formatted_billing_address; @endphp
                    <address class="not-italic text-gray-700">
                        @if($billingAddr['name'])
                            <div class="font-medium">{{ $billingAddr['name'] }}</div>
                        @endif
                        @if($billingAddr['address'])
                            <div>{{ $billingAddr['address'] }}</div>
                        @endif
                        @if($billingAddr['city'] || $billingAddr['state'] || $billingAddr['postal_code'])
                            <div>
                                {{ $billingAddr['city'] }}@if($billingAddr['city'] && ($billingAddr['state'] || $billingAddr['postal_code'])),@endif
                                {{ $billingAddr['state'] }} {{ $billingAddr['postal_code'] }}
                            </div>
                        @endif
                        @if($billingAddr['country'])
                            <div>{{ $billingAddr['country'] }}</div>
                        @endif
                        @if($billingAddr['phone'])
                            <div class="mt-2 text-sm">
                                <i class="fas fa-phone mr-1"></i>{{ $billingAddr['phone'] }}
                            </div>
                        @endif
                    </address>
                </div>
            </div>

            <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Order Items</h3>
                <div class="-mx-4 sm:-mx-8 px-4 sm:px-8 py-4 overflow-x-auto">
                    <div class="inline-block min-w-full shadow rounded-lg overflow-hidden">
                        <table class="min-w-full leading-normal">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Product</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Price</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
                                    <th class="px-5 py-3 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($vendorItems as $item)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-12 h-12">
                                                    <img class="w-full h-full rounded-md object-cover" src="{{ $item->product->image_url ?? asset('images/default-product.png') }}" alt="{{ $item->product_name }}" />
                                                </div>
                                                <div class="ml-4">
                                                    <p class="text-gray-900 whitespace-no-wrap font-semibold">{{ $item->product_name }}</p>
                                                    <p class="text-gray-600 whitespace-no-wrap text-xs">SKU: {{ $item->product->id ?? 'N/A' }}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">₦{{ number_format($item->price, 2) }}</td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ $item->quantity }}</td>
                                        <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-right">₦{{ number_format($item->price * $item->quantity, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-10 text-gray-500">No items found for your store in this order.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                            <tfoot class="bg-gray-50 font-semibold">
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right">Subtotal</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right">₦{{ number_format($subtotal, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right">Commission (2.7%)</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right">-₦{{ number_format($commission, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-5 py-3 border-b-2 border-gray-200 text-right text-lg">Your Earnings</td>
                                    <td class="px-5 py-3 border-b-2 border-gray-200 text-right text-lg">₦{{ number_format($netAmount, 2) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            @if($order->status !== 'completed' && $order->status !== 'cancelled')
            <div class="mt-6">
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">Update Order Status</h3>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                As a vendor, you can only mark orders as "shipped" when you've dispatched the items. The system will automatically update to "completed" when delivery is confirmed via ShipBubble.
                            </p>
                        </div>
                    </div>
                </div>
                <form wire:submit.prevent="updateStatus" class="flex items-center">
                    <select wire:model.defer="newStatus" class="block w-full max-w-xs rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm">
                        <option value="{{ $order->status }}">{{ ucfirst($order->status) }} (Current)</option>
                        @if($order->status !== 'shipped')
                            <option value="shipped">Mark as Shipped</option>
                        @endif
                    </select>
                    <button type="submit"
                            wire:loading.attr="disabled"
                            class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed">
                        <span wire:loading.remove wire:target="updateStatus">Update Status</span>
                        <span wire:loading wire:target="updateStatus">Updating...</span>
                    </button>
                </form>
            </div>
            @else
            <div class="mt-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">
                                This order has been {{ $order->status }}. No further status updates are available.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
