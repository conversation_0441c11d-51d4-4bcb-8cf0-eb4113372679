<div>
    <div id="vendor-map" style="height: 500px;"></div>

    @push('scripts')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        document.addEventListener('livewire:navigated', function () {
            // CRITICAL FIX: Use modern Livewire event and ensure map container exists
            if (document.getElementById('vendor-map')) {
                var map = L.map('vendor-map').setView([9.0820, 8.6753], 6);

            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            var orderData = @json($orderData);

            fetch('/geojson/nigeria-states.json')
                .then(response => response.json())
                .then(data => {
                    L.geoJson(data, {
                        style: function (feature) {
                            var stateName = feature.properties.state;
                            var orderCount = orderData[stateName] || 0;
                            return {
                                fillColor: getColor(orderCount),
                                weight: 2,
                                opacity: 1,
                                color: 'white',
                                dashArray: '3',
                                fillOpacity: 0.7
                            };
                        },
                        onEachFeature: function (feature, layer) {
                            var stateName = feature.properties.state;
                            var orderCount = orderData[stateName] || 0;
                            layer.bindPopup('State: ' + stateName + '<br>Orders: ' + orderCount);
                        }
                    }).addTo(map);
                });

            function getColor(d) {
                return d > 100 ? '#800026' :
                       d > 50  ? '#BD0026' :
                       d > 20  ? '#E31A1C' :
                       d > 10  ? '#FC4E2A' :
                       d > 5   ? '#FD8D3C' :
                       d > 2   ? '#FEB24C' :
                       d > 1   ? '#FED976' :
                                  '#FFEDA0';
            }
            }
        });
    </script>
    @endpush
</div>