{{-- Simple Tailwind-based notification system to replace WireUI notifications --}}
@props(['zIndex' => 'z-50'])

<div 
    x-data="notificationSystem" 
    x-init="init()"
    class="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end {{ $zIndex }}"
    x-on:toast.window="addNotification($event.detail)"
    x-on:notification.window="addNotification($event.detail)"
>
    <div class="flex flex-col space-y-4 w-full max-w-sm pointer-events-auto">
        <template x-for="notification in notifications" :key="notification.id">
            <div 
                x-show="notification.show"
                x-transition:enter="transform ease-out duration-300 transition"
                x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
                x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
            >
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <!-- Success Icon -->
                            <template x-if="notification.type === 'success'">
                                <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </template>
                            
                            <!-- Error Icon -->
                            <template x-if="notification.type === 'error'">
                                <svg class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </template>
                            
                            <!-- Warning Icon -->
                            <template x-if="notification.type === 'warning'">
                                <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </template>
                            
                            <!-- Info Icon -->
                            <template x-if="notification.type === 'info' || !notification.type">
                                <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </template>
                        </div>
                        
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-gray-900" x-text="notification.title || 'Notification'"></p>
                            <p class="mt-1 text-sm text-gray-500" x-text="notification.message"></p>
                        </div>
                        
                        <div class="ml-4 flex-shrink-0 flex">
                            <button 
                                @click="removeNotification(notification.id)"
                                class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('notificationSystem', () => ({
        notifications: [],
        nextId: 1,

        init() {
            // Listen for Livewire flash messages
            this.$nextTick(() => {
                // Check for Laravel flash messages and convert them to notifications
                @if(session('success'))
                    this.addNotification({
                        type: 'success',
                        message: @json(session('success'))
                    });
                @endif

                @if(session('error'))
                    this.addNotification({
                        type: 'error',
                        message: @json(session('error'))
                    });
                @endif

                @if(session('warning'))
                    this.addNotification({
                        type: 'warning',
                        message: @json(session('warning'))
                    });
                @endif

                @if(session('info'))
                    this.addNotification({
                        type: 'info',
                        message: @json(session('info'))
                    });
                @endif
            });
        },

        addNotification(notification) {
            const id = this.nextId++;
            const newNotification = {
                id: id,
                type: notification.type || 'info',
                title: notification.title || null,
                message: notification.message || notification,
                show: true
            };

            this.notifications.push(newNotification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                this.removeNotification(id);
            }, 5000);
        },

        removeNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications[index].show = false;
                // Remove from array after transition
                setTimeout(() => {
                    this.notifications.splice(index, 1);
                }, 300);
            }
        }
    }));
});
</script>
