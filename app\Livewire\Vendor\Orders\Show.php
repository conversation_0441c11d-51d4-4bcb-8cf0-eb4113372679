<?php

namespace App\Livewire\Vendor\Orders;

use App\Models\Order;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Show extends Component
{
    public Order $order;
    public $vendorItems;
    public $subtotal = 0;
    public $commission = 0;
    public $netAmount = 0;
    public $newStatus;

    public function mount(Order $order)
    {
        $this->authorize('view', $order);
        $this->order = $order->load('items.product', 'user');
        $this->newStatus = $this->order->status;
        $this->calculateTotals();
    }

    public function calculateTotals()
    {
        $vendor = Auth::user()->vendor;
        $this->vendorItems = $this->order->items->filter(function ($item) use ($vendor) {
            return $item->product && $item->product->vendor_id == $vendor->id;
        });

        $this->subtotal = $this->vendorItems->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        // Use configured commission rate (2.7%)
        $commissionRate = config('brandify.commission_rate', 0.027);
        $this->commission = $this->subtotal * $commissionRate;
        $this->netAmount = $this->subtotal - $this->commission;
    }

    public function updateStatus()
    {
        // Vendors can only change status to 'shipped'
        $this->validate(['newStatus' => 'required|in:shipped']);

        // Authorization is handled in mount method via OrderPolicy

        // Prevent updating already completed or cancelled orders
        if (in_array($this->order->status, ['completed', 'cancelled'])) {
            session()->flash('error', 'Cannot update status of completed or cancelled orders.');
            return;
        }

        try {
            // Update order status to shipped
            $this->order->update([
                'status' => $this->newStatus,
                'shipped_at' => now()
            ]);

            // If order has ShipBubble tracking, check delivery status
            if ($this->order->shipping_tracking_url && $this->newStatus === 'shipped') {
                $this->checkShipBubbleDeliveryStatus();
            }

            session()->flash('message', 'Order status successfully updated to shipped.');

        } catch (\Exception $e) {
            \Log::error('Error updating order status', [
                'order_id' => $this->order->id,
                'vendor_id' => $vendor->id,
                'error' => $e->getMessage()
            ]);

            session()->flash('error', 'An error occurred while updating the order status. Please try again.');
        }

        // Refresh component state
        $this->order = $this->order->fresh();
    }

    /**
     * Check ShipBubble delivery status and auto-complete if delivered
     */
    private function checkShipBubbleDeliveryStatus()
    {
        try {
            $shipBubbleService = app(ShipBubbleService::class);

            // Extract tracking code from URL or use order reference
            $trackingCode = $this->order->shipping_provider_order_id ?? $this->order->order_number;

            if ($trackingCode) {
                $trackingResult = $shipBubbleService->trackShipment($trackingCode);

                if ($trackingResult && isset($trackingResult['status']) && $trackingResult['status'] === 'success') {
                    $deliveryStatus = $trackingResult['data']['status'] ?? '';

                    // If package is delivered, automatically mark as completed
                    if (in_array(strtolower($deliveryStatus), ['delivered', 'completed'])) {
                        $this->order->update([
                            'status' => 'completed',
                            'delivered_at' => now()
                        ]);

                        session()->flash('message', 'Order automatically marked as completed based on delivery confirmation from ShipBubble.');

                        \Log::info('Order auto-completed via ShipBubble', [
                            'order_id' => $this->order->id,
                            'tracking_code' => $trackingCode,
                            'delivery_status' => $deliveryStatus
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            // Don't fail the status update if ShipBubble check fails
            \Log::warning('ShipBubble delivery status check failed', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.vendor.orders.show');
    }
}
