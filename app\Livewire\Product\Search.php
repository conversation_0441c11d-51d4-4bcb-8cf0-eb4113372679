<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Search extends Component
{
    use WithPagination;

    #[Url(as: 'q', except: '')]
    public $query = '';

    public function render()
    {
        // SECURITY FIX: Sanitize and validate search query
        $searchQuery = $this->sanitizeSearchQuery($this->query);

        $products = collect();

        if (!empty($searchQuery)) {
            $products = Product::query()
                ->where('is_active', true)
                ->where(function ($q) use ($searchQuery) {
                    // SECURITY FIX: Use parameterized queries to prevent SQL injection
                    $q->where('name', 'like', '%' . $searchQuery . '%')
                      ->orWhere('description', 'like', '%' . $searchQuery . '%')
                      ->orWhereHas('category', function ($categoryQuery) use ($searchQuery) {
                          $categoryQuery->where('name', 'like', '%' . $searchQuery . '%');
                      })
                      ->orWhereHas('brand', function ($brandQuery) use ($searchQuery) {
                          $brandQuery->where('name', 'like', '%' . $searchQuery . '%');
                      });
                })
                ->with(['vendor', 'category', 'brand'])
                ->latest()
                ->paginate(12);
        } else {
            // Return empty paginated collection for empty search
            $products = Product::query()->where('id', 0)->paginate(12);
        }

        return view('livewire.product.search', [
            'products' => $products,
            'searchQuery' => $searchQuery,
        ]);
    }

    /**
     * SECURITY FIX: Sanitize search query to prevent SQL injection
     */
    private function sanitizeSearchQuery(string $query): string
    {
        // Remove any potentially dangerous characters
        $query = trim($query);

        // Remove SQL injection patterns
        $query = preg_replace('/[\'";\\\\]/', '', $query);

        // Remove excessive whitespace
        $query = preg_replace('/\s+/', ' ', $query);

        // Limit length to prevent abuse
        $query = substr($query, 0, 100);

        // Remove HTML tags
        $query = strip_tags($query);

        return $query;
    }
}
