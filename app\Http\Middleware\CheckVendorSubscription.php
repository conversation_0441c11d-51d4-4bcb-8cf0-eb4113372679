<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckVendorSubscription
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Ensure user is authenticated and is a vendor
        if (!Auth::check() || !Auth::user()->vendor) {
            // Not a vendor or not logged in, so middleware doesn't apply
            return $next($request);
        }

        $vendor = Auth::user()->vendor;
        $subscription = $vendor->subscription; // Uses the relationship: latest subscription with its plan

        // Check subscription status and handle accordingly
        if (!$subscription) {
            return redirect()->route('pricing')
                ->with('error', 'You do not have an active subscription. Please choose a plan to continue.');
        }

        // Handle different subscription statuses
        switch ($subscription->status) {
            case 'past_due':
                // Allow access but show warning
                session()->flash('warning', 'Your subscription payment is overdue. Please update your payment method to avoid service interruption.');
                break;

            case 'cancelled':
                return redirect()->route('vendor.subscription.index')
                    ->with('error', 'Your subscription has been cancelled. Please reactivate to continue using our services.');

            case 'expired':
                return redirect()->route('pricing')
                    ->with('error', 'Your subscription has expired. Please choose a plan to continue.');

            case 'inactive':
                return redirect()->route('pricing')
                    ->with('error', 'Your subscription is inactive. Please choose a plan to continue.');

            case 'active':
                // Check if subscription has actually expired
                if ($subscription->ends_at < now()) {
                    return redirect()->route('pricing')
                        ->with('error', 'Your subscription has expired. Please choose a plan to continue.');
                }
                break;

            default:
                return redirect()->route('pricing')
                    ->with('error', 'Invalid subscription status. Please choose a plan to continue.');
        }

        $plan = $subscription->plan;

        // Check Product Limit on relevant routes (e.g., creating a new product)
        if ($request->routeIs('vendor.products.create')) {
            if ($plan->product_limit !== null && $vendor->products()->count() >= $plan->product_limit) {
                return redirect()->route('pricing')
                    ->with('error', 'You have reached your product limit. Please upgrade your plan to add more products.');
            }
        }

        // BUSINESS LOGIC FIX: Enhanced Order Limit enforcement with configurable actions
        if ($plan->order_limit !== null) {
            $orderCountThisCycle = $vendor->orders()
                ->where('created_at', '>=', $subscription->starts_at)
                ->count();

            if ($orderCountThisCycle >= $plan->order_limit) {
                // Get enforcement configuration (can be set in config or subscription plan)
                $enforcementLevel = $plan->enforcement_level ?? config('brandify.subscription.enforcement_level', 'warning');

                switch ($enforcementLevel) {
                    case 'strict':
                        // Hard limit: Disable vendor products and redirect to upgrade
                        $vendor->products()->update(['is_active' => false]);

                        \Log::warning('Vendor order limit exceeded - products disabled', [
                            'vendor_id' => $vendor->id,
                            'order_count' => $orderCountThisCycle,
                            'order_limit' => $plan->order_limit,
                            'plan_name' => $plan->name
                        ]);

                        return redirect()->route('pricing')
                            ->with('error', 'Your order limit has been exceeded. Your products have been temporarily disabled. Please upgrade your plan to continue receiving orders.');

                    case 'grace':
                        // Grace period: Allow orders but charge extra fees
                        $graceOrders = $orderCountThisCycle - $plan->order_limit;
                        $graceFee = $graceOrders * ($plan->overage_fee ?? 500); // Default ₦500 per extra order

                        session()->flash('warning', "You have exceeded your order limit by {$graceOrders} orders. An overage fee of ₦{$graceFee} will be charged. Please upgrade your plan.");

                        \Log::info('Vendor in grace period for order limit', [
                            'vendor_id' => $vendor->id,
                            'grace_orders' => $graceOrders,
                            'grace_fee' => $graceFee
                        ]);
                        break;

                    case 'warning':
                    default:
                        // Warning only: Show persistent warning message
                        session()->flash('warning', 'You have reached your monthly order limit. Please upgrade your plan to continue receiving new orders without interruption.');

                        // Log for analytics
                        \Log::info('Vendor order limit reached - warning shown', [
                            'vendor_id' => $vendor->id,
                            'order_count' => $orderCountThisCycle,
                            'order_limit' => $plan->order_limit
                        ]);
                        break;
                }
            } elseif ($orderCountThisCycle >= ($plan->order_limit * 0.8)) {
                // Early warning at 80% of limit
                $remaining = $plan->order_limit - $orderCountThisCycle;
                session()->flash('info', "You have {$remaining} orders remaining in your current plan. Consider upgrading to avoid interruption.");
            }
        }

        return $next($request);
    }
}
