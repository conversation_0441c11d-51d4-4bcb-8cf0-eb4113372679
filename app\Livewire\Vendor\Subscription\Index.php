<?php

namespace App\Livewire\Vendor\Subscription;

use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use App\Services\PaystackService;
use App\Services\SubscriptionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Index extends Component
{
    public $subscription;
    public $subscriptionStatus;
    public $standardPlan;
    public $allPlans;
    protected $paystackService;
    protected $subscriptionService;

    public function boot(PaystackService $paystackService, SubscriptionService $subscriptionService)
    {
        $this->paystackService = $paystackService;
        $this->subscriptionService = $subscriptionService;
    }

    public function mount()
    {
        $vendor = Auth::user()->vendor;
        $this->subscription = $vendor->subscription;
        $this->subscriptionStatus = $this->subscriptionService->getSubscriptionStatus($vendor);
        $this->standardPlan = $this->subscriptionService->ensureStandardPlan();

        // Ensure all subscription plans are created
        $this->subscriptionService->ensureAllPlans();

        // Get all available subscription plans ordered by price
        $this->allPlans = SubscriptionPlan::where('status', 'active')
            ->where('name', '!=', 'Free Plan') // Exclude free plan as it's not purchasable
            ->orderBy('price')
            ->get();
    }

    public function subscribeToStandard()
    {
        $vendor = Auth::user()->vendor;
        $user = Auth::user();

        // Check if vendor has exceeded free tier
        $orderCount = $vendor->orders()->count();
        if ($orderCount < 10) {
            session()->flash('info', 'You still have ' . (10 - $orderCount) . ' free orders remaining. You can subscribe anytime.');
            return;
        }

        // Initialize Paystack transaction for standard plan
        $reference = 'sub_' . Str::random(10);

        $data = [
            'email' => $user->email,
            'amount' => (int) round($this->standardPlan->price * 100), // Convert naira to kobo for Paystack
            'reference' => $reference,
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'plan_id' => $this->standardPlan->id,
                'vendor_id' => $vendor->id,
                'user_id' => $user->id,
                'subscription_type' => 'standard'
            ]
        ];

        $result = $this->paystackService->initializeTransaction($data);

        if ($result && $result['status']) {
            return $this->redirect($result['data']['authorization_url']);
        }

        session()->flash('error', $result['message'] ?? 'Could not initialize subscription. Please try again.');
    }

    public function cancel()
    {
        $vendor = Auth::user()->vendor;
        $subscription = $vendor->subscription;

        if ($subscription) {
            // This would ideally interact with Paystack's API to cancel the subscription
            // For now, we'll just mark it as cancelled locally.
            $subscription->update(['status' => 'cancelled']);
            session()->flash('success', 'Your subscription has been cancelled.');
        } else {
            session()->flash('error', 'You do not have an active subscription.');
        }
        
        return $this->redirect(route('vendor.subscription.index'), navigate: true);
    }

    public function subscribeToPlan($planId)
    {
        $vendor = Auth::user()->vendor;
        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($planId);

        // Check if vendor has exceeded free tier for paid plans
        $orderCount = $vendor->orders()->count();
        if ($orderCount < 10 && $plan->price > 0) {
            session()->flash('info', 'You still have ' . (10 - $orderCount) . ' free orders remaining. You can subscribe anytime.');
            return;
        }

        // Initialize Paystack transaction for the selected plan
        $reference = 'sub_' . Str::random(10);

        $data = [
            'email' => $user->email,
            'amount' => (int) round($plan->price * 100), // Convert naira to kobo for Paystack
            'reference' => $reference,
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'plan_id' => $plan->id,
                'vendor_id' => $vendor->id,
                'user_id' => $user->id,
                'subscription_type' => strtolower(str_replace(' ', '_', $plan->name))
            ]
        ];

        $result = $this->paystackService->initializeTransaction($data);

        if ($result && $result['status']) {
            return $this->redirect($result['data']['authorization_url']);
        }

        session()->flash('error', $result['message'] ?? 'Could not initialize subscription. Please try again.');
    }

    public function isCurrentPlan($planId)
    {
        return $this->subscription &&
               $this->subscription->subscription_plan_id == $planId &&
               $this->subscription->status === 'active';
    }

    /**
     * Cancel current subscription
     */
    public function cancelSubscription()
    {
        $user = Auth::user();
        $vendor = $user->vendor;
        $subscriptionService = app(\App\Services\SubscriptionService::class);

        if ($subscriptionService->cancelSubscription($vendor)) {
            session()->flash('success', 'Your subscription has been cancelled. You can continue using the service until the end of your billing period.');
        } else {
            session()->flash('error', 'Unable to cancel subscription. Please contact support.');
        }
    }

    /**
     * Upgrade to a different plan
     */
    public function upgradePlan($planId)
    {
        $user = Auth::user();
        $vendor = $user->vendor;
        $plan = SubscriptionPlan::find($planId);

        if (!$plan) {
            session()->flash('error', 'Invalid plan selected.');
            return;
        }

        // Initialize payment for the new plan
        $this->subscribeToPlan($planId);
    }

    /**
     * Reactivate cancelled subscription
     */
    public function reactivateSubscription()
    {
        $user = Auth::user();
        $vendor = $user->vendor;
        $currentSubscription = $vendor->subscription;

        if ($currentSubscription && $currentSubscription->status === 'cancelled') {
            // Redirect to plan selection
            $this->subscribeToStandard();
        } else {
            session()->flash('error', 'No cancelled subscription found.');
        }
    }

    public function render()
    {
        $user = Auth::user();
        $vendor = $user->vendor;
        $orderCount = $vendor->orders()->count();

        // Get current subscription
        $currentSubscription = $vendor->subscription;

        // Get all available plans
        $plans = SubscriptionPlan::where('status', 'active')->get();

        // Calculate subscription status
        $subscriptionStatus = 'none';
        if ($currentSubscription) {
            if ($currentSubscription->status === 'active' && $currentSubscription->ends_at > now()) {
                $subscriptionStatus = 'active';
            } elseif ($currentSubscription->status === 'past_due') {
                $subscriptionStatus = 'past_due';
            } elseif ($currentSubscription->status === 'cancelled') {
                $subscriptionStatus = 'cancelled';
            } else {
                $subscriptionStatus = 'expired';
            }
        }

        return view('livewire.vendor.subscription.index', [
            'orderCount' => $orderCount,
            'currentSubscription' => $currentSubscription,
            'plans' => $plans,
            'standardPlan' => $this->standardPlan,
            'subscriptionStatus' => $subscriptionStatus,
        ]);
    }
}
