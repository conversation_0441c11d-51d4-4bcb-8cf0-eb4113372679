<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class UserController extends Controller
{
    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        // Load roles for dropdown
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * SECURITY FIX: Secure user deletion with proper authorization and data integrity checks
     */
    public function destroy(User $user)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // SECURITY FIX: Additional gate-based authorization
        if (!Gate::allows('manage-users')) {
            abort(403, 'Unauthorized to delete users.');
        }

        // SECURITY FIX: Prevent deletion of admin users by non-super-admin
        if ($user->isAdmin() && Auth::id() === $user->id) {
            return redirect()->route('admin.users.index')
                ->with('error', 'You cannot delete your own admin account.');
        }

        // BUSINESS LOGIC FIX: Check for dependent data before deletion
        $hasVendorAccount = $user->vendor()->exists();
        $hasActiveOrders = $user->orders()->whereIn('status', ['pending', 'processing', 'shipped'])->exists();

        if ($hasVendorAccount || $hasActiveOrders) {
            $reasons = [];
            if ($hasVendorAccount) $reasons[] = 'vendor account';
            if ($hasActiveOrders) $reasons[] = 'active orders';

            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete user with ' . implode(', ', $reasons) . '. Please resolve these first.');
        }

        // SECURITY FIX: Handle deletion with proper logging
        DB::transaction(function () use ($user) {
            // Log the deletion attempt for audit trail
            \Log::warning('User deletion initiated by admin', [
                'admin_user_id' => Auth::id(),
                'admin_email' => Auth::user()->email,
                'target_user_id' => $user->id,
                'target_user_email' => $user->email,
                'target_user_role' => $user->getRoleName(),
                'timestamp' => now()
            ]);

            // Delete the user
            $user->delete();

            \Log::info('User successfully deleted by admin', [
                'admin_user_id' => Auth::id(),
                'deleted_user_id' => $user->id,
                'timestamp' => now()
            ]);
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }
}
