@extends('layouts.app')

@section('content')
<div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-8">My Account</h1>

    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <div class="lg:col-span-4 mb-8 lg:mb-0">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 rounded-full bg-black text-white flex items-center justify-center text-2xl font-bold mr-4">
                        {{ auth()->user()->initials() }}
                    </div>
                    <div>
                        <h5 class="font-bold text-lg">{{ auth()->user()->name }}</h5>
                        <p class="text-gray-500">{{ auth()->user()->email }}</p>
                    </div>
                </div>

                <nav class="space-y-1">
                    {{-- CRITICAL FIX NA2: Added wire:navigate for consistent SPA-like navigation --}}
                    <a href="{{ route('dashboard') }}" wire:navigate class="{{ request()->routeIs('dashboard') ? 'bg-black text-white' : 'text-black hover:bg-gray-100' }} group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-tachometer-alt mr-3"></i> Dashboard
                    </a>
                    <a href="{{ route('orders.index') }}" wire:navigate class="{{ request()->routeIs('orders.index') || request()->routeIs('orders.show') ? 'bg-black text-white' : 'text-black hover:bg-gray-100' }} group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-shopping-bag mr-3"></i> My Orders
                    </a>
                    <a href="{{ route('wishlist.index') }}" wire:navigate class="{{ request()->routeIs('wishlist.index') ? 'bg-black text-white' : 'text-black hover:bg-gray-100' }} group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-heart mr-3"></i> My Wishlist
                    </a>
                    <a href="{{ route('settings.profile') }}" wire:navigate class="{{ request()->routeIs('settings.profile') ? 'bg-black text-white' : 'text-black hover:bg-gray-100' }} group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-user-cog mr-3"></i> Account Settings
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="w-full text-left text-red-600 hover:bg-gray-100 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                            <i class="fas fa-sign-out-alt mr-3"></i> Logout
                        </button>
                    </form>
                </nav>
            </div>
        </div>

        <div class="lg:col-span-8">
            @yield('customer-content')
        </div>
    </div>
</div>
@endsection
