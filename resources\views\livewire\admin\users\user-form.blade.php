<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $user && $user->exists ? 'Edit User' : 'Add New User' }}</h1>
            <a href="{{ route('admin.users.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Back to Users</a>
        </div>

        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">User Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <x-input wire:model.defer="name" label="Full Name" placeholder="Enter user's full name" />
                            <x-input wire:model.defer="email" label="Email Address" type="email" placeholder="Enter user's email" />
                            <x-input wire:model.defer="password" label="Password" type="password" placeholder="Leave blank to keep current password" />
                            <x-input wire:model.defer="password_confirmation" label="Confirm Password" type="password" placeholder="Confirm new password" />
                        </div>
                    </div>
                </div>

                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Assign Role</h2>
                        <div class="space-y-4">
                            @foreach($allRoles as $id => $roleName)
                                <div class="flex items-center">
                                    <input type="radio" wire:model.defer="selectedRole" id="role_{{ $id }}" value="{{ $id }}" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                    <label for="role_{{ $id }}" class="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {{ ucfirst($roleName) }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        @error('selectedRole')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    {{ $user && $user->exists ? 'Update User' : 'Create User' }}
                </button>
            </div>
        </form>
    </div>
</div>
