/**
 * Chart.js Configuration for Vendor Dashboard
 * Optimized for black & white aesthetic with minimal color usage for emphasis
 */

// Set default chart options to match Brandify design system
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                usePointStyle: true,
                boxWidth: 8,
                font: {
                    size: 11,
                    family: "'Inter', sans-serif"
                }
            }
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            bodyFont: {
                size: 12,
                family: "'Inter', sans-serif"
            },
            titleFont: {
                size: 13,
                family: "'Inter', sans-serif"
            },
            padding: 10,
            cornerRadius: 3
        }
    },
    scales: {
        x: {
            ticks: {
                font: {
                    size: 10,
                    family: "'Inter', sans-serif"
                },
                maxRotation: 45,
                minRotation: 0
            },
            grid: {
                display: false
            },
            // Limit the maximum number of ticks to prevent overcrowding
            afterFit: function(scale) {
                if (scale.ticks.length > 10) {
                    const originalTicks = [...scale.ticks];
                    const step = Math.ceil(originalTicks.length / 10);
                    scale.ticks = originalTicks.filter((_, i) => i % step === 0);
                }
            }
        },
        y: {
            ticks: {
                font: {
                    size: 10,
                    family: "'Inter', sans-serif"
                },
                // Add maximum ticks to prevent chart from going too high
                count: 8,
                precision: 0
            },
            grid: {
                color: 'rgba(0, 0, 0, 0.05)',
                borderDash: [2, 2]
            }
        }
    }
};

// Revenue chart configuration
function createRevenueChart(elementId, data) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Default data if none provided
    if (!data) {
        data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Revenue',
                data: [1200, 1900, 1600, 2500, 3200, 3800],
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                borderColor: '#000',
                borderWidth: 2,
                pointBackgroundColor: '#000',
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        };
    }
    
    const options = {
        ...defaultChartOptions,
        plugins: {
            ...defaultChartOptions.plugins,
            title: {
                display: false,
                text: 'Monthly Revenue'
            }
        }
    };
    
    return new Chart(ctx, {
        type: 'line',
        data: data,
        options: options
    });
}

// Order count chart configuration
function createOrdersChart(elementId, data) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Default data if none provided
    if (!data) {
        data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Orders',
                data: [45, 62, 58, 75, 83, 96],
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 1,
                borderRadius: 4
            }]
        };
    }
    
    const options = {
        ...defaultChartOptions,
        plugins: {
            ...defaultChartOptions.plugins,
            title: {
                display: false,
                text: 'Order Count'
            }
        }
    };
    
    return new Chart(ctx, {
        type: 'bar',
        data: data,
        options: options
    });
}

// Product category distribution chart
function createCategoryChart(elementId, data) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Default data if none provided
    if (!data) {
        data = {
            labels: ['Electronics', 'Clothing', 'Home', 'Beauty', 'Books'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    'rgba(0, 0, 0, 0.8)',
                    'rgba(0, 0, 0, 0.6)',
                    'rgba(0, 0, 0, 0.4)',
                    'rgba(0, 0, 0, 0.2)',
                    'rgba(0, 0, 0, 0.1)'
                ],
                borderWidth: 1,
                borderColor: '#fff'
            }]
        };
    }
    
    const options = {
        ...defaultChartOptions,
        plugins: {
            ...defaultChartOptions.plugins,
            title: {
                display: false,
                text: 'Product Categories'
            }
        }
    };
    
    return new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: options
    });
}

// Customer growth chart
function createCustomerGrowthChart(elementId, data) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Default data if none provided
    if (!data) {
        data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'New Customers',
                data: [15, 22, 18, 27, 33, 39],
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                borderColor: '#000',
                borderWidth: 2,
                pointBackgroundColor: '#000',
                tension: 0.3
            }, {
                label: 'Returning Customers',
                data: [30, 40, 38, 48, 50, 57],
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                borderColor: '#666',
                borderWidth: 2,
                pointBackgroundColor: '#666',
                borderDash: [5, 5],
                tension: 0.3
            }]
        };
    }
    
    const options = {
        ...defaultChartOptions,
        plugins: {
            ...defaultChartOptions.plugins,
            title: {
                display: false,
                text: 'Customer Growth'
            }
        }
    };
    
    return new Chart(ctx, {
        type: 'line',
        data: data,
        options: options
    });
}
