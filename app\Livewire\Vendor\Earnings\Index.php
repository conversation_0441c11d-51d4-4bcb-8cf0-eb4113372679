<?php

namespace App\Livewire\Vendor\Earnings;

use App\Models\Order;
use App\Models\Withdrawal;
use App\Services\PaystackService;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

#[Layout('layouts.vendor')]
class Index extends Component
{
    use WithPagination;

    public $balance = 0;
    public $withdrawableBalance = 0;
    public $pendingBalance = 0;
    public $totalEarnings = 0;
    public $totalWithdrawn = 0;
    public $banks = [];

    // Withdrawal form fields
    public $amount;
    public $bank_code;
    public $account_name;
    public $account_number;
    public $verifying_account = false;
    public $account_verified = false;

    protected $paystackService;
    protected $shipBubbleService;

    public function boot(PaystackService $paystackService, ShipBubbleService $shipBubbleService)
    {
        $this->paystackService = $paystackService;
        $this->shipBubbleService = $shipBubbleService;
    }

    public function mount()
    {
        $this->authorize('viewFinances', Auth::user()->vendor);

        $this->loadEarningsData();
        $this->loadBanks();
    }

    public function loadEarningsData()
    {
        $vendor = Auth::user()->vendor;

        // FINANCIAL LOGIC FIX: Use persistent balance fields instead of complex calculations
        // This ensures consistency and better performance
        $this->totalEarnings = $vendor->total_earnings;
        $this->balance = $vendor->balance;
        $this->totalWithdrawn = $vendor->withdrawn_amount;

        // FINANCIAL LOGIC FIX: Use available balance for withdrawals
        // This respects the 7-14 day hold period for new earnings
        $this->withdrawableBalance = $vendor->available_balance;

        // Additional financial state information
        $this->pendingBalance = $vendor->pending_balance;

        // SECURITY FIX: Log financial data access for audit trail
        \Log::info('Vendor earnings data loaded', [
            'vendor_id' => $vendor->id,
            'total_earnings' => $this->totalEarnings,
            'available_balance' => $this->withdrawableBalance,
            'pending_balance' => $this->pendingBalance,
            'total_withdrawn' => $this->totalWithdrawn,
            'last_balance_update' => $vendor->last_balance_update,
        ]);
    }

    public function loadBanks()
    {
        $bankListResponse = $this->paystackService->getBankList();
        if ($bankListResponse['status']) {
            $this->banks = $bankListResponse['data'];
        }
    }

    public function withdraw()
    {
        $vendor = Auth::user()->vendor;

        // SECURITY FIX: Centralize authorization with VendorPolicy
        $this->authorize('createWithdrawal', $vendor);

        // SECURITY FIX: Add rate limiting to prevent abuse
        $this->throttleWithdrawalRequests();

        $this->loadEarningsData(); // Recalculate just before validation

        // CODE QUALITY: Refactor complex delivery check logic
        if (!$this->verifyDeliveryStatusForWithdrawableBalance($vendor)) {
            return; // Stop if delivery checks fail
        }

        $this->validate([
            'amount' => ['required', 'numeric', 'min:1000', function ($attribute, $value, $fail) {
                if ($value > $this->withdrawableBalance) {
                    $fail('The withdrawal amount exceeds your available balance from completed orders.');
                }
            }],
            'bank_code' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'account_number' => 'required|string|digits:10',
        ]);

        $bankName = '';
        if (!empty($this->banks)) {
            $selectedBank = collect($this->banks)->firstWhere('code', $this->bank_code);
            if ($selectedBank) {
                $bankName = $selectedBank['name'];
            }
        }

        if (empty($bankName)) {
            session()->flash('error', 'Invalid bank selected.');
            return;
        }

        Withdrawal::create([
            'vendor_id' => $vendor->id,
            'amount' => $this->amount,
            'method' => 'bank_transfer',
            'details' => [
                'bank_code' => $this->bank_code,
                'bank_name' => $bankName,
                'account_name' => $this->account_name,
                'account_number' => $this->account_number,
            ],
            'status' => 'pending',
        ]);

        $this->reset(['amount', 'bank_code', 'account_name', 'account_number', 'verifying_account', 'account_verified']);
        $this->loadEarningsData();
        
        session()->flash('success', 'Your withdrawal request has been submitted and is pending approval.');

        $this->dispatch('close-modal');

        // Clear rate limiter on success
        RateLimiter::clear('withdraw-attempt:' . Auth::id());
    }

    /**
     * Auto-verify account when account number and bank code are provided
     */
    public function updatedAccountNumber()
    {
        $this->verifyAccount();
    }

    public function updatedBankCode()
    {
        $this->verifyAccount();
    }

    private function verifyAccount()
    {
        // Reset verification state
        $this->account_verified = false;
        $this->account_name = '';

        // Only verify if we have both bank code and a 10-digit account number
        if (empty($this->bank_code) || empty($this->account_number) || strlen($this->account_number) !== 10) {
            return;
        }

        try {
            $this->verifying_account = true;

            // Call Paystack API to verify account
            $response = $this->paystackService->resolveAccountName($this->account_number, $this->bank_code);

            if ($response && isset($response['data']['account_name'])) {
                $this->account_name = $response['data']['account_name'];
                $this->account_verified = true;

                // Show success message
                $this->dispatch('account-verified', [
                    'message' => 'Account verified successfully!',
                    'account_name' => $this->account_name
                ]);
            } else {
                $this->dispatch('account-verification-failed', [
                    'message' => 'Could not verify account. Please check your account number and bank.'
                ]);
            }
        } catch (\Exception $e) {
            $this->dispatch('account-verification-failed', [
                'message' => 'Account verification failed. Please check your details and try again.'
            ]);
        } finally {
            $this->verifying_account = false;
        }
    }

    private function throttleWithdrawalRequests()
    {
        $key = 'withdraw-attempt:' . Auth::id();
        $maxAttempts = 5; // Max 5 attempts in 15 minutes

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            throw ValidationException::withMessages([
                'amount' => "Too many withdrawal attempts. Please try again in {$seconds} seconds.",
            ]);
        }

        RateLimiter::hit($key, 60 * 15);
    }

    private function verifyDeliveryStatusForWithdrawableBalance($vendor): bool
    {
        $relevantOrders = $vendor->orders()
            ->where('payment_status', 'paid')
            ->whereIn('status', ['processing', 'shipped', 'completed'])
            ->whereNotNull('shipping_provider_order_id')
            ->get();

        foreach ($relevantOrders as $order) {
            $trackingCode = $order->shipping_provider_order_id;
            if (!$trackingCode) {
                continue;
            }

            $deliveryStatus = $this->shipBubbleService->getShipmentTracking($trackingCode);

            if (!$deliveryStatus['status'] || !in_array($deliveryStatus['data']['status'], ['delivered', 'completed'])) {
                if ($order->created_at->diffInDays(now()) < 7) {
                    session()->flash('error', "Unable to verify delivery status for recent order #{$order->order_number}. Please try again later or contact support.");
                    return false;
                }
            }
        }

        return true;
    }

    public function render()
    {
        $transactions = Auth::user()->vendor->transactions()->latest()->paginate(15);

        return view('livewire.vendor.earnings.index', [
            'transactions' => $transactions,
        ]);
    }
}
