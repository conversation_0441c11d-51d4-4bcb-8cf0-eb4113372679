<?php

namespace App\Livewire;

use Livewire\Component;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class ProductCard extends Component
{
    public \App\Models\Product $product;

    public $loadingCart = false;
    public $loadingWishlist = false;
    public $inWishlist = false;

    protected $listeners = ['cartUpdated' => '$refresh'];

    public function mount(\App\Models\Product $product)
    {
        // CRITICAL FIX: Add null checks and error handling
        if (!$product || !$product->exists) {
            throw new \Exception('Product not found or invalid');
        }

        $this->product = $product;

        try {
            $this->inWishlist = $this->checkWishlist();
        } catch (\Exception $e) {
            \Log::error('Error checking wishlist status', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
            $this->inWishlist = false;
        }
    }

    public function addToCart()
    {
        // CRITICAL FIX: Validate product exists and is accessible
        if (!$this->product || !$this->product->exists) {
            $this->dispatch('toast', message: 'Product not found.', type: 'error');
            return;
        }

        // DEBUGGING: Log add-to-cart attempts for troubleshooting
        \Log::info('ProductCard addToCart called', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
        ]);

        $this->loadingCart = true;

        // Check if product is active and in stock
        if (!$this->product->is_active) {
            $this->loadingCart = false;
            $this->dispatch('toast', message: 'This product is currently unavailable.', type: 'error');
            \Log::warning('Add to cart failed - product inactive', ['product_id' => $this->product->id]);
            return;
        }

        if (!$this->product->hasSufficientStock(1)) {
            $this->loadingCart = false;
            $this->dispatch('toast', message: 'This product is out of stock.', type: 'error');
            return;
        }

        // If the product has variants, redirect to the product page to select options.
        // If the product has variants, show the quick view modal.
        if ($this->product->variants()->exists() && $this->product->variants()->count() > 0) {
            $this->loadingCart = false;
            $this->dispatch('show-quick-view', productId: $this->product->id);
            return;
        }

        // If no variants, add directly to cart.
        $cart = session()->get('cart', []);
        $cartId = $this->product->id;

        if (isset($cart[$cartId])) {
            // LOGIC-HIGH-001 FIX: Use standardized stock validation method
            $newQuantity = $cart[$cartId]['quantity'] + 1;
            if (!$this->product->hasSufficientStock($newQuantity)) {
                $availableStock = $this->product->getStockLevel();
                $this->loadingCart = false;
                $this->dispatch('toast', message: "Cannot add more items. Only {$availableStock} units available.", type: 'error');
                return;
            }
            $cart[$cartId]['quantity'] = $newQuantity;
        } else {
            $cart[$cartId] = [
                'id' => $this->product->id,
                'product_id' => $this->product->id,
                'name' => $this->product->name,
                'quantity' => 1,
                'price' => $this->product->getCurrentPrice(), // CRITICAL FIX: Use getCurrentPrice() to handle discounts
                'image_url' => $this->product->image_url,
                'vendor_id' => $this->product->vendor_id,
                'attributes' => []
            ];
        }

        session()->put('cart', $cart);

        // Get updated cart count for proper counter updates
        $cartCount = count($cart);

        // Dispatch multiple events to ensure cart counter updates properly
        $this->dispatch('cartUpdated');
        $this->dispatch('cart-updated-total', $cartCount);

        // Force refresh of cart counter component
        $this->dispatch('$refresh')->to('cart-counter');

        $this->dispatch('toast', message: 'Product added to cart!', type: 'success');
        $this->loadingCart = false;

        // DEBUGGING: Log successful cart addition
        \Log::info('ProductCard addToCart successful', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'cart_total_items' => count($cart),
            'user_id' => auth()->id(),
        ]);
    }

    public function toggleWishlist()
    {
        $this->loadingWishlist = true;

        try {
            // CRITICAL FIX: Validate product exists and is accessible
            if (!$this->product || !$this->product->exists) {
                $this->dispatch('toast', message: 'Product not found.', type: 'error');
                return;
            }

            if (!Auth::check()) {
                $this->loadingWishlist = false;
                $this->dispatch('toast', message: 'Please login to manage your wishlist.', type: 'info');
                return $this->redirect(route('login'));
            }

            $user = Auth::user();
            $existingWishlistItem = $user->wishlist()->where('product_id', $this->product->id)->first();

            if ($existingWishlistItem) {
                // Remove from wishlist
                $existingWishlistItem->delete();
                $this->inWishlist = false;
                $this->loadingWishlist = false;
                $this->dispatch('wishlistUpdated');
                $this->dispatch('toast', message: 'Product removed from wishlist!', type: 'success');
            } else {
                // Add to wishlist
                $user->wishlist()->create(['product_id' => $this->product->id]);
                $this->inWishlist = true;
                $this->loadingWishlist = false;
                $this->dispatch('wishlistUpdated');
                $this->dispatch('toast', message: 'Product added to wishlist!', type: 'success');
            }

        } catch (\Exception $e) {
            \Log::error('Error toggling wishlist', [
                'product_id' => $this->product->id ?? 'unknown',
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            $this->dispatch('toast', message: 'An error occurred. Please try again.', type: 'error');
            $this->loadingWishlist = false;
        }
    }

    public function addToWishlist()
    {
        $this->toggleWishlist();
    }

    public function checkWishlist()
    {
        if (!Auth::check()) return false;
        return Auth::user()->wishlist()->where('product_id', $this->product->id)->exists();
    }

    public function render()
    {
        return view('livewire.product-card');
    }
}
