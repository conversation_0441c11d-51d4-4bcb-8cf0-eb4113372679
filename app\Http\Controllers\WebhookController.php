<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\Vendor\SubscriptionController;

class WebhookController extends Controller
{
    protected $paymentController;
    protected $subscriptionController;

    public function __construct(
        PaymentController $paymentController,
        SubscriptionController $subscriptionController
    ) {
        $this->paymentController = $paymentController;
        $this->subscriptionController = $subscriptionController;
    }

    /**
     * Handle all Paystack webhooks in a single consolidated endpoint
     * This fixes the critical issue where Paystack only allows one webhook URL per application
     */
    public function handlePaystackWebhook(Request $request)
    {
        // Log incoming webhook for debugging
        Log::info('Consolidated Paystack webhook received', [
            'event' => $request->input('event'),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString()
        ]);

        // Verify the webhook signature first
        if (!$this->verifyPaystackSignature($request)) {
            Log::warning('Consolidated Paystack webhook: Invalid signature', [
                'ip' => $request->ip(),
                'event' => $request->input('event'),
                'signature_provided' => $request->header('x-paystack-signature') ? 'yes' : 'no'
            ]);
            return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 401);
        }

        $event = $request->input('event');
        
        Log::info('Processing Paystack webhook event', [
            'event' => $event,
            'reference' => $request->input('data.reference', 'unknown')
        ]);

        // Route events to appropriate handlers
        try {
            return match($event) {
                // Payment events - delegate to PaymentController
                'charge.success' => $this->delegateToPaymentController($request),
                
                // Subscription events - delegate to SubscriptionController
                'subscription.create',
                'subscription.disable', 
                'invoice.payment_failed',
                'subscription.not_renew' => $this->delegateToSubscriptionController($request),
                
                // Handle unknown events gracefully
                default => $this->handleUnknownEvent($event)
            };
        } catch (\Exception $e) {
            Log::error('Consolidated Paystack webhook: Error processing event', [
                'event' => $event,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error', 
                'message' => 'Webhook processing failed'
            ], 500);
        }
    }

    /**
     * Verify Paystack webhook signature
     */
    protected function verifyPaystackSignature(Request $request): bool
    {
        $signature = $request->header('x-paystack-signature');
        $webhookSecret = config('services.paystack.secret');

        if (!$webhookSecret) {
            Log::error('Consolidated Paystack webhook: Secret key not configured');
            return false;
        }

        if (!$signature) {
            Log::warning('Consolidated Paystack webhook: No signature provided');
            return false;
        }

        $computedSignature = hash_hmac('sha512', $request->getContent(), $webhookSecret);
        
        $isValid = hash_equals($signature, $computedSignature);
        
        if (!$isValid) {
            Log::warning('Consolidated Paystack webhook: Signature mismatch', [
                'provided_length' => strlen($signature),
                'computed_length' => strlen($computedSignature),
                'content_length' => strlen($request->getContent())
            ]);
        }

        return $isValid;
    }

    /**
     * Delegate payment events to PaymentController
     */
    protected function delegateToPaymentController(Request $request)
    {
        Log::info('Delegating payment event to PaymentController', [
            'event' => $request->input('event'),
            'reference' => $request->input('data.reference', 'unknown')
        ]);

        return $this->paymentController->handlePaystackWebhook($request);
    }

    /**
     * Delegate subscription events to SubscriptionController
     */
    protected function delegateToSubscriptionController(Request $request)
    {
        Log::info('Delegating subscription event to SubscriptionController', [
            'event' => $request->input('event'),
            'subscription_code' => $request->input('data.subscription_code', 'unknown')
        ]);

        return $this->subscriptionController->handleWebhook($request);
    }

    /**
     * Handle unknown webhook events gracefully
     */
    protected function handleUnknownEvent(string $event)
    {
        Log::warning('Consolidated Paystack webhook: Unhandled event type', [
            'event' => $event,
            'timestamp' => now()->toISOString()
        ]);

        // Return success to prevent Paystack from retrying unknown events
        return response()->json([
            'status' => 'ignored',
            'message' => "Event '{$event}' not handled by this application"
        ], 200);
    }

    /**
     * Test endpoint to verify webhook connectivity
     * This replaces the old separate test endpoint
     */
    public function testWebhook(Request $request)
    {
        Log::info('Consolidated Paystack webhook test endpoint called', [
            'method' => $request->method(),
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString(),
            'user_agent' => $request->userAgent()
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Consolidated webhook endpoint is reachable',
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'webhook_url' => route('paystack.webhook'),
            'supported_events' => [
                'charge.success',
                'subscription.create',
                'subscription.disable',
                'invoice.payment_failed',
                'subscription.not_renew'
            ]
        ]);
    }
}
