<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;

class ProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Authorization is handled by policies in the controller
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products,slug',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
            'sku' => 'nullable|string|max:255|unique:products,sku',
            'barcode' => 'nullable|string|max:255',
            'quantity' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'nullable|string|in:kg,g,lb,oz',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ];

        // For update requests, adjust unique validation rules
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $product = $this->route('product');
            if ($product) {
                $rules['slug'] = 'required|string|max:255|unique:products,slug,' . $product->id;
                $rules['sku'] = 'nullable|string|max:255|unique:products,sku,' . $product->id;
            }
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Product name is required.',
            'slug.required' => 'Product slug is required.',
            'slug.unique' => 'This product slug is already taken.',
            'description.required' => 'Product description is required.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'The selected category is invalid.',
            'price.required' => 'Product price is required.',
            'price.numeric' => 'Product price must be a number.',
            'price.min' => 'Product price must be at least 0.',
            'compare_price.numeric' => 'Compare price must be a number.',
            'compare_price.min' => 'Compare price must be at least 0.',
            'cost_per_item.numeric' => 'Cost per item must be a number.',
            'cost_per_item.min' => 'Cost per item must be at least 0.',
            'sku.unique' => 'This SKU is already taken.',
            'quantity.required' => 'Product quantity is required.',
            'quantity.integer' => 'Product quantity must be a whole number.',
            'quantity.min' => 'Product quantity must be at least 0.',
            'images.*.image' => 'Each file must be an image.',
            'images.*.mimes' => 'Only JPEG, PNG, JPG, and GIF images are allowed.',
            'images.*.max' => 'Each image must not exceed 2MB.',
        ];
    }
}
