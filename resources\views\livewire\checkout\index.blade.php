<div class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="max-w-2xl mx-auto pt-8 sm:pt-12 lg:pt-16 pb-16 sm:pb-20 lg:pb-24 px-4 sm:px-6 lg:max-w-7xl lg:px-8 xl:max-w-8xl 2xl:max-w-screen-2xl">
        <!-- Header Section -->
        <div class="text-center mb-8 sm:mb-12">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">Secure Checkout</h1>
            <p class="text-gray-600 text-sm sm:text-base">Complete your order safely and securely</p>

            <!-- Progress Indicator -->
            <div class="flex items-center justify-center mt-6 space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Shipping</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Payment</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Complete</span>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        @if($errors->any())
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Please fix the following errors:
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                        @if($errors->has('stock') || $errors->has('price') || $errors->has('availability'))
                            <div class="mt-4">
                                <button
                                    wire:click="refreshCart"
                                    class="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                    <span wire:loading.remove wire:target="refreshCart">Refresh Cart</span>
                                    <span wire:loading wire:target="refreshCart">Refreshing...</span>
                                </button>
                                <p class="text-xs text-red-600 mt-1">This will update prices and remove unavailable items.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        <!-- Cart Updated Message -->
        @if(session('cart_updated'))
            <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Cart Updated</h3>
                        <p class="text-sm text-blue-700 mt-1">{{ session('cart_updated') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Debug Information (remove in production) -->
        @if(config('app.debug'))
            <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="text-sm font-medium text-blue-800 mb-2">Debug Information:</h3>
                <div class="text-xs text-blue-700 space-y-1">
                    <p>Cart Items: {{ count($cartItems) }}</p>
                    <p>Address Complete: {{ $this->isAddressComplete() ? 'Yes' : 'No' }}</p>
                    <p>Shipping Rates Available: {{ count($shippingRates) }}</p>
                    <p>Selected Shipping Rate: {{ $selectedShippingRate ? 'Yes' : 'No' }}</p>
                    @if(!$this->isAddressComplete())
                        <p>Missing Fields: {{ implode(', ', $this->getMissingAddressFields()) }}</p>
                    @endif
                </div>
            </div>
        @endif

        <!-- Success Messages -->
        @if(session()->has('success'))
            <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            {{ session('success') }}
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <div class="lg:grid lg:grid-cols-12 lg:gap-x-8 xl:gap-x-12 2xl:gap-x-16">
            <!-- Shipping and Payment Information -->
            <div class="lg:col-span-7 xl:col-span-8 order-2 lg:order-1 mt-8 lg:mt-0">
                <!-- Shipping Information Card -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 mb-6">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center mr-4">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Shipping Information</h2>
                            <p class="text-gray-600 text-sm">Where should we deliver your order?</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
                        <!-- Address Field -->
                        <div class="sm:col-span-2">
                            <label for="address" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    Street Address *
                                </span>
                            </label>
                            <div class="relative">
                                <input
                                    type="text"
                                    id="address"
                                    wire:model.live.debounce.500ms="shippingAddress.address"
                                    placeholder="Enter your full street address"
                                    class="block w-full border-2 rounded-xl shadow-sm text-sm sm:text-base py-4 px-4 pr-12 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white {{ $errors->has('shippingAddress.address') ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-200 focus:ring-2 focus:ring-black focus:border-black' }}"
                                    aria-describedby="address-error"
                                    required>
                                <!-- Validation Icon -->
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    @if($errors->has('shippingAddress.address'))
                                        <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    @elseif(!empty($shippingAddress['address']) && !$errors->has('shippingAddress.address'))
                                        <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            @error('shippingAddress.address')
                                <div class="flex items-center mt-2 text-red-600" id="address-error">
                                    <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <!-- City Field -->
                        <div>
                            <label for="city" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    City *
                                </span>
                            </label>
                            <div class="relative">
                                <input
                                    type="text"
                                    id="city"
                                    wire:model.live.debounce.500ms="shippingAddress.city"
                                    placeholder="Enter your city"
                                    class="block w-full border-2 rounded-xl shadow-sm text-sm sm:text-base py-4 px-4 pr-12 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white {{ $errors->has('shippingAddress.city') ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-200 focus:ring-2 focus:ring-black focus:border-black' }}"
                                    aria-describedby="city-error"
                                    required>
                                <!-- Validation Icon -->
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    @if($errors->has('shippingAddress.city'))
                                        <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    @elseif(!empty($shippingAddress['city']) && !$errors->has('shippingAddress.city'))
                                        <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            @error('shippingAddress.city')
                                <div class="flex items-center mt-2 text-red-600" id="city-error">
                                    <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <!-- Phone Number Field -->
                        <div>
                            <label for="phone" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    Phone Number *
                                </span>
                            </label>
                            <div class="relative">
                                <input
                                    type="tel"
                                    id="phone"
                                    wire:model.live.debounce.500ms="shippingAddress.phone"
                                    placeholder="Enter your phone number (e.g., 08012345678)"
                                    class="block w-full border-2 rounded-xl shadow-sm text-sm sm:text-base py-4 px-4 pr-12 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white {{ $errors->has('shippingAddress.phone') ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-200 focus:ring-2 focus:ring-black focus:border-black' }}"
                                    aria-describedby="phone-error"
                                    pattern="[0-9]{11}"
                                    required>
                                <!-- Validation Icon -->
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    @if($errors->has('shippingAddress.phone'))
                                        <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    @elseif(!empty($shippingAddress['phone']) && !$errors->has('shippingAddress.phone'))
                                        <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            @error('shippingAddress.phone')
                                <div class="flex items-center mt-2 text-red-600" id="phone-error">
                                    <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <!-- Postal Code Field -->
                        <div>
                            <label for="postal_code" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z"></path>
                                    </svg>
                                    Postal Code
                                </span>
                            </label>
                            <input
                                type="text"
                                id="postal_code"
                                wire:model.defer="shippingAddress.postal_code"
                                placeholder="Enter postal code"
                                class="block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black text-sm sm:text-base py-4 px-4 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white">
                            @error('shippingAddress.postal_code')
                                <div class="flex items-center mt-2 text-red-600">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                        <!-- State Selection -->
                        <div class="sm:col-span-2">
                            <label for="state" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                    </svg>
                                    State *
                                </span>
                            </label>
                            <div class="relative">
                                <select
                                    id="state"
                                    wire:model.live="shippingAddress.state"
                                    wire:key="state-dropdown"
                                    class="block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black text-sm sm:text-base py-4 px-4 pr-10 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white appearance-none"
                                    required>
                                    <option value="">Choose your state</option>
                                    @if(!empty($states) && is_array($states))
                                        @foreach($states as $state => $stateLgas)
                                            <option value="{{ $state }}" {{ $shippingAddress['state'] === $state ? 'selected' : '' }}>{{ $state }}</option>
                                        @endforeach
                                    @endif
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                            @error('shippingAddress.state')
                                <div class="flex items-center mt-2 text-red-600">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>



                        <!-- LGA Selection -->
                        <div class="sm:col-span-2">
                            <label for="lga" class="block text-sm font-semibold text-gray-900 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Local Government Area *
                                </span>
                            </label>
                            <div class="relative">
                                <select
                                    id="lga"
                                    wire:model.live="shippingAddress.lga"
                                    wire:key="lga-{{ $shippingAddress['state'] ?? 'no-state' }}"
                                    class="block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black text-sm sm:text-base py-4 px-4 pr-10 transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white appearance-none {{ empty($lgas) ? 'opacity-50 cursor-not-allowed' : '' }}"
                                    {{ empty($lgas) ? 'disabled' : 'required' }}>
                                    <option value="">
                                        @if(empty($lgas))
                                            Please select a state first
                                        @else
                                            Choose your LGA
                                        @endif
                                    </option>
                                    @if(!empty($lgas) && is_array($lgas))
                                        @foreach($lgas as $lga)
                                            <option value="{{ $lga }}" {{ $shippingAddress['lga'] === $lga ? 'selected' : '' }}>{{ $lga }}</option>
                                        @endforeach
                                    @endif
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    @if($loadingLgas)
                                        <svg class="animate-spin w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            @error('shippingAddress.lga')
                                <div class="flex items-center mt-2 text-red-600">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-xs font-medium">{{ $message }}</span>
                                </div>
                            @enderror
                        </div>

                    <div class="sm:col-span-2">
                        @if(!$this->isAddressComplete())
                            <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800">Complete Your Address for Shipping Rates</h3>
                                        <p class="text-sm text-blue-700 mt-1">Fill in your delivery address to get shipping rates. Customer details are only required for payment.</p>
                                        @if(!empty($this->getMissingAddressFields()))
                                            <p class="text-xs text-blue-600 mt-1">Missing address fields: {{ implode(', ', $this->getMissingAddressFields()) }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        {{-- FIXED: Use simple HTML button with loading state --}}
                        <button
                            wire:click="getShippingRates"
                            class="w-full bg-black text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-800 hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            {{ !$this->isAddressComplete() ? 'disabled' : '' }}>
                            <span wire:loading.remove wire:target="getShippingRates">
                                {{ $this->isAddressComplete() ? 'Get Shipping Rates' : 'Complete Address First' }}
                            </span>
                            <span wire:loading wire:target="getShippingRates" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Loading...
                            </span>
                        </button>
                    </div>
                    @error('shipping') <span class="sm:col-span-2 text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Shipping Method -->
                @if(!empty($shippingRates))
                    <div class="mt-8">
                        <!-- Fallback Notification -->
                        @if(session('shipping_fallback'))
                            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-yellow-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Estimated Shipping Rates</h3>
                                        <p class="text-sm text-yellow-700 mt-1">{{ session('shipping_fallback') }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Choose Shipping Method</h2>
                                    <p class="text-gray-600 text-sm">Select your preferred delivery option</p>
                                </div>
                            </div>

                            <fieldset>
                                <legend class="sr-only">Shipping method</legend>
                                <div class="space-y-4">
                                    @foreach($shippingRates as $key => $rate)
                                        @php
                                            $rate = is_array($rate) ? $rate : [];
                                            $selectedRate = is_array($selectedShippingRate) ? $selectedShippingRate : [];
                                            $rateCourierId = isset($rate['courier_id']) ? $rate['courier_id'] : (isset($rate['id']) ? $rate['id'] : '');
                                            $selectedCourierId = isset($selectedRate['courier_id']) ? $selectedRate['courier_id'] : (isset($selectedRate['id']) ? $selectedRate['id'] : '');
                                            $isSelected = $selectedShippingRate && $selectedCourierId == $rateCourierId;

                                            // Extract shipping details with enhanced information
                                            $courierName = isset($rate['courier_name']) ? $rate['courier_name'] : (isset($rate['name']) ? $rate['name'] : 'Shipping Option');
                                            $deliveryTime = isset($rate['estimated_delivery']) ? $rate['estimated_delivery'] : (isset($rate['delivery_time']) ? $rate['delivery_time'] : (isset($rate['duration']) ? $rate['duration'] : 'Standard delivery'));
                                            $shippingCost = isset($rate['total']) ? $rate['total'] : (isset($rate['fee']) ? $rate['fee'] : 0);
                                            $description = isset($rate['description']) ? $rate['description'] : '';
                                            $features = isset($rate['features']) ? $rate['features'] : [];
                                        @endphp

                                        <label
                                            wire:click="selectShippingRate('{{ $key }}')"
                                            class="relative border-2 rounded-xl p-6 flex cursor-pointer transition-all duration-300 hover:shadow-md {{ $isSelected ? 'border-black bg-gray-50 shadow-md' : 'border-gray-200 hover:border-gray-300' }}">
                                            <input type="radio" name="shipping-method" value="{{ $key }}" class="sr-only">

                                            <!-- Shipping Icon -->
                                            <div class="flex-shrink-0 mr-4">
                                                <div class="w-12 h-12 {{ $isSelected ? 'bg-black text-white' : 'bg-gray-100 text-gray-600' }} rounded-full flex items-center justify-center transition-colors duration-300">
                                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                                    </svg>
                                                </div>
                                            </div>

                                            <!-- Shipping Details -->
                                            <div class="flex-1">
                                                <div class="flex items-start justify-between">
                                                    <div class="flex-1">
                                                        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $courierName }}</h3>

                                                        @if($description)
                                                            <p class="text-sm text-gray-600 mb-2">{{ $description }}</p>
                                                        @endif

                                                        <div class="flex items-center text-sm text-gray-500 mb-3">
                                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                            {{ $deliveryTime }}
                                                        </div>

                                                        <!-- Enhanced Features -->
                                                        <div class="flex flex-wrap gap-2">
                                                            @if(!empty($features))
                                                                @foreach(array_slice($features, 0, 4) as $feature)
                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                        </svg>
                                                                        {{ $feature }}
                                                                    </span>
                                                                @endforeach
                                                            @else
                                                                <!-- Fallback features for legacy rates -->
                                                                @if(isset($rate['tracking_available']) && $rate['tracking_available'])
                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                        </svg>
                                                                        Tracking Available
                                                                    </span>
                                                                @endif

                                                                @if(isset($rate['insurance_available']) && $rate['insurance_available'])
                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                                                        </svg>
                                                                        Insured
                                                                    </span>
                                                                @endif
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Price -->
                                                    <div class="text-right ml-4">
                                                        <div class="text-2xl font-bold text-gray-900">₦{{ number_format($shippingCost, 2) }}</div>
                                                        @if(isset($rate['original_price']) && $rate['original_price'] > $shippingCost)
                                                            <div class="text-sm text-gray-500 line-through">₦{{ number_format($rate['original_price'], 2) }}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Selection Indicator -->
                                            @if($isSelected)
                                                <div class="absolute top-4 right-4">
                                                    <div class="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center">
                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            @endif
                                        </label>
                                    @endforeach
                                </div>
                            </fieldset>
                        </div>
                    </div>
                @endif

                <!-- Payment Section -->
                <div class="mt-8">
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center mr-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Payment Information</h2>
                                <p class="text-gray-600 text-sm">Secure payment powered by Paystack</p>
                            </div>
                        </div>

                        @if($selectedShippingRate)
                            <!-- Payment Method Info -->
                            <div class="mb-6 p-4 bg-gray-50 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://paystack.com/assets/img/logo/paystack-icon-blue.png" alt="Paystack" class="w-8 h-8 mr-3">
                                        <div>
                                            <h3 class="font-semibold text-gray-900">Paystack Payment</h3>
                                            <p class="text-sm text-gray-600">Secure payment with cards, bank transfer, and more</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-green-600">SSL Secured</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Total Summary -->
                            <div class="mb-6 p-4 border border-gray-200 rounded-xl">
                                <h3 class="font-semibold text-gray-900 mb-3">Order Total</h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="font-medium">₦{{ number_format($subtotal, 2) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Shipping ({{ $selectedShippingRate['courier_name'] ?? 'Selected Method' }})</span>
                                        <span class="font-medium">₦{{ number_format($shippingCost, 2) }}</span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-2 mt-2">
                                        <div class="flex justify-between text-lg font-bold">
                                            <span>Total</span>
                                            <span>₦{{ number_format($total, 2) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Button -->
                            <button
                                wire:click="proceedToPayment"
                                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                :disabled="$processingPayment">
                                <span wire:loading.remove wire:target="proceedToPayment" class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    Proceed to Secure Payment (₦{{ number_format($total, 2) }})
                                </span>
                                <span wire:loading wire:target="proceedToPayment" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing Payment...
                                </span>
                            </button>

                            <!-- Security Notice -->
                            <div class="mt-4 flex items-center justify-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                Your payment information is encrypted and secure
                            </div>
                        @else
                            <!-- No Shipping Method Selected -->
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Select Shipping Method</h3>
                                <p class="text-gray-600">Please choose a shipping method above to proceed with payment.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Order summary -->
            <div class="lg:col-span-5 xl:col-span-4 mt-10 lg:mt-0">
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 sticky top-8">
                    <!-- Header -->
                    <div class="px-6 py-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center mr-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">Order Summary</h2>
                                <p class="text-sm text-gray-600">{{ count($cartItems) }} {{ count($cartItems) === 1 ? 'item' : 'items' }} in your cart</p>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Items -->
                    <div class="px-4 sm:px-6 py-4 max-h-80 overflow-y-auto">
                        <h3 class="sr-only">Items in your cart</h3>
                        <div class="space-y-3 sm:space-y-4">
                            @foreach($cartItems as $productId => $item)
                                @php
                                    $item = is_array($item) ? $item : [];
                                    $imageUrl = isset($item['image_url']) ? $item['image_url'] : asset('img/default-product.png');
                                    $itemName = isset($item['name']) ? $item['name'] : 'Product';
                                    $itemQuantity = isset($item['quantity']) ? $item['quantity'] : 1;
                                    $itemPrice = isset($item['price']) ? $item['price'] : 0;

                                    // Check if vendor has subscription issues
                                    $product = \App\Models\Product::find($productId);
                                    $hasVendorIssue = false;
                                    $vendorIssueMessage = '';

                                    if ($product && $product->vendor) {
                                        $subscriptionService = app(\App\Services\SubscriptionService::class);
                                        if (!$subscriptionService->canReceiveOrders($product->vendor)) {
                                            $hasVendorIssue = true;
                                            $vendorIssueMessage = 'This vendor cannot currently process orders';
                                        }
                                    }
                                @endphp
                                <div class="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gray-50 rounded-xl {{ $hasVendorIssue ? 'border-2 border-red-200 bg-red-50' : '' }}">
                                    <div class="flex-shrink-0">
                                        <img src="{{ $imageUrl }}" alt="{{ $itemName }}" class="w-14 h-14 sm:w-16 sm:h-16 rounded-lg object-cover {{ $hasVendorIssue ? 'opacity-60' : '' }}">
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm sm:text-base font-semibold text-gray-900 line-clamp-2 {{ $hasVendorIssue ? 'text-red-700' : '' }}">{{ $itemName }}</h4>

                                        <!-- Mobile: Stack quantity and price -->
                                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-1 space-y-1 sm:space-y-0">
                                            <span class="text-xs sm:text-sm text-gray-500">Qty: {{ $itemQuantity }}</span>
                                            <span class="text-sm sm:text-base font-bold {{ $hasVendorIssue ? 'text-red-700' : 'text-gray-900' }}">@currency($itemPrice * $itemQuantity)</span>
                                        </div>

                                        @if(isset($item['vendor_name']))
                                            <p class="text-xs text-gray-400 mt-1">by {{ $item['vendor_name'] }}</p>
                                        @endif

                                        @if($hasVendorIssue)
                                            <div class="mt-2 flex items-start space-x-2">
                                                <svg class="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                                <p class="text-xs text-red-600 leading-tight">{{ $vendorIssueMessage }}</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- Order Totals -->
                    <div class="border-t border-gray-200 px-6 py-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="font-medium text-gray-900">@currency($subtotal)</span>
                            </div>

                            @if($selectedShippingRate)
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Shipping</span>
                                    <span class="font-medium text-gray-900">@currency($shippingCost)</span>
                                </div>
                                <div class="text-xs text-gray-500">
                                    via {{ $selectedShippingRate['courier_name'] ?? 'Selected Method' }}
                                </div>
                            @else
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Shipping</span>
                                    <span class="text-gray-500 italic">To be calculated</span>
                                </div>
                            @endif

                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-lg font-bold text-gray-900">Total</span>
                                    <span class="text-lg font-bold text-gray-900">@currency($total)</span>
                                </div>
                                @if($total > 0)
                                    <p class="text-xs text-gray-500 mt-1">Including all taxes and fees</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                    <div class="border-t border-gray-200 py-6 px-4 sm:px-6">
                        <button
                            class="w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-xl font-semibold cursor-not-allowed"
                            disabled>
                            Place Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


