{"resources/views/admin/dashboard.blade.php": {"type": "blade", "layout": "layouts.admin", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": null}, "resources/views/admin/profile.blade.php": {"type": "blade", "layout": "layouts.admin", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection"], "linked_php_class": null}, "resources/views/components/action-message.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["on"], "slots": [], "sections": [], "directives": ["@props", "@if", "@endif"], "linked_php_class": null}, "resources/views/components/app-logo-icon.blade.php": {"type": "component", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/app-logo.blade.php": {"type": "component", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/auth-header.blade.php": {"type": "component", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/auth-session-status.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["status"], "slots": [], "sections": [], "directives": ["@props", "@if", "@endif"], "linked_php_class": null}, "resources/views/components/button.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["type", "class"], "slots": ["slot"], "sections": [], "directives": ["@props"], "linked_php_class": null}, "resources/views/components/button/circle.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["size", "variant", "icon", "loading", "disabled"], "slots": [], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif", "@class"], "linked_php_class": null}, "resources/views/components/button/danger.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["type", "class", "loading", "disabled"], "slots": ["slot"], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif", "@class"], "linked_php_class": null}, "resources/views/components/button/primary.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["type", "class", "loading", "disabled", "size"], "slots": ["slot"], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif", "@class"], "linked_php_class": null}, "resources/views/components/button/secondary.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["type", "class", "loading", "disabled"], "slots": ["slot"], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif", "@class"], "linked_php_class": null}, "resources/views/components/image/responsive.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["src", "alt", "class", "loading", "sizes", "width", "height"], "slots": [], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif"], "linked_php_class": null}, "resources/views/components/input.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["disabled", "type", "class"], "slots": [], "sections": [], "directives": ["@props", "@php", "@endphp"], "linked_php_class": null}, "resources/views/components/layouts/app.blade.php": {"type": "layout", "layout": null, "includes": [], "props": [], "slots": ["slot"], "sections": [], "directives": ["@livewireStyles", "@livewireScripts", "@stack"], "linked_php_class": null}, "resources/views/components/layouts/app/header.blade.php": {"type": "partial", "layout": null, "includes": ["components.outline.search"], "props": [], "slots": [], "sections": [], "directives": ["@livewire", "@auth", "@endauth", "@guest", "@endguest", "@if", "@endif"], "linked_php_class": null}, "resources/views/components/layouts/app/sidebar.blade.php": {"type": "partial", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@auth", "@endauth", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": null}, "resources/views/components/layouts/auth.blade.php": {"type": "layout", "layout": null, "includes": [], "props": [], "slots": ["slot"], "sections": [], "directives": ["@livewireStyles", "@livewireScripts"], "linked_php_class": null}, "resources/views/components/layouts/auth/card.blade.php": {"type": "component", "layout": null, "includes": ["components.app-logo"], "props": [], "slots": ["slot"], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/layouts/auth/simple.blade.php": {"type": "component", "layout": null, "includes": ["components.app-logo"], "props": [], "slots": ["slot"], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/layouts/auth/split.blade.php": {"type": "component", "layout": null, "includes": ["components.app-logo"], "props": [], "slots": ["slot"], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/loading-spinner.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["size", "color"], "slots": [], "sections": [], "directives": ["@props", "@php", "@endphp"], "linked_php_class": null}, "resources/views/components/logo.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["class"], "slots": [], "sections": [], "directives": ["@props"], "linked_php_class": null}, "resources/views/components/modal/card.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["name", "show", "max<PERSON><PERSON><PERSON>"], "slots": ["slot"], "sections": [], "directives": ["@props", "@php", "@endphp", "@if", "@endif"], "linked_php_class": null}, "resources/views/components/outline/search.blade.php": {"type": "component", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@livewire"], "linked_php_class": null}, "resources/views/components/products/card.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["product"], "slots": [], "sections": [], "directives": ["@props", "@if", "@endif", "@foreach", "@endforeach", "@livewire"], "linked_php_class": null}, "resources/views/components/session-message.blade.php": {"type": "component", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@session"], "linked_php_class": null}, "resources/views/components/settings/layout.blade.php": {"type": "layout", "layout": "layouts.app", "includes": [], "props": [], "slots": ["slot"], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/components/textarea.blade.php": {"type": "component", "layout": null, "includes": [], "props": ["disabled", "class"], "slots": ["slot"], "sections": [], "directives": ["@props", "@php", "@endphp"], "linked_php_class": null}, "resources/views/layouts/app.blade.php": {"type": "layout", "layout": null, "includes": ["components.layouts.app.header", "layouts._footer"], "props": [], "slots": ["slot"], "sections": [], "directives": ["@vite", "@livewireStyles", "@livewireScripts", "@stack", "@yield"], "linked_php_class": null}, "resources/views/layouts/admin.blade.php": {"type": "layout", "layout": null, "includes": ["layouts.partials._admin-nav"], "props": [], "slots": ["slot"], "sections": [], "directives": ["@vite", "@livewireStyles", "@livewireScripts", "@stack", "@yield"], "linked_php_class": null}, "resources/views/layouts/vendor.blade.php": {"type": "layout", "layout": null, "includes": ["layouts.partials._vendor-nav"], "props": [], "slots": ["slot"], "sections": [], "directives": ["@vite", "@livewireStyles", "@livewireScripts", "@stack", "@yield"], "linked_php_class": null}, "resources/views/layouts/customer.blade.php": {"type": "layout", "layout": "layouts.app", "includes": [], "props": [], "slots": ["slot"], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/livewire/add-to-cart-button.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\AddToCartButton", "layout": null, "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["@if", "@else", "@endif", "wire:click", "wire:target"], "linked_php_class": "app/Livewire/AddToCartButton.php"}, "resources/views/livewire/cart-counter.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\CartCounter", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif"], "linked_php_class": "app/Livewire/CartCounter.php"}, "resources/views/livewire/search.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Search", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Search.php"}, "resources/views/livewire/product-card.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\ProductCard", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach", "wire:click", "@livewire"], "linked_php_class": "app/Livewire/ProductCard.php"}, "resources/views/livewire/checkout/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Checkout\\Index", "layout": "layouts.app", "includes": ["components.button.primary", "components.loading-spinner"], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach", "wire:model", "wire:click", "wire:submit", "wire:loading", "wire:target", "@error", "@enderror"], "linked_php_class": "app/Livewire/Checkout/Index.php"}, "resources/views/livewire/checkout/success.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Checkout\\Success", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Checkout/Success.php"}, "resources/views/livewire/cart/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Cart\\Index", "layout": "layouts.app", "includes": ["components.button.primary", "components.button.secondary", "components.products.card"], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach", "wire:click", "wire:model", "@currency"], "linked_php_class": "app/Livewire/Cart/Index.php"}, "resources/views/livewire/dashboard/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Dashboard\\Index", "layout": "layouts.app", "includes": ["dashboard.partials._stats-cards", "dashboard.partials._recent-orders", "dashboard.partials._recommended-products"], "props": [], "slots": [], "sections": [], "directives": ["@livewire"], "linked_php_class": "app/Livewire/Dashboard/Index.php"}, "resources/views/livewire/dashboard/map-widget.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Dashboard\\MapWidget", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif"], "linked_php_class": "app/Livewire/Dashboard/MapWidget.php"}, "resources/views/livewire/contact-form.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\ContactForm", "layout": null, "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading"], "linked_php_class": "app/Livewire/ContactForm.php"}, "resources/views/livewire/debug-checkout.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\DebugCheckout", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/DebugCheckout.php"}, "resources/views/livewire/auth/login.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\Login", "layout": "components.layouts.auth.split", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading", "@if", "@endif"], "linked_php_class": "app/Livewire/Auth/Login.php"}, "resources/views/livewire/auth/register.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\Register", "layout": "components.layouts.auth.split", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading"], "linked_php_class": "app/Livewire/Auth/Register.php"}, "resources/views/livewire/auth/forgot-password.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\ForgotPassword", "layout": "components.layouts.auth.simple", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading", "@session"], "linked_php_class": "app/Livewire/Auth/ForgotPassword.php"}, "resources/views/livewire/auth/reset-password.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\ResetPassword", "layout": "components.layouts.auth.simple", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading"], "linked_php_class": "app/Livewire/Auth/ResetPassword.php"}, "resources/views/livewire/auth/verify-email.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\VerifyEmail", "layout": "components.layouts.auth.simple", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:click", "wire:loading", "@if", "@endif", "@session"], "linked_php_class": "app/Livewire/Auth/VerifyEmail.php"}, "resources/views/livewire/auth/confirm-password.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\ConfirmPassword", "layout": "components.layouts.auth.simple", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading"], "linked_php_class": "app/Livewire/Auth/ConfirmPassword.php"}, "resources/views/livewire/auth/vendor/register.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Auth\\Vendor\\Register", "layout": "components.layouts.auth.simple", "includes": ["components.input", "components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading"], "linked_php_class": "app/Livewire/Auth/Vendor/Register.php"}, "resources/views/livewire/vendor/dashboard.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Dashboard", "layout": "layouts.vendor", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@livewire", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Dashboard.php"}, "resources/views/livewire/vendor/products/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Products\\Index", "layout": "layouts.vendor", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "wire:click", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Products/Index.php"}, "resources/views/livewire/vendor/products/create-product.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Products\\CreateProduct", "layout": "layouts.vendor", "includes": ["components.button.primary", "components.input", "components.textarea"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading", "@if", "@endif"], "linked_php_class": "app/Livewire/Vendor/Products/CreateProduct.php"}, "resources/views/livewire/vendor/products/edit-product.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Products\\EditProduct", "layout": "layouts.vendor", "includes": ["components.button.primary", "components.input", "components.textarea"], "props": [], "slots": [], "sections": [], "directives": ["wire:submit", "wire:model", "@error", "@enderror", "wire:loading", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Vendor/Products/EditProduct.php"}, "resources/views/livewire/vendor/orders/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Orders\\Index", "layout": "layouts.vendor", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Orders/Index.php"}, "resources/views/livewire/vendor/orders/show.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Orders\\Show", "layout": "layouts.vendor", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:click", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Orders/Show.php"}, "resources/views/livewire/vendor/earnings/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Earnings\\Index", "layout": "layouts.vendor", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:click", "wire:model", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Earnings/Index.php"}, "resources/views/livewire/vendor/subscription/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Vendor\\Subscription\\Index", "layout": "layouts.vendor", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:click", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Vendor/Subscription/Index.php"}, "resources/views/livewire/admin/dashboard.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Admin\\Dashboard", "layout": "layouts.admin", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@livewire", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Admin/Dashboard.php"}, "resources/views/livewire/admin/products/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Admin\\Products\\Index", "layout": "layouts.admin", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "wire:click", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Admin/Products/Index.php"}, "resources/views/livewire/admin/orders/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Admin\\Orders\\Index", "layout": "layouts.admin", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Admin/Orders/Index.php"}, "resources/views/livewire/admin/vendors/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Admin\\Vendors\\Index", "layout": "layouts.admin", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "wire:click", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Admin/Vendors/Index.php"}, "resources/views/livewire/admin/users/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Admin\\Users\\Index", "layout": "layouts.admin", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "wire:click", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Admin/Users/<USER>"}, "resources/views/livewire/product/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Product\\Index", "layout": "layouts.app", "includes": ["components.products.card"], "props": [], "slots": [], "sections": [], "directives": ["wire:model", "@if", "@endif", "@foreach", "@endforeach", "@livewire"], "linked_php_class": "app/Livewire/Product/Index.php"}, "resources/views/livewire/product/show.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Product\\Show", "layout": "layouts.app", "includes": ["components.button.primary"], "props": [], "slots": [], "sections": [], "directives": ["@livewire", "@if", "@endif", "@foreach", "@endforeach"], "linked_php_class": "app/Livewire/Product/Show.php"}, "resources/views/livewire/orders/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Orders\\Index", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach", "@currency"], "linked_php_class": "app/Livewire/Orders/Index.php"}, "resources/views/livewire/wishlist/index.blade.php": {"type": "livewire_component", "class": "App\\Livewire\\Wishlist\\Index", "layout": "layouts.app", "includes": ["components.products.card"], "props": [], "slots": [], "sections": [], "directives": ["@if", "@endif", "@foreach", "@endforeach", "wire:click"], "linked_php_class": "app/Livewire/Wishlist/Index.php"}, "resources/views/layouts/_footer.blade.php": {"type": "partial", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": [], "linked_php_class": null}, "resources/views/layouts/partials/_admin-nav.blade.php": {"type": "partial", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@auth", "@endauth", "@if", "@endif"], "linked_php_class": null}, "resources/views/layouts/partials/_vendor-nav.blade.php": {"type": "partial", "layout": null, "includes": [], "props": [], "slots": [], "sections": [], "directives": ["@auth", "@endauth", "@if", "@endif"], "linked_php_class": null}, "resources/views/welcome.blade.php": {"type": "blade", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection", "@livewire"], "linked_php_class": null}, "resources/views/home.blade.php": {"type": "blade", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection", "@livewire"], "linked_php_class": null}, "resources/views/about.blade.php": {"type": "blade", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection"], "linked_php_class": null}, "resources/views/contact.blade.php": {"type": "blade", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection", "@livewire"], "linked_php_class": null}, "resources/views/pricing.blade.php": {"type": "blade", "layout": "layouts.app", "includes": [], "props": [], "slots": [], "sections": ["content"], "directives": ["@section", "@endsection", "@livewire"], "linked_php_class": null}}