<?php

namespace Database\Factories;

use App\Models\Brand;
 use Illuminate\Database\Eloquent\Factories\Factory;

 /**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->paragraph,
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'image_url' => $this->faker->imageUrl(640, 480, 'products', true),
            'brand_id' => Brand::inRandomOrder()->first()?->id ?? null,
            'stock' => $this->faker->numberBetween(10, 100),
        ];
    }
}
