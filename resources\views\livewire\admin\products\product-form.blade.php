<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $product && $product->exists ? 'Edit Product' : 'Add New Product' }}</h1>
            <a href="{{ route('admin.products.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Back to Products</a>
        </div>

        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <x-input wire:model.defer="name" label="Product Name" placeholder="Enter product name" />
                        <x-textarea wire:model.defer="description" label="Description" placeholder="Enter product description" />
                        <x-input wire:model.defer="slug" label="Slug" placeholder="enter-product-slug" />
                    </div>
                </div>

                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <x-input wire:model.defer="price" label="Price" type="number" step="0.01" placeholder="0.00" />
                        <x-input wire:model.defer="discount_price" label="Discount Price" type="number" step="0.01" placeholder="0.00" />
                        <x-input wire:model.defer="stock" label="Stock Quantity" type="number" placeholder="0" />
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 space-y-6">
                        <!-- Vendor -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Vendor</label>
                            <select wire:model.defer="vendor_id"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select a vendor</option>
                                @foreach($vendors as $vendor)
                                    <option value="{{ $vendor->id }}">{{ $vendor->shop_name }}</option>
                                @endforeach
                            </select>
                            @error('vendor_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                            <select wire:model.defer="category_id"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select a category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('category_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
                        <!-- Image URL -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Image URL</label>
                            <input type="url" wire:model.defer="image_url"
                                   placeholder="https://example.com/image.png"
                                   class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            @error('image_url') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Active Checkbox -->
                        <div class="flex items-center mt-4">
                            <input type="checkbox" wire:model.defer="is_active" id="is_active"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">Active</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    {{ $product && $product->exists ? 'Update Product' : 'Create Product' }}
                </button>
            </div>
        </form>
    </div>
</div>
