<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class PaystackService
{
    protected $secretKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret');
        $this->baseUrl = 'https://api.paystack.co';

        // SECURITY FIX: Validate that secret key exists
        if (empty($this->secretKey)) {
            throw new \InvalidArgumentException('Paystack secret key is not configured');
        }
    }

    /**
     * Initialize a transaction.
     *
     * @param array $data
     * @return array
     */
    public function initializeTransaction(array $data)
    {
        // SECURITY FIX: Comprehensive input validation
        $this->validateTransactionData($data);

        // SECURITY FIX: Sanitize and validate inputs
        $payload = [
            'email' => filter_var($data['email'], FILTER_SANITIZE_EMAIL),
            'amount' => (int) $data['amount'], // Ensure integer amount in kobo
            'reference' => preg_replace('/[^a-zA-Z0-9_-]/', '', $data['reference']),
            'callback_url' => filter_var($data['callback_url'], FILTER_SANITIZE_URL),
        ];

        // SECURITY FIX: Validate email format after sanitization
        if (!filter_var($payload['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email address provided');
        }

        // SECURITY FIX: Validate callback URL
        if (!filter_var($payload['callback_url'], FILTER_VALIDATE_URL)) {
            throw new \InvalidArgumentException('Invalid callback URL provided');
        }

        if (isset($data['plan']) && $data['plan']) {
            $payload['plan'] = preg_replace('/[^a-zA-Z0-9_-]/', '', $data['plan']);
        }

        if (isset($data['metadata']) && is_array($data['metadata'])) {
            // SECURITY FIX: Sanitize metadata
            $payload['metadata'] = $this->sanitizeMetadata($data['metadata']);
        }

        try {
            // SECURITY FIX: Add timeout and error handling
            $response = Http::timeout(30)
                ->withToken($this->secretKey)
                ->post($this->baseUrl . '/transaction/initialize', $payload);

            // SECURITY FIX: Log transaction initialization for audit trail
            \Log::info('Paystack transaction initialized', [
                'reference' => $payload['reference'],
                'amount' => $payload['amount'],
                'email' => $payload['email'],
                'status_code' => $response->status()
            ]);

            if (!$response->successful()) {
                throw new \Exception('Paystack API request failed: ' . $response->body());
            }

            return $response->json();

        } catch (\Exception $e) {
            // SECURITY FIX: Log errors for monitoring
            \Log::error('Paystack transaction initialization failed', [
                'error' => $e->getMessage(),
                'reference' => $payload['reference'] ?? 'unknown',
                'amount' => $payload['amount'] ?? 0
            ]);

            throw $e;
        }
    }

    /**
     * Verify a transaction.
     *
     * @param string $reference
     * @return array
     */
    public function verifyTransaction(string $reference)
    {
        // SECURITY FIX: Validate reference format
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $reference)) {
            throw new \InvalidArgumentException('Invalid reference format');
        }

        if (strlen($reference) > 100) {
            throw new \InvalidArgumentException('Reference is too long');
        }

        try {
            // SECURITY FIX: Add timeout and error handling
            $response = Http::timeout(30)
                ->withToken($this->secretKey)
                ->get($this->baseUrl . '/transaction/verify/' . $reference);

            // SECURITY FIX: Log verification attempts for audit trail
            \Log::info('Paystack transaction verification', [
                'reference' => $reference,
                'status_code' => $response->status(),
                'success' => $response->successful()
            ]);

            if (!$response->successful()) {
                throw new \Exception('Paystack verification request failed: ' . $response->body());
            }

            return $response->json();

        } catch (\Exception $e) {
            // SECURITY FIX: Log verification errors
            \Log::error('Paystack transaction verification failed', [
                'error' => $e->getMessage(),
                'reference' => $reference
            ]);

            throw $e;
        }
    }

    /**
     * Create a subscription plan on Paystack.
     *
     * @param array $data
     * @return array
     */
    public function createPlan(array $data)
    {
        $payload = [
            'name' => $data['name'],
            'amount' => $data['amount'], // Amount should be in kobo
            'interval' => $data['interval'], // e.g., monthly, annually
            'description' => $data['description'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/plan', $payload);

        return $response->json();
    }

    /**
     * Create a subscription on Paystack.
     *
     * @param array $data
     * @return array
     */
    public function createSubscription(array $data)
    {
        $payload = [
            'customer' => $data['customer_code'],
            'plan' => $data['plan_code'],
            'authorization' => $data['authorization_code'],
            'start_date' => $data['start_date'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/subscription', $payload);

        return $response->json();
    }

    /**
     * Cancel a subscription on Paystack.
     *
     * @param string $subscriptionCode
     * @return array
     */
    public function cancelSubscription(string $subscriptionCode)
    {
        $payload = [
            'code' => $subscriptionCode,
            'token' => $this->secretKey,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/subscription/disable', $payload);

        return $response->json();
    }

    /**
     * Get subscription details from Paystack.
     *
     * @param string $subscriptionCode
     * @return array
     */
    public function getSubscription(string $subscriptionCode)
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/subscription/' . $subscriptionCode);

        return $response->json();
    }

    /**
     * Create or get customer on Paystack.
     *
     * @param array $data
     * @return array
     */
    public function createCustomer(array $data)
    {
        $payload = [
            'email' => $data['email'],
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'phone' => $data['phone'] ?? '',
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/customer', $payload);

        return $response->json();
    }

    /**
     * Update a subscription plan on Paystack.
     *
     * @param string $planCode
     * @param array $data
     * @return array
     */
    public function updatePlan(string $planCode, array $data)
    {
        $payload = [
            'name' => $data['name'],
            'amount' => $data['amount'], // Amount should be in kobo
            'description' => $data['description'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->put($this->baseUrl . '/plan/' . $planCode, $payload);

        return $response->json();
    }

    /**
     * Create a transfer recipient.
     *
     * @param array $data
     * @return array
     */
    public function createTransferRecipient(array $data)
    {
        $payload = [
            'type' => 'nuban',
            'name' => $data['name'],
            'account_number' => $data['account_number'],
            'bank_code' => $data['bank_code'],
            'currency' => 'NGN',
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transferrecipient', $payload);

        return $response->json();
    }

    /**
     * Initiate a transfer to a recipient.
     *
     * @param array $data
     * @return array
     */
    public function initiateTransfer(array $data)
    {
        $payload = [
            'source' => 'balance',
            'amount' => $data['amount'], // Amount should be in kobo
            'recipient' => $data['recipient'], // Recipient code
            'reason' => $data['reason'],
            'reference' => $data['reference'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transfer', $payload);

        return $response->json();
    }

    /**
     * Get the list of supported banks.
     *
     * @return array
     */
    public function getBankList()
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/bank?currency=NGN');

        return $response->json();
    }

    /**
     * Create a subaccount for split payments.
     *
     * @param array $data
     * @return array
     */
    public function createSubaccount(array $data)
    {
        $payload = [
            'business_name' => $data['business_name'],
            'settlement_bank' => $data['settlement_bank'],
            'account_number' => $data['account_number'],
            'percentage_charge' => $data['percentage_charge'] ?? 0,
            'description' => $data['description'] ?? null,
            'primary_contact_email' => $data['primary_contact_email'] ?? null,
            'primary_contact_name' => $data['primary_contact_name'] ?? null,
            'primary_contact_phone' => $data['primary_contact_phone'] ?? null,
            'metadata' => $data['metadata'] ?? null,
        ];

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/subaccount', $payload);

        return $response->json();
    }

    /**
     * Update a subaccount.
     *
     * @param string $subaccountCode
     * @param array $data
     * @return array
     */
    public function updateSubaccount(string $subaccountCode, array $data)
    {
        $payload = array_filter([
            'business_name' => $data['business_name'] ?? null,
            'settlement_bank' => $data['settlement_bank'] ?? null,
            'account_number' => $data['account_number'] ?? null,
            'percentage_charge' => $data['percentage_charge'] ?? null,
            'description' => $data['description'] ?? null,
            'primary_contact_email' => $data['primary_contact_email'] ?? null,
            'primary_contact_name' => $data['primary_contact_name'] ?? null,
            'primary_contact_phone' => $data['primary_contact_phone'] ?? null,
            'metadata' => $data['metadata'] ?? null,
        ]);

        $response = Http::withToken($this->secretKey)
            ->put($this->baseUrl . '/subaccount/' . $subaccountCode, $payload);

        return $response->json();
    }

    /**
     * Get subaccount details.
     *
     * @param string $subaccountCode
     * @return array
     */
    public function getSubaccount(string $subaccountCode)
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/subaccount/' . $subaccountCode);

        return $response->json();
    }

    /**
     * Initialize transaction with split payment.
     *
     * @param array $data
     * @return array
     */
    public function initializeTransactionWithSplit(array $data)
    {
        $payload = [
            'email' => $data['email'],
            'amount' => $data['amount'], // Amount should be in kobo
            'reference' => $data['reference'],
            'callback_url' => $data['callback_url'],
        ];

        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $payload['metadata'] = $data['metadata'];
        }

        // Add split configuration
        if (isset($data['split']) && is_array($data['split'])) {
            $payload['split'] = $data['split'];
        }

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transaction/initialize', $payload);

        return $response->json();
    }

    public function resolveAccountName($accountNumber, $bankCode)
    {
        $response = \Illuminate\Support\Facades\Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/bank/resolve', [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode,
            ]);
        return $response->json();
    }

    /**
     * Calculate vendor split percentages for dynamic split payments.
     * This keeps all funds in the platform account initially.
     *
     * @param array $vendorTotals Array of vendor_id => total_amount
     * @param float $totalAmount Total transaction amount
     * @return array Split configuration for Paystack
     */
    public function calculateVendorSplits(array $vendorTotals, float $totalAmount)
    {
        $splits = [];

        foreach ($vendorTotals as $vendorId => $vendorAmount) {
            // Calculate percentage (rounded to avoid decimal issues)
            $percentage = round(($vendorAmount / $totalAmount) * 100, 2);

            // Store split info for internal tracking (not sent to Paystack)
            $splits[$vendorId] = [
                'amount' => $vendorAmount,
                'percentage' => $percentage,
            ];
        }

        return $splits;
    }

    /**
     * Initialize transaction with metadata for internal split tracking.
     * All funds go to platform account, splits are tracked internally.
     *
     * @param array $data
     * @return array
     */
    public function initializeTransactionWithInternalSplit(array $data)
    {
        $payload = [
            'email' => $data['email'],
            'amount' => $data['amount'], // Amount should be in kobo
            'reference' => $data['reference'],
            'callback_url' => $data['callback_url'],
        ];

        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $payload['metadata'] = $data['metadata'];
        }

        // No split sent to Paystack - all funds go to platform account
        // Split information is stored in metadata for internal processing

        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transaction/initialize', $payload);

        return $response->json();
    }

    /**
     * SECURITY FIX: Validate transaction data
     */
    private function validateTransactionData(array $data): void
    {
        $required = ['email', 'amount', 'reference', 'callback_url'];

        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing or empty");
            }
        }

        // Validate amount is positive integer
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new \InvalidArgumentException('Amount must be a positive number');
        }

        // Validate amount is not too large (prevent overflow)
        if ($data['amount'] > 999999999999) { // 9.9 trillion kobo = 99 billion naira
            throw new \InvalidArgumentException('Amount exceeds maximum allowed value');
        }

        // Validate reference format
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $data['reference'])) {
            throw new \InvalidArgumentException('Reference contains invalid characters');
        }

        // Validate reference length
        if (strlen($data['reference']) > 100) {
            throw new \InvalidArgumentException('Reference is too long (max 100 characters)');
        }
    }

    /**
     * SECURITY FIX: Sanitize metadata array
     */
    private function sanitizeMetadata(array $metadata): array
    {
        $sanitized = [];

        foreach ($metadata as $key => $value) {
            // Sanitize key
            $cleanKey = preg_replace('/[^a-zA-Z0-9_-]/', '', $key);

            if (is_string($value)) {
                // Sanitize string values
                $sanitized[$cleanKey] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            } elseif (is_numeric($value)) {
                // Keep numeric values
                $sanitized[$cleanKey] = $value;
            } elseif (is_array($value)) {
                // Recursively sanitize arrays (max depth 2)
                $sanitized[$cleanKey] = $this->sanitizeMetadataArray($value);
            }
        }

        return $sanitized;
    }

    /**
     * SECURITY FIX: Sanitize nested metadata arrays
     */
    private function sanitizeMetadataArray(array $array): array
    {
        $sanitized = [];

        foreach ($array as $key => $value) {
            $cleanKey = preg_replace('/[^a-zA-Z0-9_-]/', '', $key);

            if (is_string($value)) {
                $sanitized[$cleanKey] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            } elseif (is_numeric($value)) {
                $sanitized[$cleanKey] = $value;
            }
            // Don't allow deeper nesting for security
        }

        return $sanitized;
    }
}
