<?php

namespace Tests\Integration;

use App\Services\ShipBubbleService;
use App\Services\ShipBubbleCacheService;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ShipBubbleIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected ShipBubbleService $shipBubbleService;
    protected ShipBubbleCacheService $cacheService;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = new ShipBubbleCacheService();
        $this->shipBubbleService = new ShipBubbleService($this->cacheService);
        
        $this->vendor = Vendor::factory()->create([
            'business_address' => '123 Vendor Street, Lagos',
            'city' => 'Lagos',
            'state' => 'Lagos State',
            'country' => 'Nigeria',
        ]);

        // Configure ShipBubble for testing
        config([
            'services.shipbubble.key' => 'test_api_key',
            'services.shipbubble.url' => 'https://api.shipbubble.com/v1',
        ]);
    }

    /** @test */
    public function it_validates_address_successfully()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'message' => 'Address validated successfully',
                'data' => [
                    'address_code' => 'ADDR_123456',
                    'validated_address' => '123 Test Street, Lagos, Lagos State, Nigeria',
                ],
            ], 200),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('success', $result['status']);
        $this->assertArrayHasKey('data', $result);
        $this->assertEquals('ADDR_123456', $result['data']['address_code']);
    }

    /** @test */
    public function it_caches_address_validation_results()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'data' => ['address_code' => 'ADDR_123456'],
            ], 200),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        // First call should hit the API
        $result1 = $this->shipBubbleService->validateAddress($addressData);
        
        // Second call should use cache
        $result2 = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals($result1, $result2);
        
        // Verify only one HTTP request was made
        Http::assertSentCount(1);
    }

    /** @test */
    public function it_gets_shipping_rates_successfully()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'data' => ['address_code' => 'ADDR_123456'],
            ], 200),
            'api.shipbubble.com/v1/shipping/rates' => Http::response([
                'status' => 'success',
                'data' => [
                    'rates' => [
                        [
                            'service_name' => 'Standard Delivery',
                            'amount' => 1500,
                            'delivery_time' => '2-3 business days',
                        ],
                        [
                            'service_name' => 'Express Delivery',
                            'amount' => 2500,
                            'delivery_time' => '1-2 business days',
                        ],
                    ],
                ],
            ], 200),
        ]);

        $cartItems = [
            [
                'id' => 1,
                'quantity' => 2,
                'price' => 1000,
                'weight' => 0.5,
            ],
        ];

        $shippingAddress = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '456 Customer Street',
            'city' => 'Abuja',
            'state' => 'FCT',
        ];

        $result = $this->shipBubbleService->getShippingRates($cartItems, $shippingAddress, $this->vendor);

        $this->assertEquals('success', $result['status']);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('rates', $result['data']);
        $this->assertCount(2, $result['data']['rates']);
    }

    /** @test */
    public function it_caches_shipping_rates()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'data' => ['address_code' => 'ADDR_123456'],
            ], 200),
            'api.shipbubble.com/v1/shipping/rates' => Http::response([
                'status' => 'success',
                'data' => ['rates' => []],
            ], 200),
        ]);

        $cartItems = [['id' => 1, 'quantity' => 1, 'price' => 1000, 'weight' => 0.5]];
        $shippingAddress = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        // First call
        $result1 = $this->shipBubbleService->getShippingRates($cartItems, $shippingAddress, $this->vendor);
        
        // Second call should use cache
        $result2 = $this->shipBubbleService->getShippingRates($cartItems, $shippingAddress, $this->vendor);

        $this->assertEquals($result1, $result2);
        
        // Should have made 2 address validation calls and 1 rates call
        Http::assertSentCount(3); // 2 address validations + 1 rates call
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'error',
                'message' => 'Invalid address format',
            ], 400),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'phone' => 'invalid-phone',
            'address' => '',
            'city' => '',
            'state' => '',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('error', $result['status']);
        $this->assertStringContains('validation failed', $result['message']);
    }

    /** @test */
    public function it_implements_circuit_breaker_pattern()
    {
        // Simulate multiple failures to trigger circuit breaker
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([], 500),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        // Make multiple failed requests
        for ($i = 0; $i < 6; $i++) {
            $result = $this->shipBubbleService->validateAddress($addressData);
            $this->assertEquals('error', $result['status']);
        }

        // Circuit breaker should now be open, preventing further API calls
        $result = $this->shipBubbleService->validateAddress($addressData);
        $this->assertEquals('error', $result['status']);
        $this->assertStringContains('temporarily unavailable', $result['message']);
    }

    /** @test */
    public function it_retries_failed_requests()
    {
        // First request fails, second succeeds
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::sequence()
                ->push([], 500) // First attempt fails
                ->push([
                    'status' => 'success',
                    'data' => ['address_code' => 'ADDR_123456'],
                ], 200), // Second attempt succeeds
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('success', $result['status']);
        
        // Should have made 2 requests (1 failed, 1 successful)
        Http::assertSentCount(2);
    }

    /** @test */
    public function it_validates_input_data_before_api_calls()
    {
        $invalidAddressData = [
            'name' => '', // Empty name
            'email' => 'invalid-email', // Invalid email
            'phone' => '', // Empty phone
            'address' => '', // Empty address
        ];

        $result = $this->shipBubbleService->validateAddress($invalidAddressData);

        $this->assertEquals('error', $result['status']);
        $this->assertEquals('validation_error', $result['type']);
        $this->assertArrayHasKey('details', $result);
        
        // Should not make any HTTP requests for invalid data
        Http::assertNothingSent();
    }

    /** @test */
    public function it_handles_network_timeouts()
    {
        Http::fake(function () {
            throw new \Illuminate\Http\Client\ConnectionException('Connection timeout');
        });

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('error', $result['status']);
        $this->assertStringContains('network', strtolower($result['message']));
    }

    /** @test */
    public function it_logs_api_calls_and_responses()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'data' => ['address_code' => 'ADDR_123456'],
            ], 200),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        // Capture logs
        $this->expectsEvents(\Illuminate\Log\Events\MessageLogged::class);

        $this->shipBubbleService->validateAddress($addressData);

        // Verify that API call was logged
        // Note: In a real test, you'd use Log::spy() or similar to verify specific log entries
    }

    /** @test */
    public function it_respects_cache_ttl_settings()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([
                'status' => 'success',
                'data' => ['address_code' => 'ADDR_123456'],
            ], 200),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        // Make first request
        $this->shipBubbleService->validateAddress($addressData);

        // Verify cache entry exists
        $cacheKey = $this->cacheService->generateAddressCacheKey($addressData);
        $this->assertTrue(Cache::has($cacheKey));

        // Verify TTL is set correctly
        $ttl = $this->cacheService->getAddressTtl();
        $this->assertEquals(1800, $ttl); // 30 minutes
    }

    /** @test */
    public function it_handles_malformed_api_responses()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response('invalid json response', 200),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('error', $result['status']);
        $this->assertStringContains('response', strtolower($result['message']));
    }

    /** @test */
    public function it_provides_fallback_when_service_unavailable()
    {
        Http::fake([
            'api.shipbubble.com/v1/shipping/address/validate' => Http::response([], 503),
        ]);

        $addressData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos State',
        ];

        $result = $this->shipBubbleService->validateAddress($addressData);

        $this->assertEquals('error', $result['status']);
        $this->assertArrayHasKey('fallback_available', $result);
    }
}
