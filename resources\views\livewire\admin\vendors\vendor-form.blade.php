<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-3 sm:space-y-0">
            <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{{ $vendor && $vendor->exists ? 'Edit Vendor' : 'Add New Vendor' }}</h1>
            <a href="{{ route('admin.vendors.index') }}" class="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 min-h-[44px]">Back to Vendors</a>
        </div>

        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                <div class="lg:col-span-2 space-y-4 sm:space-y-6 lg:space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6">
                        <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">User Account Details</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div class="sm:col-span-2">
                                <x-input wire:model.defer="name" label="Full Name" placeholder="Enter user's full name" />
                            </div>
                            <div class="sm:col-span-2">
                                <x-input wire:model.defer="email" label="Email Address" type="email" placeholder="Enter user's email" />
                            </div>
                            <x-input wire:model.defer="password" label="Password" type="password" placeholder="Enter new password" />
                            <x-input wire:model.defer="password_confirmation" label="Confirm Password" type="password" placeholder="Confirm new password" />
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6">
                        <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Shop Details</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div class="sm:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Shop Name</label>
                                <input type="text" wire:model.defer="shop_name" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter shop name">
                                @error('shop_name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div class="sm:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Business Name</label>
                                <input type="text" wire:model.defer="business_name" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter business name">
                                @error('business_name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone</label>
                                <input type="text" wire:model.defer="phone" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter phone number">
                                @error('phone') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City</label>
                                <input type="text" wire:model.defer="city" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter city">
                                @error('city') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">State</label>
                                <input type="text" wire:model.defer="state" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter state">
                                @error('state') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country</label>
                                <input type="text" wire:model.defer="country" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter country">
                                @error('country') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                        </div>
                        <div class="mt-4 sm:mt-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Business Description</label>
                            <textarea wire:model.defer="business_description" rows="3" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter business description"></textarea>
                            @error('business_description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div class="mt-4 sm:mt-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Business Address</label>
                            <textarea wire:model.defer="business_address" rows="2" class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base" placeholder="Enter business address"></textarea>
                            @error('business_address') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6">
                        <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Storefront Settings</h2>

                        <!-- Banner Upload -->
                        <div class="mb-4 sm:mb-6">
                            <label for="banner" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Store Banner</label>
                            @if ($vendor && $vendor->banner_url)
                                <div class="mt-2 mb-2">
                                    <img src="{{ $vendor->banner_url }}" alt="Current Banner" class="h-24 sm:h-32 w-full object-cover rounded-md">
                                </div>
                            @endif
                            <div class="mt-2">
                                <input type="file" id="banner" wire:model="banner" class="block w-full text-sm text-gray-500 file:mr-4 file:py-3 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-200 file:text-gray-700 hover:file:bg-gray-300 min-h-[44px]">
                            </div>
                            @error('banner') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                        </div>

                        <!-- About Us -->
                        <div class="mb-4 sm:mb-6">
                            <label for="about" class="block text-sm font-medium text-gray-700 dark:text-gray-300">About Store</label>
                            <textarea id="about" wire:model.defer="about" rows="4" class="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base px-3 py-3"></textarea>
                            @error('about') <span class="text-red-500 text-xs mt-2">{{ $message }}</span> @enderror
                        </div>

                        <!-- Social Media Links -->
                        <div>
                            <h3 class="text-sm sm:text-md font-semibold text-gray-900 dark:text-white mb-3">Social Links</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                                <div class="sm:col-span-2 lg:col-span-1">
                                    <x-input wire:model.defer="facebook_url" label="Facebook URL" placeholder="https://facebook.com/your-page" />
                                </div>
                                <div class="sm:col-span-2 lg:col-span-1">
                                    <x-input wire:model.defer="twitter_url" label="Twitter URL" placeholder="https://twitter.com/your-handle" />
                                </div>
                                <div class="sm:col-span-2 lg:col-span-1">
                                    <x-input wire:model.defer="instagram_url" label="Instagram URL" placeholder="https://instagram.com/your-username" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-4 sm:space-y-6 lg:space-y-8">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 sm:p-6">
                        <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Status & Visibility</h2>
                        <div class="space-y-4">
                            <div class="flex items-center py-2">
                                <input type="checkbox" wire:model.defer="is_approved" id="is_approved" class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_approved" class="ml-3 block text-sm text-gray-900 dark:text-gray-300 cursor-pointer">Approved</label>
                            </div>
                            <div class="flex items-center py-2">
                                <input type="checkbox" wire:model.defer="is_featured" id="is_featured" class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_featured" class="ml-3 block text-sm text-gray-900 dark:text-gray-300 cursor-pointer">Featured</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 sm:mt-8 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                <a href="{{ route('admin.vendors.index') }}" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 min-h-[44px]">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 min-h-[44px]">
                    {{ $vendor && $vendor->exists ? 'Update Vendor' : 'Create Vendor' }}
                </button>
            </div>
        </form>
    </div>
</div>
