<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\CheckoutSession;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CheckoutSessionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_checkout_session_with_shipping_cost()
    {
        $user = User::factory()->create();

        $sessionData = [
            'transaction_id' => 'TEST-' . time(),
            'user_id' => $user->id,
            'cart_items' => [
                '1' => ['product_id' => 1, 'quantity' => 2, 'price' => 100]
            ],
            'shipping_address' => [
                'name' => 'Test User',
                'address' => '123 Test St',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria'
            ],
            'selected_shipping_rate' => [
                'courier_name' => 'Test Courier',
                'total' => 251.06
            ],
            'subtotal' => 200.00,
            'shipping_cost' => 251.06,
            'total' => 451.06,
            'vendor_splits' => []
        ];

        $checkoutSession = CheckoutSession::createSession($sessionData);

        $this->assertNotNull($checkoutSession);
        $this->assertEquals(251.06, $checkoutSession->shipping_cost);
        $this->assertEquals(200.00, $checkoutSession->subtotal);
        $this->assertEquals(451.06, $checkoutSession->total);
        $this->assertEquals('pending', $checkoutSession->status);
    }

    /** @test */
    public function it_handles_null_shipping_cost_gracefully()
    {
        $user = User::factory()->create();

        $sessionData = [
            'transaction_id' => 'TEST-NULL-' . time(),
            'user_id' => $user->id,
            'cart_items' => [],
            'shipping_address' => [],
            'selected_shipping_rate' => [],
            'subtotal' => 100.00,
            'shipping_cost' => null, // This should be handled gracefully
            'total' => 100.00,
            'vendor_splits' => []
        ];

        $checkoutSession = CheckoutSession::createSession($sessionData);

        $this->assertNotNull($checkoutSession);
        $this->assertEquals(0.00, $checkoutSession->shipping_cost); // Should default to 0
        $this->assertEquals(100.00, $checkoutSession->subtotal);
        $this->assertEquals(100.00, $checkoutSession->total);
    }

    /** @test */
    public function it_handles_missing_shipping_cost_gracefully()
    {
        $user = User::factory()->create();

        $sessionData = [
            'transaction_id' => 'TEST-MISSING-' . time(),
            'user_id' => $user->id,
            'cart_items' => [],
            'shipping_address' => [],
            'selected_shipping_rate' => [],
            'subtotal' => 100.00,
            // shipping_cost is completely missing
            'total' => 100.00,
            'vendor_splits' => []
        ];

        $checkoutSession = CheckoutSession::createSession($sessionData);

        $this->assertNotNull($checkoutSession);
        $this->assertEquals(0.00, $checkoutSession->shipping_cost); // Should default to 0
        $this->assertEquals(100.00, $checkoutSession->subtotal);
        $this->assertEquals(100.00, $checkoutSession->total);
    }
}
