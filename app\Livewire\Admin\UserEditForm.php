<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;

class UserEditForm extends Component
{
    public User $user;
    public $roles = [];

    // Form fields
    public $name;
    public $email;
    public $password;
    public $password_confirmation;
    public $selectedRole;

    // UI state
    public $saving = false;

    public function mount(User $user, $roles = [])
    {
        $this->user = $user;
        $this->roles = $roles;

        // Populate form fields
        $this->name = $user->name;
        $this->email = $user->email;
        $this->selectedRole = $user->role_id;
    }

    protected function rules()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $this->user->id,
            'selectedRole' => 'required|exists:roles,id',
        ];

        // Password is optional for updates
        if ($this->password) {
            $rules['password'] = 'string|min:8|confirmed';
        }

        return $rules;
    }

    protected function messages()
    {
        return [
            'name.required' => 'Full name is required.',
            'name.max' => 'Full name cannot exceed 255 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'selectedRole.required' => 'Please select a role for the user.',
            'selectedRole.exists' => 'Selected role is invalid.',
        ];
    }

    public function updatedName($value)
    {
        $this->validateOnly('name');
    }

    public function updatedEmail($value)
    {
        $this->validateOnly('email');
    }

    public function updatedSelectedRole($value)
    {
        $this->validateOnly('selectedRole');
    }

    public function save()
    {
        $this->saving = true;

        try {
            $this->validate();

            // Security check for role assignment
            if (!Gate::allows('assign-role', $this->selectedRole)) {
                session()->flash('error', 'Unauthorized role assignment attempt.');
                return;
            }

            $userData = [
                'name' => $this->name,
                'email' => $this->email,
                'role_id' => $this->selectedRole,
            ];

            if ($this->password) {
                $userData['password'] = Hash::make($this->password);
            }

            $this->user->update($userData);

            $this->dispatch('user-updated');
            session()->flash('success', 'User updated successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update user: ' . $e->getMessage());
        } finally {
            $this->saving = false;
        }
    }

    public function render()
    {
        return view('livewire.admin.user-edit-form');
    }
}
