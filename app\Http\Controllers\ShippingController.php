<?php

namespace App\Http\Controllers;

use App\Services\ShipBubbleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Support\Facades\Log;

class ShippingController extends Controller
{
    protected $shipBubbleService;

    public function __construct(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    /**
     * Get shipping rates for the items in the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRates(Request $request)
    {
        // CRITICAL FIX: Only validate address fields for shipping rate requests
        // Customer personal info is not needed for ShipBubble rate calculation
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'lga' => 'nullable|string|max:255',
            'phone' => 'required|string|regex:/^0[789][01]\\d{8}$/',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:255',
            // Optional fields that can be provided but are not required for rates
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
        ], [
            'address.required' => 'Street address is required for shipping calculation.',
            'city.required' => 'City is required for shipping calculation.',
            'state.required' => 'State is required for shipping calculation.',
            'phone.required' => 'Phone number is required for delivery.',
            'phone.regex' => 'Please enter a valid Nigerian phone number (e.g., 08012345678).',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $destinationPayload = $validator->validated();

        // 1. Validate destination address
        $destinationResponse = $this->shipBubbleService->validateAddress($destinationPayload);
        if ($destinationResponse['status'] !== 'success') {
            $errorMessage = $destinationResponse['message'] ?? 'Invalid destination address. Please check the details and try again.';
            return response()->json(['error' => $errorMessage], 400);
        }
        $receiverAddressCode = $destinationResponse['data']['address_code'];

        // 2. Group cart items by vendor using session cart
        $sessionCart = session('cart', []);
        if (empty($sessionCart)) {
            return response()->json(['error' => 'Your cart is empty.'], 400);
        }

        $productIds = array_keys($sessionCart);
        $products = Product::findMany($productIds)->keyBy('id');

        $vendorShipments = [];
        foreach ($sessionCart as $productId => $item) {
            if (!isset($products[$productId])) {
                Log::warning("Product with ID {$productId} found in cart but not in database. Skipping.");
                continue;
            }
            $product = $products[$productId];
            $vendorId = $product->vendor_id;

            if (!$vendorId) {
                Log::warning("Product with ID {$productId} does not have a vendor. Skipping.");
                continue;
            }

            if (!isset($vendorShipments[$vendorId])) {
                $vendorShipments[$vendorId] = [
                    'items' => [],
                    'vendor' => Vendor::with('user')->find($vendorId),
                    'total_weight' => 0,
                    'max_length' => 0,
                    'max_width' => 0,
                    'max_height' => 0,
                ];
            }

            // Attach the full product model to the item for later use
            $item['product'] = $product;
            $vendorShipments[$vendorId]['items'][] = $item;

            $vendorShipments[$vendorId]['total_weight'] += ($product->weight ?? 0.5) * $item['quantity'];
            // Find the max dimensions for the package
            $vendorShipments[$vendorId]['max_length'] = max($vendorShipments[$vendorId]['max_length'], ($product->length ?? 10));
            $vendorShipments[$vendorId]['max_width'] = max($vendorShipments[$vendorId]['max_width'], ($product->width ?? 10));
            $vendorShipments[$vendorId]['max_height'] = max($vendorShipments[$vendorId]['max_height'], ($product->height ?? 10));
        }

        // 3. Fetch rates for each vendor
        $allRates = [];
        foreach ($vendorShipments as $vendorId => $shipment) {
            $vendor = $shipment['vendor'];
            $requiredFields = ['address', 'city', 'state', 'phone'];
            $missingFields = [];

            if (!$vendor) {
                Log::warning("Vendor with ID {$vendorId} not found. Skipping shipment.");
                continue;
            }

            foreach ($requiredFields as $field) {
                if (empty($vendor->$field)) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                Log::warning("Skipping shipment for vendor ID {$vendorId} ({$vendor->shop_name}) due to missing address details: " . implode(', ', $missingFields));
                continue;
            }

            // 3a. Validate vendor's address with null checks
            $vendorAddressPayload = [
                'name' => $shipment['vendor']->user?->name ?? $shipment['vendor']->business_name, // Use business name as fallback
                'email' => $shipment['vendor']->user?->email ?? 'noreply@' . config('app.domain', 'example.com'),
                'phone' => $shipment['vendor']->phone,
                'address' => $shipment['vendor']->address,
                'city' => $shipment['vendor']->city,
                'state' => $shipment['vendor']->state,
                'lga' => $shipment['vendor']->lga ?? null,
                'country' => 'Nigeria', // Hardcode to Nigeria as per user's instruction
            ];
            $originResponse = $this->shipBubbleService->validateAddress($vendorAddressPayload);
            if ($originResponse['status'] !== 'success') {
                Log::error('Failed to validate vendor address for ' . $shipment['vendor']->business_name, $originResponse);
                continue;
            }
            $senderAddressCode = $originResponse['data']['address_code'];

            // 3b. Prepare payload for fetching rates
            $packageItems = collect($shipment['items'])->map(function ($cartItem) {
                $product = $cartItem['product']; // Retrieve the product model attached earlier
                return [
                    'name' => $cartItem['name'],
                    'description' => $product->short_description ?? $cartItem['name'],
                    'unit_weight' => $product->weight ?? 0.5,
                    'unit_amount' => $cartItem['price'],
                    'quantity' => $cartItem['quantity'],
                ];
            })->all();

            $shipmentData = [
                'sender_address_code' => $senderAddressCode,
                'reciever_address_code' => $receiverAddressCode,
                'pickup_date' => now()->addDay()->format('Y-m-d'),
                // FIXED: Use configuration value instead of hardcoded category ID
                'category_id' => config('services.shipbubble.default_category_id', 74794423),
                'package_items' => $packageItems,
                'package_dimension' => [
                    'length' => $shipment['max_length'],
                    'width' => $shipment['max_width'],
                    'height' => $shipment['max_height'],
                ],
            ];

            $rates = $this->shipBubbleService->getRates($shipmentData);

            if (isset($rates['status']) && $rates['status'] === 'success') {
                // Store the request token in the session for later use (e.g., creating shipments)
                if (isset($rates['data']['request_token'])) {
                    session(["shipbubble_request_tokens.{$vendorId}" => $rates['data']['request_token']]);
                }

                $allRates[$vendorId] = [
                    'vendor_name' => $shipment['vendor']->business_name,
                    'data' => $rates['data'],
                ];
            } else {
                $allRates[$vendorId] = [
                    'vendor_name' => $shipment['vendor']->business_name,
                    'data' => null,
                    'error' => $rates['message'] ?? 'Could not retrieve shipping rates.',
                ];
            }
        }

        // 4. Consolidate rates for a simplified frontend display with enhanced descriptions
        $consolidatedRates = [];
        foreach ($allRates as $vendorId => $vendorRates) {
            if (isset($vendorRates['data']['couriers']) && is_array($vendorRates['data']['couriers'])) {
                foreach ($vendorRates['data']['couriers'] as $courier) {
                    $courierId = $courier['courier_id'];
                    if (!isset($consolidatedRates[$courierId])) {
                        $consolidatedRates[$courierId] = [
                            'courier_id' => $courier['courier_id'],
                            'courier_name' => $courier['courier_name'],
                            'logo' => $courier['logo'] ?? '',
                            'total' => 0,
                            // We store the service code from the first time we see a courier.
                            // This assumes a user will use the same service (e.g., 'same_day') across all vendors for a given courier.
                            // The frontend will need to handle the logic to select the corresponding rate for each vendor.
                            'service_code' => $courier['service_code'],
                            // Enhanced descriptive information
                            'description' => $this->generateShippingDescription($courier),
                            'estimated_delivery' => $this->formatDeliveryTime($courier),
                            'features' => $this->getShippingFeatures($courier),
                        ];
                    }
                    $consolidatedRates[$courierId]['total'] += $courier['total'];
                }
            }
        }

        return response()->json([
            'rates_by_vendor' => $allRates,
            'consolidated_rates' => array_values($consolidatedRates), // Return as a simple array
            'currency' => 'NGN',
        ]);
    }

    /**
     * Generate descriptive information for shipping methods
     */
    private function generateShippingDescription($courier)
    {
        $courierName = strtolower($courier['courier_name'] ?? '');
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // Generate description based on courier and service type
        if (str_contains($serviceCode, 'same_day') || str_contains($serviceCode, 'express')) {
            return 'Fast delivery service with same-day or next-day delivery options. Perfect for urgent orders.';
        } elseif (str_contains($serviceCode, 'standard') || str_contains($serviceCode, 'regular')) {
            return 'Reliable standard delivery service with tracking. Most economical option for regular deliveries.';
        } elseif (str_contains($serviceCode, 'premium') || str_contains($serviceCode, 'priority')) {
            return 'Premium delivery service with enhanced handling and faster processing times.';
        } elseif (str_contains($courierName, 'dhl') || str_contains($courierName, 'fedex')) {
            return 'International courier service with global tracking and insurance options.';
        } elseif (str_contains($courierName, 'gig') || str_contains($courierName, 'kwik')) {
            return 'Local delivery service specializing in fast, reliable deliveries within major cities.';
        } else {
            return 'Professional delivery service with tracking and customer support.';
        }
    }

    /**
     * Format delivery time information
     */
    private function formatDeliveryTime($courier)
    {
        $deliveryEta = $courier['delivery_eta'] ?? '';
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // If we have specific ETA, use it
        if (!empty($deliveryEta) && $deliveryEta !== 'Standard delivery') {
            return $deliveryEta;
        }

        // Generate estimated delivery time based on service code
        if (str_contains($serviceCode, 'same_day')) {
            return 'Same day delivery (order before 2 PM)';
        } elseif (str_contains($serviceCode, 'express') || str_contains($serviceCode, 'next_day')) {
            return '1-2 business days';
        } elseif (str_contains($serviceCode, 'standard') || str_contains($serviceCode, 'regular')) {
            return '3-5 business days';
        } elseif (str_contains($serviceCode, 'economy')) {
            return '5-7 business days';
        } else {
            return '2-4 business days';
        }
    }

    /**
     * Get shipping features for display
     */
    private function getShippingFeatures($courier)
    {
        $features = [];
        $courierName = strtolower($courier['courier_name'] ?? '');
        $serviceCode = strtolower($courier['service_code'] ?? '');

        // Add features based on courier and service type
        $features[] = 'Real-time tracking';

        if (str_contains($serviceCode, 'same_day') || str_contains($serviceCode, 'express')) {
            $features[] = 'Priority handling';
            $features[] = 'SMS notifications';
        }

        if (str_contains($serviceCode, 'premium') || str_contains($courierName, 'dhl') || str_contains($courierName, 'fedex')) {
            $features[] = 'Insurance included';
            $features[] = 'Signature required';
        }

        if (str_contains($courierName, 'gig') || str_contains($courierName, 'kwik')) {
            $features[] = 'Local expertise';
            $features[] = 'Flexible delivery';
        }

        $features[] = 'Customer support';

        return $features;
    }
}
