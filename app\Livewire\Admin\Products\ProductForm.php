<?php

namespace App\Livewire\Admin\Products;

use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use App\Traits\ProductValidationRules;
use Illuminate\Support\Str;
use Livewire\Component;

class ProductForm extends Component
{
    use ProductValidationRules;
    public ?Product $product = null;
    public string $name = '';
    public string $slug = '';
    public ?int $vendor_id = null;
    public ?int $category_id = null;
    public string $description = '';
    public ?float $price = null;
    public ?float $discount_price = null;
    public ?string $image_url = '';
    public ?int $stock = null;
    public bool $is_active = true;

    public array $vendors = [];
    public array $categories = [];

    public function mount(?Product $product = null): void
    {
        $this->product = $product;
        if ($this->product && $this->product->exists) {
            $this->name = $this->product->name;
            $this->slug = $this->product->slug;
            $this->vendor_id = $this->product->vendor_id;
            $this->category_id = $this->product->category_id;
            $this->description = $this->product->description;
            $this->price = $this->product->price;
            $this->discount_price = $this->product->discount_price;
            $this->image_url = $this->product->image_url;
            $this->stock = $this->product->stock;
            $this->is_active = $this->product->is_active;
        }
        $this->vendors = Vendor::orderBy('shop_name')->get(['id', 'shop_name'])->toArray();
        $this->categories = Category::orderBy('name')->get(['id', 'name'])->toArray();
    }

    /**
     * LOGIC-CRITICAL-001 FIX: Use standardized validation rules
     * This ensures consistency with all other product forms
     */
    protected function rules(): array
    {
        $rules = $this->getProductValidationRules(
            isUpdate: $this->product && $this->product->exists,
            requireVendor: true // Admin forms require vendor selection
        );

        // Add slug validation specific to admin forms
        $rules['slug'] = 'required|string|max:255|unique:products,slug,' . ($this->product?->id ?? 'NULL');

        // Add image URL validation for admin forms
        $rules = array_merge($rules, $this->getProductImageUrlValidationRules('image_url', false));

        return $rules;
    }

    /**
     * Get custom validation messages
     */
    protected function messages(): array
    {
        return $this->getProductValidationMessages();
    }

    /**
     * Get validation attributes for better error messages
     */
    protected function validationAttributes(): array
    {
        return $this->getProductValidationAttributes();
    }

    public function updatedName(string $value): void
    {
        if (!$this->product || !$this->product->exists) {
            $this->slug = Str::slug($value);
        }
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'vendor_id' => $this->vendor_id,
            'category_id' => $this->category_id,
            'description' => $this->description,
            'price' => $this->price,
            'discount_price' => $this->discount_price,
            'image_url' => $this->image_url,
            'stock' => $this->stock,
            'is_active' => $this->is_active,
        ];

        if ($this->product && $this->product->exists) {
            $this->product->update($data);
            session()->flash('success', 'Product updated successfully.');
        } else {
            Product::create($data);
            session()->flash('success', 'Product created successfully.');
        }

        // Form submission complete - no redirect needed
    }

    public function render()
    {
        return view('livewire.admin.products.product-form');
    }
}