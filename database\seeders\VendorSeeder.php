<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Vendor;
use App\Models\User;
use App\Models\Role;
use App\Models\VendorSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure storage directories exist
        Storage::disk('public')->makeDirectory('vendor-logos');
        Storage::disk('public')->makeDirectory('vendor-documents');
        Storage::disk('public')->makeDirectory('vendor-banners');

        // Create comprehensive vendor data with media files
        $vendors = [
            [
                'name' => 'Nike',
                'description' => 'Leading sportswear and athletic footwear brand known for innovation and performance.',
                'address' => '1 Nike Store Road',
                'city' => 'Ikeja',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'phone' => '+2348012345678',
                'featured' => true,
                'subscription_plan' => 'Monthly Plan',
                'about' => 'Welcome to the official Nike store. We are dedicated to bringing you the best in athletic footwear and apparel. Innovation is at the heart of everything we do.',
                'facebook_url' => 'https://facebook.com/nike',
                'twitter_url' => 'https://twitter.com/nike',
                'instagram_url' => 'https://instagram.com/nike'
            ],
            [
                'name' => 'Adidas',
                'description' => 'Iconic sportswear brand combining style with performance for athletes and fashion enthusiasts.',
                'address' => '2 Adidas Avenue',
                'city' => 'Lekki',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'phone' => '+2348087654321',
                'featured' => true,
                'subscription_plan' => 'Monthly Plan',
                'about' => 'Adidas has a long history of creating innovative products that blend style and performance. Discover our latest collections for men, women, and kids.',
                'facebook_url' => 'https://facebook.com/adidas',
                'twitter_url' => 'https://twitter.com/adidas',
                'instagram_url' => 'https://instagram.com/adidas'
            ],
            [
                'name' => 'Puma',
                'description' => 'Sports lifestyle brand offering innovative designs for both athletic and casual wear.',
                'address' => '3 Akin Adesola Street',
                'city' => 'Victoria Island',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'phone' => '+2348098765432',
                'featured' => true,
                'subscription_plan' => 'Free Plan'
            ],
            [
                'name' => 'Zara Fashion',
                'description' => 'Contemporary fashion retailer offering trendy clothing for men and women.',
                'address' => '45 Admiralty Way',
                'city' => 'Lekki',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'phone' => '+2348123456789',
                'featured' => false,
                'subscription_plan' => 'Monthly Plan'
            ],
            [
                'name' => 'H&M Store',
                'description' => 'Affordable fashion for everyone with sustainable and conscious choices.',
                'address' => '12 Adeola Odeku Street',
                'city' => 'Victoria Island',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'phone' => '08123456789', // Fixed format for ShipBubble API
                'featured' => false,
                'subscription_plan' => 'Free Plan'
            ]
        ];

        foreach ($vendors as $vendorData) {
            $this->createVendorWithMedia($vendorData);
        }
    }

    /**
     * Create a vendor with comprehensive data including media files
     */
    private function createVendorWithMedia(array $data)
    {
        // Get the vendor role
        $vendorRole = Role::where('name', 'vendor')->first();

        // Create a user for the vendor if it doesn't exist
        $email = Str::slug($data['name']) . '@example.com';
        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => $data['name'] . ' Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role_id' => $vendorRole->id
            ]
        );

        // Create or update the vendor
        $vendor = Vendor::updateOrCreate(
            ['user_id' => $user->id],
            [
                'shop_name' => $data['name'],
                'slug' => Str::slug($data['name']),
                'business_name' => $data['name'] . ' Inc.',
                'business_description' => $data['description'],
                'business_address' => $data['address'],
                'city' => $data['city'],
                'state' => $data['state'],
                'country' => $data['country'],
                'phone' => $data['phone'],
                'is_featured' => $data['featured'],
                'is_approved' => true, // All seeded vendors are pre-approved
                'has_completed_onboarding' => true, // CRITICAL FIX: Set onboarding as completed for seeded vendors
                'about' => $data['about'] ?? null,
                'facebook_url' => $data['facebook_url'] ?? null,
                'twitter_url' => $data['twitter_url'] ?? null,
                'instagram_url' => $data['instagram_url'] ?? null,
            ]
        );

        // Add sample logo
        $this->addSampleLogo($vendor, $data['name']);

        // Add sample banner
        $this->addSampleBanner($vendor, $data['name']);

        // Add sample documents
        $this->addSampleDocuments($vendor, $data['name']);

        // Create subscription for vendor
        $this->createVendorSubscription($vendor, $data['subscription_plan']);

        $this->command->info("Created vendor: {$data['name']} with media files and subscription");
    }

    /**
     * Add sample logo to vendor using Media Library
     */
    private function addSampleLogo(Vendor $vendor, string $vendorName)
    {
        try {
            // Create a sample image file
            $logoContent = $this->createSampleImage(200, 200, $vendorName);
            $logoPath = storage_path('app/temp/' . Str::slug($vendorName) . '-logo.png');

            // Ensure temp directory exists
            if (!file_exists(dirname($logoPath))) {
                mkdir(dirname($logoPath), 0755, true);
            }

            file_put_contents($logoPath, $logoContent);

            // Add to media library
            $vendor->addMedia($logoPath)
                ->usingName($vendorName . ' Logo')
                ->usingFileName(Str::slug($vendorName) . '-logo.png')
                ->toMediaCollection('logo');

            // Clean up temp file
            if (file_exists($logoPath)) {
                unlink($logoPath);
            }
        } catch (\Exception $e) {
            $this->command->warn("Could not create logo for {$vendorName}: " . $e->getMessage());
        }
    }

    /**
     * Add sample documents to vendor
     */
    private function addSampleBanner(Vendor $vendor, string $vendorName)
    {
        try {
            // Create a temporary image file for the banner
            $bannerImage = $this->createSampleImage(1200, 400, "{$vendorName} Banner");
            $bannerPath = storage_path('app/public/vendor-banners/' . Str::slug($vendorName) . '-banner.png');
            file_put_contents($bannerPath, $bannerImage);

            // Attach banner to vendor using Media Library
            $vendor->addMedia($bannerPath)
                ->preservingOriginal()
                ->toMediaCollection('banner');

        } catch (\Exception $e) {
            $this->command->warn("Could not create banner for {$vendorName}: " . $e->getMessage());
        }
    }

    /**
     * Add sample documents to vendor
     */
    private function addSampleDocuments(Vendor $vendor, string $vendorName)
    {
        try {
            // Create sample ID document
            $idDocContent = $this->createSamplePDF("ID Document for {$vendorName}");
            $idDocPath = storage_path('app/temp/' . Str::slug($vendorName) . '-id.pdf');

            // Ensure temp directory exists
            if (!file_exists(dirname($idDocPath))) {
                mkdir(dirname($idDocPath), 0755, true);
            }

            file_put_contents($idDocPath, $idDocContent);

            $vendor->addMedia($idDocPath)
                ->usingName('ID Document')
                ->usingFileName(Str::slug($vendorName) . '-id.pdf')
                ->toMediaCollection('id_documents');

            // Create sample business document
            $businessDocContent = $this->createSamplePDF("Business Registration for {$vendorName}");
            $businessDocPath = storage_path('app/temp/' . Str::slug($vendorName) . '-business.pdf');

            file_put_contents($businessDocPath, $businessDocContent);

            $vendor->addMedia($businessDocPath)
                ->usingName('Business Document')
                ->usingFileName(Str::slug($vendorName) . '-business.pdf')
                ->toMediaCollection('business_documents');

            // Clean up temp files
            if (file_exists($idDocPath)) unlink($idDocPath);
            if (file_exists($businessDocPath)) unlink($businessDocPath);

        } catch (\Exception $e) {
            $this->command->warn("Could not create documents for {$vendorName}: " . $e->getMessage());
        }
    }

    /**
     * Create vendor subscription
     */
    private function createVendorSubscription(Vendor $vendor, string $planName)
    {
        $plan = SubscriptionPlan::where('name', $planName)->first();

        if ($plan) {
            VendorSubscription::updateOrCreate(
                ['vendor_id' => $vendor->id],
                [
                    'subscription_plan_id' => $plan->id,
                    'paystack_subscription_code' => 'DEMO_' . strtoupper(Str::random(10)),
                    'status' => 'active',
                    'starts_at' => now(),
                    'ends_at' => now()->addDays($plan->duration_days),
                ]
            );
        }
    }

    /**
     * Create a simple sample image
     */
    private function createSampleImage(int $width, int $height, string $text): string
    {
        // Create a simple PNG image with text
        $image = imagecreate($width, $height);

        // Colors
        $backgroundColor = imagecolorallocate($image, 255, 255, 255); // White
        $textColor = imagecolorallocate($image, 0, 0, 0); // Black
        $borderColor = imagecolorallocate($image, 200, 200, 200); // Light gray

        // Fill background
        imagefill($image, 0, 0, $backgroundColor);

        // Add border
        imagerectangle($image, 0, 0, $width - 1, $height - 1, $borderColor);

        // Add text using built-in font
        $textWidth = strlen($text) * 10; // Approximate width for built-in font
        $textHeight = 15; // Approximate height for built-in font

        $x = max(5, ($width - $textWidth) / 2);
        $y = max(5, ($height - $textHeight) / 2);

        // Use imagestring with built-in font
        imagestring($image, 3, $x, $y, $text, $textColor);

        // Capture output
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();

        // Clean up
        imagedestroy($image);

        return $imageData;
    }

    /**
     * Create a simple sample PDF content
     */
    private function createSamplePDF(string $content): string
    {
        // Simple PDF structure (minimal PDF for testing)
        $pdf = "%PDF-1.4\n";
        $pdf .= "1 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Catalog\n";
        $pdf .= "/Pages 2 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n";
        $pdf .= "2 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Pages\n";
        $pdf .= "/Kids [3 0 R]\n";
        $pdf .= "/Count 1\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n";
        $pdf .= "3 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Type /Page\n";
        $pdf .= "/Parent 2 0 R\n";
        $pdf .= "/MediaBox [0 0 612 792]\n";
        $pdf .= "/Contents 4 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "endobj\n";
        $pdf .= "4 0 obj\n";
        $pdf .= "<<\n";
        $pdf .= "/Length " . strlen($content) . "\n";
        $pdf .= ">>\n";
        $pdf .= "stream\n";
        $pdf .= "BT\n";
        $pdf .= "/F1 12 Tf\n";
        $pdf .= "100 700 Td\n";
        $pdf .= "(" . $content . ") Tj\n";
        $pdf .= "ET\n";
        $pdf .= "endstream\n";
        $pdf .= "endobj\n";
        $pdf .= "xref\n";
        $pdf .= "0 5\n";
        $pdf .= "0000000000 65535 f \n";
        $pdf .= "0000000009 65535 n \n";
        $pdf .= "0000000074 65535 n \n";
        $pdf .= "0000000120 65535 n \n";
        $pdf .= "0000000179 65535 n \n";
        $pdf .= "trailer\n";
        $pdf .= "<<\n";
        $pdf .= "/Size 5\n";
        $pdf .= "/Root 1 0 R\n";
        $pdf .= ">>\n";
        $pdf .= "startxref\n";
        $pdf .= "492\n";
        $pdf .= "%%EOF\n";

        return $pdf;
    }
}
