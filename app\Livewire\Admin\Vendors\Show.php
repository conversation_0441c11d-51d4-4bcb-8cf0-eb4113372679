<?php

namespace App\Livewire\Admin\Vendors;

use App\Models\Vendor;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.admin')]
class Show extends Component
{
    public Vendor $vendor;

    public function mount(Vendor $vendor)
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
        
        // Load the vendor with all necessary relationships
        $this->vendor = $vendor->load([
            'user',
            'products',
            'orders',
            'subscription',
            'subscription.plan',
            'transactions'
        ]);
    }

    /**
     * CRITICAL FIX SM2/AL3: Use is_approved as single source of truth for vendor status
     * This eliminates confusion between status and is_approved fields
     */
    public function toggleStatus()
    {
        // Use is_approved as the single source of truth
        $this->vendor->is_approved = !$this->vendor->is_approved;

        // Keep status field in sync for backward compatibility
        $this->vendor->status = $this->vendor->is_approved ? 'approved' : 'pending';

        $this->vendor->save();

        $status = $this->vendor->is_approved ? 'approved' : 'pending approval';
        session()->flash('success', "Vendor status has been changed to {$status}.");
    }

    public function toggleFeatured()
    {
        $this->vendor->is_featured = !$this->vendor->is_featured;
        $this->vendor->save();
        
        $status = $this->vendor->is_featured ? 'featured' : 'unfeatured';
        session()->flash('success', "Vendor has been {$status} successfully.");
    }

    public function render()
    {
        return view('livewire.admin.vendors.show');
    }
}
