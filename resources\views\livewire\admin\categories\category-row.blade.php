<tr wire:key="category-{{ $category->id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
        <span style="padding-left: {{ $level * 20 }}px;">{{ $category->name }}</span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $category->slug }}</td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $category->parent->name ?? '-' }}</td>
    <td class="px-6 py-4">
        <div class="flex items-center space-x-2">
            <button wire:click="edit({{ $category->id }})" class="p-1 text-gray-600 hover:text-gray-900">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </button>
            <button wire:click="delete({{ $category->id }})" onclick="return confirm('Are you sure you want to delete {{ $category->name }}?')" class="p-1 text-red-600 hover:text-red-900">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </td>
</tr>

@if ($category->children->isNotEmpty())
    @foreach ($category->children as $child)
        @include('livewire.admin.categories.category-row', ['category' => $child, 'level' => $level + 1])
    @endforeach
@endif
