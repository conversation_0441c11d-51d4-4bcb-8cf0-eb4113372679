<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Commission;
use App\Models\VendorEarning;
use Illuminate\Support\Str;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = User::whereHas('role', function($query) {
            $query->where('name', 'customer');
        })->get();

        $products = Product::with('vendor')->get();

        if ($customers->isEmpty()) {
            $this->command->error('No customers found. Please run CustomerSeeder first.');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->error('No products found. Please run ProductSeeder first.');
            return;
        }

        // Create 50 sample orders with realistic data
        for ($i = 1; $i <= 50; $i++) {
            $customer = $customers->random();
            $orderDate = now()->subDays(rand(1, 90)); // Orders from last 90 days
            
            // Random order status based on age
            $daysSinceOrder = $orderDate->diffInDays(now());
            $status = $this->getRealisticOrderStatus($daysSinceOrder);

            $order = Order::create([
                'order_number' => 'BRD-' . strtoupper(Str::random(8)),
                'user_id' => $customer->id,
                'status' => $status,
                'subtotal' => 0, // Will be calculated
                'tax_amount' => 0,
                'shipping_amount' => rand(500, 2000), // ₦5-20 shipping
                'total_amount' => 0, // Will be calculated
                'currency' => 'NGN',
                
                // Shipping details
                'shipping_name' => $customer->name,
                'shipping_email' => $customer->email,
                'shipping_phone' => $customer->phone ?? '+2348123456789',
                'shipping_address' => $customer->address ?? '123 Sample Street',
                'shipping_city' => $customer->city ?? 'Lagos',
                'shipping_state' => $customer->state ?? 'Lagos',
                'shipping_country' => $customer->country ?? 'Nigeria',
                'shipping_postal_code' => '100001',
                
                // Payment details
                'payment_method' => 'paystack',
                'payment_status' => $status === 'cancelled' ? 'failed' : 'paid',
                'payment_reference' => 'PAY_' . strtoupper(Str::random(10)),
                
                'created_at' => $orderDate,
                'updated_at' => $orderDate,
            ]);

            // Add 1-4 random products to each order
            $orderProducts = $products->random(rand(1, 4));
            $subtotal = 0;

            foreach ($orderProducts as $product) {
                $quantity = rand(1, 3);
                $price = $product->discount_price ?? $product->price;
                $itemTotal = $price * $quantity;
                $subtotal += $itemTotal;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'vendor_id' => $product->vendor_id,
                    'quantity' => $quantity,
                    'price' => $price,
                    'total' => $itemTotal,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku ?? 'SKU-' . $product->id,
                ]);

                // Create commission record for completed orders
                if ($status === 'delivered') {
                    $commissionAmount = $itemTotal * 0.027; // 2.7% commission
                    $vendorEarning = $itemTotal - $commissionAmount;

                    Commission::create([
                        'vendor_id' => $product->vendor_id,
                        'order_id' => $order->id,
                        'order_item_id' => null, // Can be null for order-level commissions
                        'amount' => $commissionAmount,
                        'status' => rand(0, 1) ? 'paid' : 'pending',
                        'created_at' => $orderDate->addDays(rand(1, 7)), // Commission created after order
                    ]);

                    // Update vendor earnings directly on the vendor model
                    $vendor = $product->vendor;
                    $vendor->increment('total_earnings', $vendorEarning);
                    $vendor->increment('pending_balance', $vendorEarning);
                }
            }

            // Calculate totals
            $taxAmount = $subtotal * 0.075; // 7.5% VAT
            $totalAmount = $subtotal + $taxAmount + $order->shipping_amount;

            $order->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
            ]);

            $this->command->info("Created order {$order->order_number} for {$customer->name} - ₦" . number_format($totalAmount, 2));
        }

        $this->command->info('Created 50 sample orders with realistic data, commissions, and vendor earnings');
    }

    /**
     * Get realistic order status based on order age
     */
    private function getRealisticOrderStatus(int $daysSinceOrder): string
    {
        if ($daysSinceOrder <= 1) {
            return collect(['pending', 'processing'])->random();
        } elseif ($daysSinceOrder <= 3) {
            return collect(['processing', 'shipped'])->random();
        } elseif ($daysSinceOrder <= 7) {
            return collect(['shipped', 'delivered'])->random();
        } elseif ($daysSinceOrder <= 30) {
            return collect(['delivered', 'delivered', 'delivered', 'cancelled'])->random(); // 75% delivered, 25% cancelled
        } else {
            return collect(['delivered', 'delivered', 'delivered', 'delivered', 'cancelled'])->random(); // 80% delivered, 20% cancelled
        }
    }
}
