<?php

namespace App\Console\Commands;

use App\Models\Brand;
use App\Models\Vendor;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class SyncVendorBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendor:sync-brands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync vendors with brands - create brands for vendors that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting vendor-brand synchronization...');
        
        $vendors = Vendor::with('brand')->get();
        $synced = 0;
        $created = 0;
        
        foreach ($vendors as $vendor) {
            // Check if there's already a brand with the same name as the vendor
            $existingBrand = Brand::where('name', $vendor->shop_name)->first();

            if ($existingBrand && !$existingBrand->vendor_id) {
                // Link existing brand to vendor
                $existingBrand->update(['vendor_id' => $vendor->id]);
                $this->info("Linked existing brand '{$existingBrand->name}' to vendor '{$vendor->shop_name}'");
                $synced++;
            } elseif (!$vendor->brand) {
                // Create a brand for this vendor
                $brand = Brand::create([
                    'vendor_id' => $vendor->id,
                    'name' => $vendor->shop_name,
                    'slug' => $vendor->slug,
                    'description' => "Official brand for {$vendor->shop_name}",
                    'is_active' => true,
                    'is_featured' => $vendor->is_featured,
                ]);

                $this->info("Created brand '{$brand->name}' for vendor '{$vendor->shop_name}'");
                $created++;
            } else {
                // Update existing brand to match vendor
                $vendor->brand->update([
                    'name' => $vendor->shop_name,
                    'slug' => $vendor->slug,
                    'is_featured' => $vendor->is_featured,
                ]);

                $this->info("Synced brand '{$vendor->brand->name}' with vendor '{$vendor->shop_name}'");
                $synced++;
            }
        }
        
        $this->info("Synchronization complete!");
        $this->info("Created: {$created} brands");
        $this->info("Synced: {$synced} existing brands");
        
        return 0;
    }
}
