<?php

namespace App\Livewire\Shared;

use Livewire\Component;
use App\Traits\HandlesProductImages;
use App\Traits\ManagesVariants;
use App\Traits\ManagesSpecifications;
use App\Traits\ProductValidationRules;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Storage;

/**
 * Base component for product forms that provides common functionality
 */
abstract class ProductForm extends Component
{
    use HandlesProductImages, ManagesVariants, ManagesSpecifications, ProductValidationRules;

    // Common properties
    public $product;
    public $image;
    public $gallery = [];
    public $variants = [];
    public $specifications = [];
    public $mediaToDelete = [];

    // Common initialization
    protected function initializeProductForm()
    {
        // Initialize variants if empty
        if (empty($this->variants)) {
            $this->addVariant();
        }

        // Initialize specifications if empty
        if (empty($this->specifications)) {
            $this->addSpecification();
        }
    }

    /**
     * LOGIC-CRITICAL-001 FIX: Use standardized validation rules with prefix
     * This ensures consistency with all other product forms
     *
     * @return array
     */
    protected function getProductRules()
    {
        return array_merge(
            $this->getPrefixedProductValidationRules(
                prefix: 'product',
                isUpdate: $this->product && $this->product->exists,
                requireVendor: false // Shared forms don't require vendor (auto-assigned)
            ),
            $this->getImageValidationRules('image', 10240),
            $this->getImageValidationRules('gallery', 10240, true),
            $this->getVariantValidationRules(),
            $this->getSpecificationValidationRules()
        );
    }

    /**
     * Get custom validation messages using standardized messages
     */
    protected function getMessages()
    {
        return array_merge(
            $this->getProductValidationMessages('product'),
            $this->getVariantValidationMessages(),
            $this->getSpecificationValidationMessages()
        );
    }

    /**
     * Handle file uploads and save the product
     */
    protected function saveProduct()
    {
        return DB::transaction(function () {
            // Save the product
            $this->product->save();

            // Handle main image upload
            if ($this->image) {
                $this->handleMainImageUpload($this->product, 'image', 'product_images');
            }

            // Handle gallery uploads
            if (!empty($this->gallery)) {
                $this->handleGalleryUploads($this->product, 'gallery', 'product_gallery');
            }

            // Save variants and specifications
            $this->saveVariants($this->product);
            $this->saveSpecifications($this->product);

            // Clean up any deleted media
            $this->cleanupMedia();

            return true;
        });
    }

    /**
     * Clean up any media marked for deletion
     */
    protected function cleanupMedia()
    {
        if (!empty($this->mediaToDelete)) {
            if (class_exists(Media::class)) {
                Media::whereIn('id', $this->mediaToDelete)->delete();
            }
            $this->mediaToDelete = [];
        }
    }

    /**
     * Handle file upload for variants
     */
    public function uploadVariantImage($index)
    {
        $this->handleVariantImageUpload($index, 'image');
    }

    /**
     * Remove a variant image
     */
    public function removeVariantImage($index)
    {
        // If it's a new, temporary image, just unset it
        if (isset($this->variants[$index]['image']) && $this->variants[$index]['image']) {
            $this->variants[$index]['image'] = null;
            return;
        }

        // If it's an existing image, find the variant and clear its media
        if (isset($this->variants[$index]['id'])) {
            $variant = $this->product->variants()->find($this->variants[$index]['id']);
            if ($variant) {
                $variant->clearMediaCollection('variant_image');
            }
        }

        // Also clear the URL from the local array to update the UI
        if (isset($this->variants[$index]['existing_image_url'])) {
            $this->variants[$index]['existing_image_url'] = null;
        }
    }

    /**
     * Get the view that should be rendered
     */
    abstract public function render();
}
