{{-- AUTHENTICATION REBUILD: Consistent branding with mobile-first design --}}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        {{-- Consistent Brand Logo --}}
        <div class="flex justify-center mb-6">
            <a href="{{ route('home') }}" class="flex items-center space-x-3" wire:navigate>
                <div class="flex items-center justify-center w-12 h-12 bg-black rounded-xl">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
                <span class="text-2xl font-bold text-gray-900">Brandify</span>
            </a>
        </div>

        <h2 class="text-center text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            {{ __('Welcome back') }}
        </h2>
        <p class="text-center text-sm text-gray-600 mb-8">
            {{ __('Sign in to your account or') }}
            @if (Route::has('register'))
                <a href="{{ route('register') }}" class="font-medium text-black hover:text-gray-800 underline" wire:navigate>
                    {{ __('create a new account') }}
                </a>
            @endif
        </p>
    </div>

    <div class="mx-4 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-6 shadow-xl rounded-2xl border border-gray-100">
            {{-- Status Messages --}}
            @if (session('status'))
                <div class="mb-6 rounded-xl bg-green-50 border border-green-200 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">{{ session('status') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Login Form with Mobile-First Design --}}
            <form wire:submit="login" class="space-y-6" wire:loading.class="opacity-75">
                @csrf

                {{-- Email Field --}}
                <div>
                    <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        wire:model="email"
                        autocomplete="email"
                        placeholder="Enter your email address"
                        required
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('email') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('email')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Password Field --}}
                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-900 mb-2">
                        Password
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        wire:model="password"
                        autocomplete="current-password"
                        placeholder="Enter your password"
                        required
                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-base min-h-[44px] @error('password') border-red-500 focus:border-red-500 @enderror"
                    >
                    @error('password')
                        <p class="text-red-600 text-sm flex items-center mt-2">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                {{-- Remember Me & Forgot Password --}}
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex items-center">
                        <input wire:model="remember"
                               id="remember"
                               name="remember"
                               type="checkbox"
                               class="w-4 h-4 text-black border-2 border-gray-300 rounded focus:ring-black focus:ring-2 transition-all duration-300">
                        <label for="remember" class="ml-3 text-sm font-medium text-gray-900">
                            {{ __('Remember me') }}
                        </label>
                    </div>

                    @if (Route::has('password.request'))
                        <div class="text-sm">
                            <a href="{{ route('password.request') }}"
                               class="font-medium text-black hover:text-gray-800 transition-colors duration-300"
                               wire:navigate>
                                {{ __('Forgot your password?') }}
                            </a>
                        </div>
                    @endif
                </div>

                {{-- Submit Button with Proper Touch Targets --}}
                <div class="pt-2">
                    <button
                        type="submit"
                        wire:loading.attr="disabled"
                        class="btn-primary w-full flex items-center justify-center py-3 px-4 border border-transparent text-base font-semibold rounded-xl text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 min-h-[44px] group"
                    >
                        <div wire:loading wire:target="login" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        <i class="fas fa-sign-in-alt transition-transform duration-300 group-hover:translate-x-1 mr-2" wire:loading.remove wire:target="login"></i>
                        <span wire:loading.remove wire:target="login">{{ __('Sign In') }}</span>
                        <span wire:loading wire:target="login">{{ __('Signing in...') }}</span>
                    </button>
                </div>
            </form>

            {{-- Additional Links --}}
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="{{ route('register') }}" class="font-medium text-black hover:text-gray-800 transition-colors" wire:navigate>
                        Sign up here
                    </a>
                </p>
                <p class="text-sm text-gray-600 mt-2">
                    Want to sell on Brandify?
                    <a href="{{ route('vendor.register') }}" class="font-medium text-black hover:text-gray-800 transition-colors" wire:navigate>
                        Become a vendor
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
