# ShipBubble API Compliance Documentation

## Overview
This document outlines the implementation of ShipBubble API integration according to their official documentation, ensuring proper compliance and best practices.

## 🔗 API Endpoints Implemented

### 1. Address Validation
**Endpoint**: `POST /shipping/address/validate`

**Purpose**: Validates and creates address codes for shipping calculations

**Implementation**:
```php
public function validateAddress(array $data)
{
    $payload = [
        'name' => $data['name'],
        'email' => $data['email'],
        'phone' => $data['phone'],
        'address' => $sanitizedAddress,
    ];
    
    return $this->sendRequest('post', '/shipping/address/validate', $payload);
}
```

**Compliance Features**:
- ✅ Address sanitization to remove special characters
- ✅ Proper error handling for invalid addresses
- ✅ Address code caching for performance
- ✅ Fallback mechanisms for validation failures

### 2. Shipping Rates
**Endpoint**: `POST /shipping/fetch_rates`

**Purpose**: Retrieves available shipping rates for given addresses and package details

**Implementation**:
```php
public function getRates(array $data)
{
    return $this->sendRequest('post', '/shipping/fetch_rates', $data);
}
```

**Required Parameters**:
- `sender_address_code`: Validated sender address code
- `reciever_address_code`: Validated receiver address code
- `pickup_date`: Scheduled pickup date (next business day)
- `category_id`: Product category ID (configurable)
- `package_items`: Array of items with weight and value
- `package_dimension`: Package dimensions in cm

**Compliance Features**:
- ✅ Proper weight conversion (grams to kg)
- ✅ Accurate package dimensions calculation
- ✅ Category ID configuration
- ✅ Pickup date scheduling

### 3. Shipment Creation
**Endpoint**: `POST /shipping/labels`

**Purpose**: Creates shipping labels and tracking codes

**Implementation**:
```php
public function createShipment(array $data)
{
    return $this->sendRequest('post', '/shipping/labels', $data);
}
```

### 4. Shipment Tracking
**Endpoint**: `GET /tracking/{trackingCode}`

**Purpose**: Retrieves shipment tracking information

**Implementation**:
```php
public function trackShipment(string $trackingCode)
{
    return $this->sendRequest('get', "/tracking/{$trackingCode}");
}
```

## 🔐 Authentication & Security

### API Key Management
```php
protected $apiKey;

public function __construct()
{
    $this->apiKey = config('services.shipbubble.key');
    $this->baseUrl = config('services.shipbubble.url', 'https://api.shipbubble.com/v1');
}
```

**Security Features**:
- ✅ API key stored in environment variables
- ✅ Secure transmission via HTTPS
- ✅ API key validation before requests
- ✅ No API key exposure in logs

### Request Headers
```php
Http::withToken($this->apiKey)
    ->acceptJson()
    ->timeout(30)
    ->retry(2, 1000)
```

**Compliance Features**:
- ✅ Bearer token authentication
- ✅ JSON content type headers
- ✅ Proper timeout handling
- ✅ Retry mechanism for failed requests

## 📦 Package Data Format

### Item Structure
```php
$packageItems[] = [
    'name' => $item['name'],
    'description' => $item['description'] ?? 'Product description',
    'unit_weight' => (string)$weight, // in kg
    'unit_amount' => (string)$amount, // in NGN
    'quantity' => (string)$quantity,
];
```

### Package Dimensions
```php
$packageDimension = [
    'length' => 30, // cm
    'width' => 20,  // cm
    'height' => 15, // cm
];
```

**Compliance Notes**:
- ✅ All numeric values converted to strings as required
- ✅ Weight in kilograms (converted from grams)
- ✅ Dimensions in centimeters
- ✅ Currency amounts in Nigerian Naira

## 🏢 Address Format Compliance

### Sender Address (Business)
```php
$senderAddress = [
    'name' => config('app.name', 'Brandify'),
    'email' => config('mail.from.address'),
    'phone' => config('services.shipbubble.default_phone'),
    'address' => 'Complete business address, City, State, Nigeria',
];
```

### Receiver Address (Customer)
```php
$receiverAddress = [
    'name' => $shippingAddress['name'],
    'email' => $shippingAddress['email'],
    'phone' => $shippingAddress['phone'],
    'address' => 'Street Address, City, LGA, State, Nigeria',
];
```

**Address Sanitization**:
```php
// Remove special characters that API might reject
$sanitizedAddress = preg_replace('/[^\p{L}\p{N}\s,]/u', '', $addressString);

// Clean up duplicate commas and spaces
$sanitizedAddress = trim(preg_replace('/,{2,}/', ',', $sanitizedAddress), ' ,');
```

## ⚠️ Error Handling Compliance

### Error Response Format
```php
protected function parseErrorMessage($responseData)
{
    if (is_array($responseData)) {
        if (isset($responseData['message'])) {
            return $responseData['message'];
        }
        
        if (isset($responseData['error'])) {
            return is_string($responseData['error']) ? $responseData['error'] : 'API error occurred';
        }
        
        if (isset($responseData['errors']) && is_array($responseData['errors'])) {
            return implode(', ', array_values($responseData['errors']));
        }
    }
    
    return 'ShipBubble API request failed';
}
```

### Error Types Handled
- ✅ **Connection Errors**: Network connectivity issues
- ✅ **Authentication Errors**: Invalid API key or permissions
- ✅ **Validation Errors**: Invalid address or package data
- ✅ **Rate Limit Errors**: API quota exceeded
- ✅ **Server Errors**: ShipBubble service unavailable

### Retry Logic
```php
->retry(2, 1000) // Retry twice with 1 second delay
```

## 📊 Response Handling

### Rate Response Structure
```json
{
    "status": "success",
    "data": [
        {
            "courier_id": "123",
            "courier_name": "Courier Name",
            "total": 1500.00,
            "delivery_time": "2-3 business days",
            "description": "Standard delivery",
            "tracking_available": true,
            "insurance_available": false
        }
    ]
}
```

### Error Response Structure
```json
{
    "status": "error",
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    }
}
```

## 🔄 Webhook Implementation (Future)

### Planned Webhook Endpoints
1. **Shipment Status Updates**: `/webhooks/shipbubble/status`
2. **Delivery Confirmations**: `/webhooks/shipbubble/delivery`
3. **Exception Notifications**: `/webhooks/shipbubble/exceptions`

### Webhook Security
- ✅ Signature verification
- ✅ IP whitelist validation
- ✅ Idempotency handling
- ✅ Proper error responses

## 🧪 Testing & Validation

### Test Scenarios
1. **Valid Address Validation**: Test with correct Nigerian addresses
2. **Invalid Address Handling**: Test with incomplete or invalid addresses
3. **Rate Calculation**: Test with various package weights and dimensions
4. **Error Scenarios**: Test API failures and network issues
5. **Edge Cases**: Test with extreme values and edge conditions

### Test Data
```php
// Test addresses for different Nigerian states
$testAddresses = [
    'lagos' => 'Victoria Island, Lagos, Lagos State, Nigeria',
    'abuja' => 'Wuse 2, Abuja, FCT, Nigeria',
    'kano' => 'Sabon Gari, Kano, Kano State, Nigeria',
];
```

## 📈 Performance Optimization

### Caching Strategy
- ✅ **Address Codes**: Cache validated address codes
- ✅ **Rate Responses**: Cache rates for identical requests
- ✅ **Location Data**: Cache state/LGA mappings
- ✅ **API Responses**: Cache successful responses with TTL

### Rate Limiting
- ✅ **Request Throttling**: Limit API calls per minute
- ✅ **Batch Processing**: Group multiple requests when possible
- ✅ **Queue Management**: Queue non-urgent requests
- ✅ **Circuit Breaker**: Temporarily disable API calls on repeated failures

## 🔍 Monitoring & Logging

### Comprehensive Logging
```php
Log::info('ShipBubble API Request:', [
    'endpoint' => $endpoint,
    'method' => strtoupper($method),
    'payload_size' => count($data),
    'timestamp' => now()->toISOString()
]);
```

### Metrics Tracked
- ✅ **API Response Times**: Monitor performance
- ✅ **Success/Failure Rates**: Track reliability
- ✅ **Error Frequencies**: Identify common issues
- ✅ **Cost Analysis**: Monitor shipping cost trends

## 🚀 Best Practices Implemented

### Code Quality
- ✅ **Type Hints**: Proper PHP type declarations
- ✅ **Error Handling**: Comprehensive exception handling
- ✅ **Documentation**: Inline code documentation
- ✅ **Testing**: Unit and integration tests

### API Usage
- ✅ **Efficient Requests**: Minimize unnecessary API calls
- ✅ **Data Validation**: Validate data before API requests
- ✅ **Graceful Degradation**: Handle API unavailability
- ✅ **User Experience**: Provide meaningful feedback

### Security
- ✅ **Input Sanitization**: Clean all user inputs
- ✅ **Output Encoding**: Properly encode API responses
- ✅ **Secure Storage**: Protect sensitive configuration
- ✅ **Audit Logging**: Log all API interactions

## 📋 Compliance Checklist

- ✅ **API Authentication**: Bearer token implementation
- ✅ **Request Format**: JSON payload structure
- ✅ **Response Handling**: Proper parsing and error handling
- ✅ **Address Validation**: Required before rate calculation
- ✅ **Package Data**: Correct format and units
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Rate Limiting**: Respect API limits
- ✅ **Security**: Secure API key management
- ✅ **Logging**: Comprehensive request/response logging
- ✅ **Testing**: Thorough testing coverage

## 📞 Support & Maintenance

### API Documentation Reference
- **Official Docs**: https://docs.shipbubble.com/
- **API Reference**: https://api.shipbubble.com/docs
- **Support**: <EMAIL>

### Maintenance Tasks
- **Monthly**: Review API usage and costs
- **Quarterly**: Update integration based on API changes
- **Annually**: Comprehensive security audit
- **As Needed**: Handle API deprecations and updates
