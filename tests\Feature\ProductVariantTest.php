<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Color;
use App\Models\Size;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use App\Models\User;
use App\Models\CartItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ProductVariantTest extends TestCase
{
    use RefreshDatabase;

    protected $vendor;
    protected $product;
    protected $color;
    protected $size;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();

        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'price' => 1000.00,
            'has_variants' => true,
        ]);

        $this->color = Color::factory()->create(['name' => 'Red', 'hex_code' => '#FF0000']);
        $this->size = Size::factory()->create(['name' => 'Medium', 'abbreviation' => 'M']);
    }

    /** @test */
    public function product_can_have_variants()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'price' => 1200.00,
            'stock_quantity' => 50,
        ]);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'price' => 1200.00,
        ]);

        $this->assertEquals(1, $this->product->variants()->count());
    }

    /** @test */
    public function variant_price_overrides_product_price()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'price' => 1500.00,
        ]);

        $this->assertEquals(1500.00, $variant->price);
        $this->assertNotEquals($this->product->price, $variant->price);
    }

    /** @test */
    public function variant_uses_product_price_when_null()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'price' => null, // Use product price
        ]);

        $this->assertEquals($this->product->price, $variant->effective_price);
    }

    /** @test */
    public function variant_stock_is_tracked_independently()
    {
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 10,
        ]);

        $variant2 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'stock_quantity' => 20,
        ]);

        $this->assertEquals(10, $variant1->stock_quantity);
        $this->assertEquals(20, $variant2->stock_quantity);
        $this->assertEquals(30, $this->product->variants()->sum('stock_quantity'));
    }

    /** @test */
    public function variant_can_be_out_of_stock()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 0,
        ]);

        $this->assertFalse($variant->is_in_stock);
        $this->assertTrue($variant->is_out_of_stock);
    }

    /** @test */
    public function variant_can_be_added_to_cart()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 10,
        ]);

        $cartItem = CartItem::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'product_variant_id' => $variant->id,
            'quantity' => 2,
            'price' => $variant->effective_price,
        ]);

        $this->assertDatabaseHas('cart_items', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'product_variant_id' => $variant->id,
            'quantity' => 2,
        ]);
    }

    /** @test */
    public function adding_variant_to_cart_reduces_stock()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 10,
        ]);

        // Simulate adding to cart and reducing stock
        $variant->decrement('stock_quantity', 2);

        $this->assertEquals(8, $variant->fresh()->stock_quantity);
    }

    /** @test */
    public function cannot_add_more_than_available_stock_to_cart()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 5,
        ]);

        // Try to add more than available stock
        $this->expectException(\Exception::class);
        
        if (6 > $variant->stock_quantity) {
            throw new \Exception('Insufficient stock');
        }
    }

    /** @test */
    public function variant_has_unique_sku()
    {
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'sku' => 'PROD-001-RED-M',
        ]);

        $variant2 = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'sku' => 'PROD-001-RED-L',
        ]);

        $this->assertNotEquals($variant1->sku, $variant2->sku);
        $this->assertEquals('PROD-001-RED-M', $variant1->sku);
        $this->assertEquals('PROD-001-RED-L', $variant2->sku);
    }

    /** @test */
    public function variant_generates_automatic_sku_if_not_provided()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'sku' => null,
        ]);

        $this->assertNotNull($variant->sku);
        $this->assertStringContains((string)$this->product->id, $variant->sku);
    }

    /** @test */
    public function product_with_variants_shows_price_range()
    {
        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'price' => 800.00,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'price' => 1200.00,
        ]);

        $priceRange = $this->product->price_range;

        $this->assertEquals(800.00, $priceRange['min']);
        $this->assertEquals(1200.00, $priceRange['max']);
    }

    /** @test */
    public function product_without_variants_shows_single_price()
    {
        $productWithoutVariants = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
            'price' => 500.00,
            'has_variants' => false,
        ]);

        $priceRange = $productWithoutVariants->price_range;

        $this->assertEquals(500.00, $priceRange['min']);
        $this->assertEquals(500.00, $priceRange['max']);
    }

    /** @test */
    public function variant_can_have_different_images()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'image_url' => 'https://example.com/red-variant.jpg',
        ]);

        $this->assertEquals('https://example.com/red-variant.jpg', $variant->image_url);
    }

    /** @test */
    public function variant_falls_back_to_product_image_when_none_specified()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'image_url' => null,
        ]);

        // Assuming product has a default image
        $this->assertEquals($this->product->featured_image_url, $variant->display_image_url);
    }

    /** @test */
    public function can_get_available_colors_for_product()
    {
        $redColor = Color::factory()->create(['name' => 'Red']);
        $blueColor = Color::factory()->create(['name' => 'Blue']);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $redColor->id,
            'size_id' => $this->size->id,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $blueColor->id,
            'size_id' => $this->size->id,
        ]);

        $availableColors = $this->product->available_colors;

        $this->assertCount(2, $availableColors);
        $this->assertTrue($availableColors->contains('name', 'Red'));
        $this->assertTrue($availableColors->contains('name', 'Blue'));
    }

    /** @test */
    public function can_get_available_sizes_for_product()
    {
        $smallSize = Size::factory()->create(['name' => 'Small']);
        $largeSize = Size::factory()->create(['name' => 'Large']);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $smallSize->id,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $largeSize->id,
        ]);

        $availableSizes = $this->product->available_sizes;

        $this->assertCount(2, $availableSizes);
        $this->assertTrue($availableSizes->contains('name', 'Small'));
        $this->assertTrue($availableSizes->contains('name', 'Large'));
    }

    /** @test */
    public function can_find_variant_by_color_and_size()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
        ]);

        $foundVariant = $this->product->findVariant($this->color->id, $this->size->id);

        $this->assertNotNull($foundVariant);
        $this->assertEquals($variant->id, $foundVariant->id);
    }

    /** @test */
    public function returns_null_when_variant_not_found()
    {
        $nonExistentColor = Color::factory()->create(['name' => 'Purple']);
        
        $foundVariant = $this->product->findVariant($nonExistentColor->id, $this->size->id);

        $this->assertNull($foundVariant);
    }

    /** @test */
    public function variant_can_be_disabled()
    {
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'is_active' => false,
        ]);

        $this->assertFalse($variant->is_active);
        
        // Disabled variants should not appear in available variants
        $activeVariants = $this->product->variants()->where('is_active', true)->get();
        $this->assertCount(0, $activeVariants);
    }

    /** @test */
    public function product_total_stock_includes_all_variant_stock()
    {
        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 10,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'stock_quantity' => 15,
        ]);

        $totalStock = $this->product->total_variant_stock;

        $this->assertEquals(25, $totalStock);
    }

    /** @test */
    public function product_is_in_stock_if_any_variant_has_stock()
    {
        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 0,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'stock_quantity' => 5,
        ]);

        $this->assertTrue($this->product->has_stock);
    }

    /** @test */
    public function product_is_out_of_stock_if_all_variants_are_out_of_stock()
    {
        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'stock_quantity' => 0,
        ]);

        ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => Size::factory()->create(['name' => 'Large'])->id,
            'stock_quantity' => 0,
        ]);

        $this->assertFalse($this->product->has_stock);
    }
}
