<div>
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-3 sm:space-y-0">
            <div>
                <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Product Variants</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $product->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('vendor.products.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Products
                </a>
                <button wire:click="create" 
                        class="w-full sm:w-auto bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                    <i class="fas fa-plus mr-2"></i>
                    Add Variant
                </button>
            </div>
        </div>

        <!-- Product Info Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    @if($product->image_url)
                        <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="w-16 h-16 object-cover rounded-lg">
                    @else
                        <i class="fas fa-image text-gray-400 text-xl"></i>
                    @endif
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $product->name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Base Price: ₦{{ number_format($product->price, 2) }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Total Variants: {{ $variants->total() }}</p>
                </div>
            </div>
        </div>

        <!-- Mobile Card View (Hidden on Desktop) -->
        <div class="block lg:hidden space-y-4 mb-6">
            @forelse($variants as $variant)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden">
                    <div class="p-4 space-y-3">
                        <!-- Variant Header -->
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-semibold text-gray-900 dark:text-white text-sm">{{ $variant->display_name }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 font-mono">{{ $variant->sku }}</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $variant->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $variant->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <!-- Variant Details -->
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Price:</span>
                                <span class="text-gray-900 dark:text-white font-medium">₦{{ number_format($variant->price, 2) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Stock:</span>
                                <span class="text-gray-900 dark:text-white font-medium">{{ $variant->stock_quantity }}</span>
                            </div>
                            @if($variant->color)
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Color:</span>
                                    <div class="flex items-center space-x-1">
                                        <div class="w-3 h-3 rounded-full border border-gray-300" style="background-color: {{ $variant->color->hex_code }}"></div>
                                        <span class="text-gray-900 dark:text-white font-medium">{{ $variant->color->name }}</span>
                                    </div>
                                </div>
                            @endif
                            @if($variant->size)
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Size:</span>
                                    <span class="text-gray-900 dark:text-white font-medium">{{ $variant->size->name }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Variant Actions -->
                        <div class="flex items-center justify-end space-x-3 pt-3 border-t border-gray-100 dark:border-gray-600">
                            <button wire:click="toggleStatus({{ $variant->id }})"
                                    class="inline-flex items-center px-3 py-1.5 {{ $variant->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white text-xs font-medium rounded-lg transition-colors">
                                <i class="fas {{ $variant->is_active ? 'fa-pause' : 'fa-play' }} mr-1"></i>
                                {{ $variant->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                            <button wire:click="edit({{ $variant->id }})"
                                    class="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button wire:click="delete({{ $variant->id }})"
                                    wire:confirm="Are you sure you want to delete this variant?"
                                    class="inline-flex items-center px-3 py-1.5 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12 bg-white dark:bg-gray-800 rounded-xl">
                    <i class="fas fa-palette text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">No variants found for this product.</p>
                    <button wire:click="create" class="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                        <i class="fas fa-plus mr-2"></i>
                        Create First Variant
                    </button>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on Mobile) -->
        <div class="hidden lg:block bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Variant</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">SKU</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Stock</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($variants as $variant)
                            <tr wire:key="{{ $variant->id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-3">
                                        @if($variant->color)
                                            <div class="w-6 h-6 rounded-full border border-gray-300" style="background-color: {{ $variant->color->hex_code }}" title="{{ $variant->color->name }}"></div>
                                        @endif
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $variant->display_name }}</div>
                                            @if($variant->size)
                                                <div class="text-sm text-gray-500 dark:text-gray-400">Size: {{ $variant->size->name }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 font-mono">{{ $variant->sku }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white font-medium">₦{{ number_format($variant->price, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $variant->stock_quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $variant->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $variant->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button wire:click="toggleStatus({{ $variant->id }})"
                                                class="text-{{ $variant->is_active ? 'yellow' : 'green' }}-600 hover:text-{{ $variant->is_active ? 'yellow' : 'green' }}-900 transition-colors">
                                            <i class="fas {{ $variant->is_active ? 'fa-pause' : 'fa-play' }}"></i>
                                        </button>
                                        <button wire:click="edit({{ $variant->id }})"
                                                class="text-blue-600 hover:text-blue-900 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button wire:click="delete({{ $variant->id }})"
                                                wire:confirm="Are you sure you want to delete this variant?"
                                                class="text-red-600 hover:text-red-900 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-12">
                                    <i class="fas fa-palette text-gray-400 text-4xl mb-4"></i>
                                    <p class="text-gray-500 dark:text-gray-400 mb-4">No variants found for this product.</p>
                                    <button wire:click="create" class="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                                        <i class="fas fa-plus mr-2"></i>
                                        Create First Variant
                                    </button>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if($variants->hasPages())
            <div class="mt-6">
                {{ $variants->links() }}
            </div>
        @endif
    </div>

    <!-- Create/Edit Modal -->
    @if($showCreateModal || $showEditModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" wire:click="closeModal">
            <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white" wire:click.stop>
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        {{ $showCreateModal ? 'Create New Variant' : 'Edit Variant' }}
                    </h3>
                    
                    <form wire:submit.prevent="{{ $showCreateModal ? 'store' : 'update' }}">
                        <div class="mb-4">
                            <label for="color_id" class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                            <select wire:model.live="color_id" id="color_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black">
                                <option value="">Select Color (Optional)</option>
                                @foreach($colors as $color)
                                    <option value="{{ $color->id }}">{{ $color->name }}</option>
                                @endforeach
                            </select>
                            @error('color_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="mb-4">
                            <label for="size_id" class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                            <select wire:model.live="size_id" id="size_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black">
                                <option value="">Select Size (Optional)</option>
                                @foreach($sizes as $size)
                                    <option value="{{ $size->id }}">{{ $size->name }}</option>
                                @endforeach
                            </select>
                            @error('size_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="mb-4">
                            <label for="sku" class="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                            <input type="text" wire:model="sku" id="sku" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black font-mono" placeholder="Auto-generated">
                            @error('sku') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="mb-4">
                            <label for="price_adjustment" class="block text-sm font-medium text-gray-700 mb-2">Price Adjustment (₦)</label>
                            <input type="number" step="0.01" wire:model="price_adjustment" id="price_adjustment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black" placeholder="0.00">
                            <p class="text-xs text-gray-500 mt-1">Leave empty for no adjustment. Use negative values for discounts.</p>
                            @error('price_adjustment') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="mb-4">
                            <label for="stock_quantity" class="block text-sm font-medium text-gray-700 mb-2">Stock Quantity</label>
                            <input type="number" min="0" wire:model="stock_quantity" id="stock_quantity" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black" placeholder="0">
                            @error('stock_quantity') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" wire:model="is_active" class="rounded border-gray-300 text-black focus:ring-black">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" wire:click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800 transition-colors">
                                {{ $showCreateModal ? 'Create' : 'Update' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
