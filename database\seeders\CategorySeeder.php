<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Clothing',
                'description' => 'Fashion and apparel for men, women, and children',
                'is_active' => true,
                'order' => 1,
                'subcategories' => [
                    ['name' => 'Men\'s Clothing', 'description' => 'Clothing for men'],
                    ['name' => 'Women\'s Clothing', 'description' => 'Clothing for women'],
                    ['name' => 'Kids\' Clothing', 'description' => 'Clothing for children'],
                    ['name' => 'Activewear', 'description' => 'Sports and fitness clothing'],
                ]
            ],
            [
                'name' => 'Footwear',
                'description' => 'Shoes, sneakers, and footwear for all occasions',
                'is_active' => true,
                'order' => 2,
                'subcategories' => [
                    ['name' => 'Sneakers', 'description' => 'Casual and athletic sneakers'],
                    ['name' => 'Formal Shoes', 'description' => 'Dress shoes and formal footwear'],
                    ['name' => 'Sandals', 'description' => 'Casual sandals and flip-flops'],
                    ['name' => 'Boots', 'description' => 'Boots for all seasons'],
                ]
            ],
            [
                'name' => 'Accessories',
                'description' => 'Fashion accessories and jewelry',
                'is_active' => true,
                'order' => 3,
                'subcategories' => [
                    ['name' => 'Bags', 'description' => 'Handbags, backpacks, and luggage'],
                    ['name' => 'Jewelry', 'description' => 'Necklaces, rings, and accessories'],
                    ['name' => 'Watches', 'description' => 'Timepieces and smartwatches'],
                    ['name' => 'Sunglasses', 'description' => 'Eyewear and sunglasses'],
                ]
            ],
            [
                'name' => 'Electronics',
                'description' => 'Consumer electronics and gadgets',
                'is_active' => true,
                'order' => 4,
                'subcategories' => [
                    ['name' => 'Smartphones', 'description' => 'Mobile phones and accessories'],
                    ['name' => 'Laptops', 'description' => 'Computers and laptops'],
                    ['name' => 'Audio', 'description' => 'Headphones, speakers, and audio gear'],
                    ['name' => 'Gaming', 'description' => 'Gaming consoles and accessories'],
                ]
            ],
            [
                'name' => 'Home & Living',
                'description' => 'Home decor and living essentials',
                'is_active' => true,
                'order' => 5,
                'subcategories' => [
                    ['name' => 'Furniture', 'description' => 'Home and office furniture'],
                    ['name' => 'Decor', 'description' => 'Home decoration and art'],
                    ['name' => 'Kitchen', 'description' => 'Kitchen appliances and utensils'],
                    ['name' => 'Bedding', 'description' => 'Bed sheets, pillows, and bedding'],
                ]
            ],
            [
                'name' => 'Beauty & Health',
                'description' => 'Beauty products and health essentials',
                'is_active' => true,
                'order' => 6,
                'subcategories' => [
                    ['name' => 'Skincare', 'description' => 'Skincare products and treatments'],
                    ['name' => 'Makeup', 'description' => 'Cosmetics and makeup products'],
                    ['name' => 'Haircare', 'description' => 'Hair products and styling tools'],
                    ['name' => 'Wellness', 'description' => 'Health and wellness products'],
                ]
            ],
            [
                'name' => 'Sports & Outdoors',
                'description' => 'Sports equipment and outdoor gear',
                'is_active' => true,
                'order' => 7,
                'subcategories' => [
                    ['name' => 'Fitness', 'description' => 'Gym and fitness equipment'],
                    ['name' => 'Outdoor Gear', 'description' => 'Camping and hiking equipment'],
                    ['name' => 'Team Sports', 'description' => 'Equipment for team sports'],
                    ['name' => 'Water Sports', 'description' => 'Swimming and water sports gear'],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            // Create parent category
            $category = Category::updateOrCreate(
                ['slug' => Str::slug($categoryData['name'])],
                [
                    'name' => $categoryData['name'],
                    'description' => $categoryData['description'],
                    'is_active' => $categoryData['is_active'],
                    'order' => $categoryData['order'],
                    'parent_id' => null,
                ]
            );

            // Create subcategories
            if (isset($categoryData['subcategories'])) {
                foreach ($categoryData['subcategories'] as $index => $subcat) {
                    Category::updateOrCreate(
                        ['slug' => Str::slug($subcat['name'])],
                        [
                            'name' => $subcat['name'],
                            'description' => $subcat['description'],
                            'is_active' => true,
                            'order' => $index + 1,
                            'parent_id' => $category->id,
                        ]
                    );
                }
            }
        }

        $this->command->info('Created comprehensive category structure with subcategories');
    }
}
