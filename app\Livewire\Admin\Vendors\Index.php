<?php

namespace App\Livewire\Admin\Vendors;

use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;

#[Layout('layouts.admin')]
class Index extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortBy = 'created_at';
    public string $sortDirection = 'desc';

    public function mount()
    {
        // SECURITY FIX: Add proper admin authorization check
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    public function approve(Vendor $vendor): void
    {
        \Log::info('Admin Vendor Approval Started', [
            'admin_user_id' => Auth::id(),
            'vendor_id' => $vendor->id,
            'vendor_name' => $vendor->business_name,
            'vendor_user_id' => $vendor->user_id,
            'timestamp' => now()->toISOString()
        ]);

        $vendor->update(['is_approved' => true]);

        \Log::info('Admin Vendor Approval Completed', [
            'admin_user_id' => Auth::id(),
            'vendor_id' => $vendor->id,
            'is_approved' => true
        ]);

        session()->flash('success', 'Vendor approved successfully.');
    }

    public function reject(Vendor $vendor): void
    {
        \Log::info('Admin Vendor Rejection Started', [
            'admin_user_id' => Auth::id(),
            'vendor_id' => $vendor->id,
            'vendor_name' => $vendor->business_name,
            'vendor_user_id' => $vendor->user_id,
            'timestamp' => now()->toISOString()
        ]);

        $vendor->update(['is_approved' => false]);

        \Log::info('Admin Vendor Rejection Completed', [
            'admin_user_id' => Auth::id(),
            'vendor_id' => $vendor->id,
            'is_approved' => false
        ]);

        session()->flash('success', 'Vendor rejected successfully.');
    }

    public function toggleFeatured(Vendor $vendor): void
    {
        $vendor->update(['is_featured' => !$vendor->is_featured]);
        session()->flash('success', 'Vendor featured status updated successfully.');
    }

    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortBy = $field;
    }

    public function render()
    {
        $vendors = Vendor::query()
            ->with('user')
            ->when($this->search, function ($query) {
                $query->where('shop_name', 'like', '%' . $this->search . '%')
                    ->orWhereHas('user', function ($query) {
                        $query->where('email', 'like', '%' . $this->search . '%');
                    });
            })
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate(15);

        return view('livewire.admin.vendors.index', [
            'vendors' => $vendors,
        ])->layout('layouts.admin');
    }
}
