<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto pt-16 sm:pt-20 lg:pt-24 pb-24 px-4 sm:px-6 lg:px-8">

        <!-- Error Messages -->
        @if (session()->has('error'))
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4" x-data="{ show: true }" x-show="show" x-transition>
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button @click="show = false" class="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Success Messages -->
        @if (session()->has('success'))
            <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4" x-data="{ show: true }" x-show="show" x-transition>
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button @click="show = false" class="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if($cartItems->isNotEmpty())
            <!-- Cart with Items -->
            <div class="lg:grid lg:grid-cols-3 lg:gap-8 xl:gap-12">
                <!-- Cart Items Section -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                        <!-- Cart Header -->
                        <div class="px-4 sm:px-6 py-4 sm:py-5 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                <h1 class="text-xl sm:text-2xl font-bold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    <span class="hidden sm:inline">Shopping Cart</span>
                                    <span class="sm:hidden">Cart</span>
                                    <span class="ml-1">({{ $cartItems->count() }})</span>
                                </h1>
                                <button type="button" wire:click="clearCart" wire:confirm="Are you sure you want to clear your cart?"
                                        class="px-3 py-2 sm:px-4 sm:py-2 text-sm text-red-600 hover:text-white hover:bg-red-600 font-semibold rounded-lg border border-red-200 hover:border-red-600 transition-all duration-200 self-start sm:self-auto">
                                    Clear all
                                </button>
                            </div>
                        </div>

                        <!-- Cart Items List -->
                        <div class="divide-y divide-gray-100">

                            @foreach ($cartItems as $item)
                                <div class="p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200">
                                    <div class="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
                                        <!-- Product Image -->
                                        <div class="flex-shrink-0 self-center sm:self-start">
                                            <x-image.responsive
                                                :src="$item['image'] ?? $item['image_url'] ?? null"
                                                :alt="$item['name']"
                                                width="96"
                                                height="96"
                                                aspect-ratio="square"
                                                sizes="(max-width: 640px) 80px, 96px"
                                                loading="eager"
                                                class="w-20 h-20 sm:w-24 sm:h-24 rounded-xl border-2 border-gray-200 shadow-sm"
                                            />
                                        </div>

                                        <!-- Product Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-3 sm:space-y-0">
                                                <div class="flex-1 sm:pr-4">
                                                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 line-clamp-2 mb-2">{{ $item['name'] }}</h3>

                                                    <!-- Variant Information -->
                                                    @if(isset($item['variant_name']) && $item['variant_name'])
                                                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                                                            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                            </svg>
                                                            Variant: <span class="font-medium">{{ $item['variant_name'] }}</span>
                                                        </p>
                                                    @endif

                                                    @if(isset($item['sku']) && $item['sku'])
                                                        <p class="text-xs text-gray-500 mb-2 font-mono">SKU: {{ $item['sku'] }}</p>
                                                    @endif

                                                    @if(isset($item['vendor_name']))
                                                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                                                            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                            </svg>
                                                            Sold by: <span class="font-medium">{{ $item['vendor_name'] }}</span>
                                                        </p>
                                                    @endif
                                                    <div class="flex items-center space-x-2">
                                                        <span class="text-xl font-bold text-gray-900">@currency($item['price'])</span>
                                                        <span class="text-sm text-gray-500">per item</span>
                                                    </div>
                                                </div>

                                                <!-- Remove Button -->
                                                <button type="button"
                                                        wire:click="removeItem('{{ $item['id'] }}')"
                                                        wire:loading.attr="disabled"
                                                        wire:target="removeItem('{{ $item['id'] }}')"
                                                        wire:confirm="Are you sure you want to remove this item?"
                                                        class="text-gray-400 hover:text-red-600 hover:bg-red-50 p-2 sm:p-3 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed min-w-[40px] min-h-[40px] sm:min-w-[44px] sm:min-h-[44px] flex items-center justify-center group border border-transparent hover:border-red-200 self-start"
                                                        title="Remove item from cart">
                                                    <span wire:loading.remove wire:target="removeItem('{{ $item['id'] }}')">
                                                        <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </span>
                                                    <span wire:loading wire:target="removeItem('{{ $item['id'] }}')">
                                                        <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                        </svg>
                                                    </span>
                                                </button>
                                            </div>

                                            <!-- Quantity and Subtotal -->
                                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 sm:mt-6 pt-4 border-t border-gray-100 space-y-3 sm:space-y-0">
                                                <div class="flex items-center justify-center sm:justify-start">
                                                    <span class="text-sm font-medium text-gray-600 mr-3 sm:mr-4">Quantity:</span>
                                                    <div class="flex items-center border-2 border-gray-200 rounded-lg sm:rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 bg-white">
                                                    <button type="button"
                                                            wire:click="decrementQuantity('{{ $item['id'] }}')"
                                                            wire:loading.attr="disabled"
                                                            wire:target="decrementQuantity('{{ $item['id'] }}')"
                                                            class="px-3 py-2 sm:px-4 sm:py-3 min-w-[40px] min-h-[40px] sm:min-w-[48px] sm:min-h-[48px] flex items-center justify-center text-gray-600 hover:text-white hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 group"
                                                            aria-label="Decrease quantity"
                                                            title="Decrease quantity">
                                                        <span wire:loading.remove wire:target="decrementQuantity('{{ $item['id'] }}')">
                                                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M20 12H4"></path>
                                                            </svg>
                                                        </span>
                                                        <span wire:loading wire:target="decrementQuantity('{{ $item['id'] }}')">
                                                            <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                    <div class="px-4 py-2 sm:px-6 sm:py-3 min-w-[60px] sm:min-w-[80px] h-10 sm:h-12 flex items-center justify-center text-gray-900 font-bold text-base sm:text-lg bg-gray-50 border-x-2 border-gray-200">
                                                        {{ $item['quantity'] }}
                                                    </div>
                                                    <button type="button"
                                                            wire:click="incrementQuantity('{{ $item['id'] }}')"
                                                            wire:loading.attr="disabled"
                                                            wire:target="incrementQuantity('{{ $item['id'] }}')"
                                                            class="px-3 py-2 sm:px-4 sm:py-3 min-w-[40px] min-h-[40px] sm:min-w-[48px] sm:min-h-[48px] flex items-center justify-center text-gray-600 hover:text-white hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 group"
                                                            aria-label="Increase quantity"
                                                            title="Increase quantity">
                                                        <span wire:loading.remove wire:target="incrementQuantity('{{ $item['id'] }}')">
                                                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
                                                            </svg>
                                                        </span>
                                                        <span wire:loading wire:target="incrementQuantity('{{ $item['id'] }}')">
                                                            <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                    </div>
                                                </div>

                                                <div class="text-center sm:text-right">
                                                    <div class="text-sm text-gray-500 mb-1">Subtotal</div>
                                                    <div class="text-lg sm:text-xl font-bold text-gray-900">
                                                        @currency($item['price'] * $item['quantity'])
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Order Summary Section -->
                <div class="lg:col-span-1 mt-8 lg:mt-0">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 sticky top-24 lg:top-32 overflow-hidden">
                        <div class="px-6 py-5 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                Order Summary
                            </h2>
                        </div>

                        <div class="px-6 py-6 space-y-5">
                            <div class="flex justify-between items-center py-2">
                                <span class="text-gray-600 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    Subtotal ({{ $cartItems->count() }} {{ Str::plural('item', $cartItems->count()) }})
                                </span>
                                <span class="font-semibold text-gray-900">@currency($subtotal)</span>
                            </div>

                            <div class="flex justify-between items-center py-2">
                                <span class="text-gray-600 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    Shipping
                                </span>
                                <span class="text-gray-600 text-sm">Calculated at checkout</span>
                            </div>

                            <div class="border-t-2 border-gray-200 pt-5">
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-xl font-bold text-gray-900">Total</span>
                                    <span class="text-2xl font-bold text-gray-900">@currency($subtotal)</span>
                                </div>
                            </div>
                        </div>

                        <div class="px-6 py-6 bg-gray-50 border-t border-gray-200">
                            <a href="{{ route('checkout.index') }}"
                               class="block w-full bg-gradient-to-r from-gray-900 to-black text-white py-4 px-6 rounded-xl font-bold hover:from-black hover:to-gray-800 transition-all duration-300 text-center shadow-lg hover:shadow-xl transform hover:scale-105 text-lg min-h-[56px] flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                Proceed to Checkout
                            </a>

                            <a href="{{ route('products.index') }}"
                               class="block w-full text-center text-gray-600 hover:text-gray-900 py-4 text-sm font-semibold transition-colors duration-200 hover:bg-gray-100 rounded-lg mt-3">
                                ← Continue Shopping
                            </a>

                            <!-- Trust Indicators -->
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="grid grid-cols-1 gap-3">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-5 h-5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="font-medium">Secure checkout with SSL encryption</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-5 h-5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="font-medium">Fast & reliable delivery</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-5 h-5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="font-medium">Quality guarantee</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="px-6 py-4 bg-gray-50 rounded-b-lg">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Secure checkout powered by Paystack
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- Empty Cart -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                    <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your cart is empty</h2>
                    <p class="text-gray-600 mb-8">Start shopping to add items to your cart</p>
                    <a href="{{ route('products.index') }}"
                       class="inline-flex items-center px-6 py-3 bg-black text-white font-medium rounded-lg hover:bg-gray-800 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Continue Shopping
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
