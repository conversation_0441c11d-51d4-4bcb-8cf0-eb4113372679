<div wire:loading.class.delay="opacity-50">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-4">
        <div class="text-xs sm:text-sm text-gray-500 order-2 sm:order-1">
            @if($products->total() > 0)
                <span class="hidden sm:inline">Showing</span> <span class="font-medium text-gray-900">{{ $products->firstItem() }}</span> - <span class="font-medium text-gray-900">{{ $products->lastItem() }}</span> of <span class="font-medium text-gray-900">{{ $products->total() }}</span> results
            @else
                <span class="font-medium text-gray-900">0</span> results
            @endif
        </div>
        <div class="flex items-center w-full sm:w-auto order-1 sm:order-2">
            <input wire:model.live.debounce.300ms="search"
                   type="text"
                   placeholder="Search products..."
                   class="form-input w-full sm:w-64 min-h-[44px] px-4 py-3 text-sm border border-gray-300 rounded-lg
                          focus:ring-2 focus:ring-black focus:border-black transition-all duration-200">
        </div>
    </div>

    {{-- Standardized responsive grid with proper Tailwind breakpoints --}}
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
        @forelse($products as $product)
            {{-- CRITICAL FIX RD2: Use unified product card component --}}
            <x-products.card :product="$product" />
        @empty
            <div class="col-span-full">
                <div class="flex flex-col items-center justify-center py-12 sm:py-16 text-center bg-gray-50 rounded-lg">
                    <svg class="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path></svg>
                    <h3 class="mt-3 sm:mt-4 text-lg sm:text-xl font-semibold text-gray-800">No Products Found</h3>
                    <p class="mt-2 text-sm text-gray-500 max-w-md">Sorry, we couldn't find any products matching your criteria. Try adjusting your search or filters.</p>
                    <button wire:click="clearFilters" class="mt-4 sm:mt-6 px-4 py-2 text-sm font-medium text-white bg-black border border-transparent rounded-lg shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-all duration-200">Reset Filters</button>
                </div>
            </div>
        @endforelse
    </div>

    @if($products->hasPages())
    <div class="mt-6 sm:mt-8">
        {{ $products->links() }}
    </div>
    @endif
</div>
