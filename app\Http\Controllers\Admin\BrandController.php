<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use Illuminate\Http\Request;

class BrandController extends Controller
{
    /**
     * Display a listing of brands
     */
    public function index()
    {
        return view('admin.brands.index');
    }

    /**
     * Show the form for creating a new brand
     */
    public function create()
    {
        return view('admin.brands.create');
    }

    /**
     * Show the form for editing the specified brand
     */
    public function edit(Brand $brand)
    {
        return view('admin.brands.edit', compact('brand'));
    }

    /**
     * Display the specified brand
     */
    public function show(Brand $brand)
    {
        $brand->load(['products', 'vendor']);
        return view('admin.brands.show', compact('brand'));
    }

    /**
     * Remove the specified brand from storage
     */
    public function destroy(Brand $brand)
    {
        // Check if brand has products
        if ($brand->products()->count() > 0) {
            return redirect()
                ->route('admin.brands.index')
                ->with('error', 'Cannot delete brand that has products.');
        }

        $brand->delete();
        
        return redirect()
            ->route('admin.brands.index')
            ->with('success', 'Brand deleted successfully.');
    }
}
