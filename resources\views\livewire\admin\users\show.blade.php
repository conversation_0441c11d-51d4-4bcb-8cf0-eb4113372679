<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $user->name }}</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">User Details & Management</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{{ route('admin.users.edit', $user) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                <i class="fas fa-edit mr-2"></i>
                Edit User
            </a>
            <a href="{{ route('admin.users.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Users
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main User Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- User Details Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">User Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                            {{ $user->role->name === 'admin' ? 'bg-red-100 text-red-800' : 
                               ($user->role->name === 'vendor' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800') }}">
                            {{ ucfirst($user->role->name) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Verified</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $user->email_verified_at ? 'Verified' : 'Not Verified' }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Joined</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->created_at->format('M d, Y') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->updated_at->format('M d, Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Vendor Information (if applicable) -->
            @if($user->vendor)
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Vendor Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Shop Name</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->vendor->shop_name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->vendor->status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($user->vendor->status) }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Products</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $user->vendor->products->count() ?? 0 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Balance</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">₦{{ number_format($user->vendor->balance ?? 0, 2) }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ route('admin.vendors.show', $user->vendor) }}" 
                       class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-store mr-2"></i>
                        View Vendor Details
                    </a>
                </div>
            </div>
            @endif

            <!-- Recent Activity -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h2>
                
                @if($user->orders->count() > 0)
                    <div class="space-y-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Orders</h3>
                        @foreach($user->orders->take(5) as $order)
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Order #{{ $order->id }}</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">{{ $order->created_at->format('M d, Y') }}</p>
                                </div>
                                <span class="text-sm text-gray-900 dark:text-white">₦{{ number_format($order->total, 2) }}</span>
                            </div>
                        @endforeach
                    </div>
                @endif

                @if($user->reviews->count() > 0)
                    <div class="space-y-3 mt-6">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Reviews</h3>
                        @foreach($user->reviews->take(3) as $review)
                            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $review->product->name ?? 'Product' }}</p>
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star text-xs {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                        @endfor
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ Str::limit($review->comment, 100) }}</p>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Avatar -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Profile</h2>
                <div class="flex flex-col items-center">
                    <div class="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-user text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $user->name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $user->email }}</p>
                </div>
            </div>

            <!-- User Stats -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Statistics</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Orders</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $user->orders->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Reviews</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $user->reviews->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Spent</span>
                        <span class="text-sm text-gray-900 dark:text-white">₦{{ number_format($user->orders->sum('total'), 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Member Since</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $user->created_at->format('M Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.users.edit', $user) }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit User
                    </a>
                    
                    @if($user->vendor)
                        <a href="{{ route('admin.vendors.show', $user->vendor) }}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors">
                            <i class="fas fa-store mr-2"></i>
                            View Vendor
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
