<?php

namespace App\Livewire\Product;

use App\Models\Product;
use Livewire\Component;

class Gallery extends Component
{
    public Product $product;
    public $images;
    public $selectedImage;

    public function mount(Product $product)
    {
        $this->product = $product;
        
        try {
            $this->images = $this->product->gallery_images;
            \Log::info('Gallery images loaded', ['product_id' => $product->id, 'count' => $this->images->count()]);
        } catch (\Exception $e) {
            \Log::error('Product gallery images error', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
            $this->images = collect();
        }
        
        if ($this->images->isEmpty()) {
            $this->images = collect([
                [
                    'url' => asset('images/product-placeholder.svg'),
                    'thumb' => asset('images/product-placeholder.svg'),
                    'alt' => $this->product->name ?? 'Product Image'
                ]
            ]);
        }
        
        $this->selectedImage = $this->images->first();
    }

    public function selectImage($imageData)
    {
        \Log::info('Gallery Image Selection Started', [
            'product_id' => $this->product->id,
            'image_data' => $imageData,
            'total_images' => $this->images ? $this->images->count() : 0,
            'current_selected_image' => $this->selectedImage['url'] ?? 'none',
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]);

        if (!$imageData || !$this->images) {
            \Log::warning('Gallery Image Selection Failed - Invalid Data', [
                'product_id' => $this->product->id,
                'image_data_provided' => $imageData !== null,
                'images_collection_exists' => $this->images !== null
            ]);
            return;
        }

        // Find the image in our collection
        $image = $this->images->firstWhere('url', $imageData);
        if ($image) {
            $this->selectedImage = $image;
            \Log::info('Gallery Image Selected Successfully', [
                'product_id' => $this->product->id,
                'selected_image_url' => $image['url'] ?? 'none',
                'selected_image_alt' => $image['alt'] ?? 'none'
            ]);
        } else {
            \Log::warning('Gallery Image Not Found in Collection', [
                'product_id' => $this->product->id,
                'requested_image_data' => $imageData,
                'available_image_urls' => $this->images->pluck('url')->toArray()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.product.gallery');
    }
}
