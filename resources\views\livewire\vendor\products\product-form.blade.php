<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    {{ $product->exists ? 'Edit Product' : 'Create New Product' }}
                </h1>
                <p class="text-gray-300 text-lg">
                    {{ $product->exists ? 'Update your product details and settings' : 'Add a new product to your store inventory' }}
                </p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.products.index') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Cancel</span>
                </a>
                <button type="submit" form="product-form"
                        class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 hover:shadow-lg flex items-center space-x-2">
                    <i class="fa-solid fa-save transition-transform duration-300 group-hover:scale-110"></i>
                    <span>{{ $product->exists ? 'Update Product' : 'Save Product' }}</span>
                </button>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="save" id="product-form" class="space-y-8">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {{-- Main Content --}}
            <div class="lg:col-span-2 space-y-8">
                {{-- Basic Information Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-info-circle text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Product Information</h3>
                                <p class="text-gray-600 text-sm">Basic details about your product</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-2">
                            <label for="name" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Product Name *</label>
                            <input type="text"
                                   wire:model.live.debounce.500ms="product.name"
                                   id="name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg"
                                   placeholder="Enter product name...">
                            @error('product.name')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="space-y-2">
                            <label for="description" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Description *</label>
                            <textarea wire:model.debounce.500ms="product.description" id="description" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg" placeholder="Enter product description..."></textarea>
                            @error('product.description')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>

                {{-- Pricing & Inventory Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-green-500 rounded-lg">
                                <i class="fas fa-tag text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Pricing & Inventory</h3>
                                <p class="text-gray-600 text-sm">Set your pricing and stock levels</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Price *</label>
                                <input type="number" step="0.01" min="0" wire:model.debounce.500ms="product.price" id="price" class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg" placeholder="0.00">
                                @error('product.price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="discount_price" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Discount Price</label>
                                <input type="number" step="0.01" min="0" wire:model.debounce.500ms="product.discount_price" id="discount_price" class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg" placeholder="0.00">
                                @error('product.discount_price')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="stock" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">Stock *</label>
                                <input type="number" min="0" wire:model.debounce.500ms="product.stock" id="stock" class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg" placeholder="0">
                                @error('product.stock')
                                    <p class="text-red-500 text-sm flex items-center mt-1">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label for="sku" class="block text-sm font-semibold text-gray-700 uppercase tracking-wide">SKU</label>
                            <input type="text" wire:model.debounce.500ms="product.sku" id="sku" class="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 text-lg" placeholder="Enter SKU...">
                            @error('product.sku')
                                <p class="text-red-500 text-sm flex items-center mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>

                {{-- Media Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-violet-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-purple-500 rounded-lg">
                                <i class="fas fa-image text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Media</h3>
                                <p class="text-gray-600 text-sm">Upload product images</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Main Image</label>
                                <input type="file" wire:model="image" class="mt-1 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-700">
                                @error('image')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Gallery Images</label>
                                <input type="file" wire:model="gallery" multiple class="mt-1 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-black file:text-white hover:file:bg-gray-700">
                                @error('gallery.*')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Variants Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-yellow-50 to-amber-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-yellow-500 rounded-lg">
                                <i class="fas fa-list text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Variants</h3>
                                <p class="text-gray-600 text-sm">Add product variations (e.g., size, color)</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-4">
                            @foreach ($variants as $index => $variant)
                                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700">Variant Name</label>
                                        <input type="text" wire:model="variants.{{ $index }}.name" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm" placeholder="e.g., Color">
                                        @error('variants.'.$index.'.name')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700">Variant Value</label>
                                        <input type="text" wire:model="variants.{{ $index }}.value" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm" placeholder="e.g., Red">
                                        @error('variants.'.$index.'.value')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div>
                                        <button type="button" wire:click="removeVariant({{ $index }})" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                            <button type="button" wire:click="addVariant" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                Add Variant
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Specifications Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-red-50 to-orange-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-red-500 rounded-lg">
                                <i class="fas fa-cog text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Specifications</h3>
                                <p class="text-gray-600 text-sm">Add technical specifications</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-6">
                        <div class="space-y-4">
                            @foreach ($specifications as $index => $specification)
                                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700">Key</label>
                                        <input type="text" wire:model="specifications.{{ $index }}.key" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm" placeholder="e.g., Material">
                                        @error('specifications.'.$index.'.key')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700">Value</label>
                                        <input type="text" wire:model="specifications.{{ $index }}.value" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm" placeholder="e.g., Cotton">
                                        @error('specifications.'.$index.'.value')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div>
                                        <button type="button" wire:click="removeSpecification({{ $index }})" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                            <button type="button" wire:click="addSpecification" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                Add Specification
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Sidebar --}}
            <div class="space-y-8">
                {{-- Status Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-gray-500 rounded-lg">
                                <i class="fas fa-toggle-on text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Status</h3>
                                <p class="text-gray-600 text-sm">Set product visibility</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-4">
                        <div class="flex items-center">
                            <input id="is_active" wire:model.defer="product.is_active" type="checkbox" class="h-4 w-4 text-black border-gray-300 rounded focus:ring-black">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                        </div>
                        <div class="flex items-center">
                            <input id="is_featured" wire:model.defer="product.is_featured" type="checkbox" class="h-4 w-4 text-black border-gray-300 rounded focus:ring-black">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">Featured</label>
                        </div>
                    </div>
                </div>

                {{-- Shipping Card --}}
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-blue-500 rounded-lg">
                                <i class="fas fa-truck text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Shipping</h3>
                                <p class="text-gray-600 text-sm">Set product dimensions</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-8 space-y-4">
                        <div class="space-y-2">
                            <label for="weight" class="block text-sm font-medium text-gray-700">Weight (kg)</label>
                            <input type="number" step="0.01" min="0" wire:model.defer="product.weight" id="weight" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm">
                        </div>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="space-y-2">
                                <label for="length" class="block text-sm font-medium text-gray-700">Length (cm)</label>
                                <input type="number" step="0.01" min="0" wire:model.defer="product.length" id="length" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm">
                            </div>
                            <div class="space-y-2">
                                <label for="width" class="block text-sm font-medium text-gray-700">Width (cm)</label>
                                <input type="number" step="0.01" min="0" wire:model.defer="product.width" id="width" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm">
                            </div>
                            <div class="space-y-2">
                                <label for="height" class="block text-sm font-medium text-gray-700">Height (cm)</label>
                                <input type="number" step="0.01" min="0" wire:model.defer="product.height" id="height" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-black focus:border-black sm:text-sm">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
