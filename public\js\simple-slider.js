// Simple auto-scroll slider for .slider-image and .slider-dot
// Works with the PHP-generated markup in welcome-bw.blade.php

document.addEventListener('DOMContentLoaded', function () {
    const slides = document.querySelectorAll('.slider-image');
    const dots = document.querySelectorAll('.slider-dot');
    if (!slides.length) return;

    let current = 0;
    let interval = null;
    const duration = 4000;

    function showSlide(idx) {
        slides.forEach((slide, i) => {
            slide.classList.toggle('opacity-100', i === idx);
            slide.classList.toggle('opacity-0', i !== idx);
            slide.style.zIndex = i === idx ? 2 : 1;
        });
        dots.forEach((dot, i) => {
            dot.classList.toggle('bg-white', i === idx);
            dot.classList.toggle('bg-white/50', i !== idx);
        });
        current = idx;
    }

    function nextSlide() {
        showSlide((current + 1) % slides.length);
    }

    function goToSlide(idx) {
        showSlide(idx);
        restart();
    }

    function start() {
        interval = setInterval(nextSlide, duration);
    }
    function stop() {
        clearInterval(interval);
    }
    function restart() {
        stop();
        start();
    }

    dots.forEach((dot, idx) => {
        dot.addEventListener('click', () => goToSlide(idx));
    });

    showSlide(0);
    start();

    // Pause on hover (desktop)
    slides[0].parentNode.addEventListener('mouseenter', stop);
    slides[0].parentNode.addEventListener('mouseleave', start);
});
