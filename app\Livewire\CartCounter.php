<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;

class CartCounter extends Component
{
    public $count = 0;

    public function mount()
    {
        \Log::info('CartCounter Component Mounted', [
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        // CRITICAL FIX: Ensure cart is always stored as array
        $this->fixCartDataFormat();
        $this->updateCount();
    }

    /**
     * Fix cart data format to ensure it's always an array
     */
    private function fixCartDataFormat()
    {
        $cart = session()->get('cart', []);

        // If cart is a Collection, convert it to array
        if ($cart instanceof \Illuminate\Support\Collection) {
            \Log::warning('CartCounter: Converting Collection cart to array', [
                'cart_type' => get_class($cart),
                'cart_count' => $cart->count(),
                'session_id' => session()->getId()
            ]);
            session()->put('cart', $cart->toArray());
        }
    }

    #[On('cartUpdated')]
    #[On('cart-updated-total')]
    public function updateCount($newCount = null)
    {
        $previousCount = $this->count;

        \Log::info('CartCounter Update Started', [
            'previous_count' => $previousCount,
            'new_count_provided' => $newCount,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        if ($newCount !== null) {
            $this->count = $newCount;
            \Log::info('CartCounter Updated with Provided Count', [
                'previous_count' => $previousCount,
                'new_count' => $this->count
            ]);
        } else {
            $cart = session()->get('cart', []);
            $this->count = count($cart);
            \Log::info('CartCounter Updated from Session', [
                'previous_count' => $previousCount,
                'session_cart_items' => count($cart),
                'new_count' => $this->count,
                'cart_keys' => safe_array_keys($cart),
                'cart_type' => gettype($cart),
                'cart_class' => is_object($cart) ? get_class($cart) : 'not_object',
                'cart_data' => $cart,
                'session_id' => session()->getId()
            ]);
        }

        \Log::info('CartCounter Update Completed', [
            'final_count' => $this->count,
            'count_changed' => $previousCount !== $this->count
        ]);
    }

    public function render()
    {
        return view('livewire.cart-counter');
    }
}
