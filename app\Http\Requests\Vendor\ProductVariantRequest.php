<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;

class ProductVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Authorization is handled by policies in the controller
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'color_id' => 'nullable|exists:colors,id',
            'size_id' => 'nullable|exists:sizes,id',
            'sku' => 'required|string|max:255|unique:product_variants,sku',
            'price_adjustment' => 'nullable|numeric',
            'stock_quantity' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ];

        // For update requests, adjust unique validation rules
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $variant = $this->route('variant');
            if ($variant) {
                $rules['sku'] = 'required|string|max:255|unique:product_variants,sku,' . $variant->id;
            }
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'color_id.exists' => 'The selected color is invalid.',
            'size_id.exists' => 'The selected size is invalid.',
            'sku.required' => 'SKU is required.',
            'sku.unique' => 'This SKU is already taken.',
            'stock_quantity.required' => 'Stock quantity is required.',
            'stock_quantity.integer' => 'Stock quantity must be a whole number.',
            'stock_quantity.min' => 'Stock quantity must be at least 0.',
            'price_adjustment.numeric' => 'Price adjustment must be a number.',
        ];
    }
}
