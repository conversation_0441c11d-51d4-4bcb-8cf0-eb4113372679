{"app/Console/Commands/CheckVendorBrandData.php": {"App\\Console\\Commands\\CheckVendorBrandData": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Console/Commands/FixVendorBrandLinks.php": {"App\\Console\\Commands\\FixVendorBrandLinks": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Console/Commands/ProcessPendingBalances.php": {"App\\Console\\Commands\\ProcessPendingBalances": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Console/Commands/SyncProductBrands.php": {"App\\Console\\Commands\\SyncProductBrands": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Console/Commands/SyncVendorBrands.php": {"App\\Console\\Commands\\SyncVendorBrands": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Console/Commands/TestAdminAccess.php": {"App\\Console\\Commands\\TestAdminAccess": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": []}}, "properties": ["$signature", "$description"]}}, "app/Facades/Cart.php": [], "app/helpers.php": [], "app/Helpers/array_helpers.php": [], "app/Helpers/ArrayHelper.php": {"App\\Helpers\\ArrayHelper": {"type": "class", "methods": {"safeArrayKeyExists": {"visibility": "public", "static": true, "params": ["$key", "$array"]}, "safeIsset": {"visibility": "public", "static": true, "params": ["$array", "$key"]}, "safeGet": {"visibility": "public", "static": true, "params": ["$array", "$key", "$default"]}, "ensureArray": {"visibility": "public", "static": true, "params": ["$value", "$default"]}}, "properties": []}}, "app/Helpers/Location.php": {"App\\Helpers\\Location": {"type": "class", "methods": {"getStates": {"visibility": "public", "static": true, "params": []}, "getLgas": {"visibility": "public", "static": true, "params": ["$state"]}, "getStateNames": {"visibility": "public", "static": true, "params": []}, "stateExists": {"visibility": "public", "static": true, "params": ["$state"]}, "lgaExists": {"visibility": "public", "static": true, "params": ["$state", "$lga"]}}, "properties": []}}, "app/Http/Controllers/Admin/DashboardController.php": {"App\\Http\\Controllers\\Admin\\DashboardController": {"type": "class", "methods": {"index": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Http/Controllers/Admin/SubscriptionPlanController.php": {"App\\Http\\Controllers\\Admin\\SubscriptionPlanController": {"type": "class", "methods": {"destroy": {"visibility": "public", "static": false, "params": ["$subscriptionPlan"]}}, "properties": []}}, "app/Http/Controllers/Admin/UserController.php": {"App\\Http\\Controllers\\Admin\\UserController": {"type": "class", "methods": {"destroy": {"visibility": "public", "static": false, "params": ["$user"]}}, "properties": []}}, "app/Http/Controllers/Admin/VendorController.php": {"App\\Http\\Controllers\\Admin\\VendorController": {"type": "class", "methods": {"destroy": {"visibility": "public", "static": false, "params": ["$vendor"]}}, "properties": []}}, "app/Http/Controllers/Api/VendorOrdersController.php": {"App\\Http\\Controllers\\Api\\VendorOrdersController": {"type": "class", "methods": {"getOrdersByState": {"visibility": "public", "static": false, "params": ["$request"]}, "isJson": {"visibility": "private", "static": false, "params": ["$string"]}, "normalizeStateName": {"visibility": "private", "static": false, "params": ["$stateName"]}}, "properties": []}}, "app/Http/Controllers/Auth/VerifyEmailController.php": {"App\\Http\\Controllers\\Auth\\VerifyEmailController": {"type": "class", "methods": {"__invoke": {"visibility": "public", "static": false, "params": ["$request"]}}, "properties": []}}, "app/Http/Controllers/CartController.php": {"App\\Http\\Controllers\\CartController": {"type": "class", "methods": {"add": {"visibility": "public", "static": false, "params": ["$product"]}}, "properties": []}}, "app/Http/Controllers/Controller.php": {"App\\Http\\Controllers\\Controller": {"type": "class", "methods": [], "properties": []}}, "app/Http/Controllers/HomeController.php": {"App\\Http\\Controllers\\HomeController": {"type": "class", "methods": {"index": {"visibility": "public", "static": false, "params": []}, "handleContactForm": {"visibility": "public", "static": false, "params": ["$request"]}}, "properties": []}}, "app/Http/Controllers/OrderController.php": {"App\\Http\\Controllers\\OrderController": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$viewedProductsService"]}, "index": {"visibility": "public", "static": false, "params": []}, "show": {"visibility": "public", "static": false, "params": ["$order"]}, "cancel": {"visibility": "public", "static": false, "params": ["$request", "$order"]}, "requestReturn": {"visibility": "public", "static": false, "params": ["$request", "$order"]}}, "properties": ["$viewedProductsService"]}}, "app/Http/Controllers/PaymentController.php": {"App\\Http\\Controllers\\PaymentController": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$paystackService", "$shipBubbleService", "$earningsService"]}, "initialize": {"visibility": "public", "static": false, "params": ["$request"]}, "initializePaystack": {"visibility": "public", "static": false, "params": ["$request"]}, "handlePaystackCallback": {"visibility": "public", "static": false, "params": ["$request"]}, "checkoutSuccess": {"visibility": "public", "static": false, "params": ["$request", "$transaction_id"]}, "handlePaystackWebhook": {"visibility": "public", "static": false, "params": ["$request"]}, "processVendorEarnings": {"visibility": "private", "static": false, "params": ["$checkoutSession", "$orders"]}, "restoreStockForFailedOrder": {"visibility": "private", "static": false, "params": ["$orderIds"]}, "sanitizePaymentData": {"visibility": "private", "static": false, "params": ["$data"]}, "sanitizeLogData": {"visibility": "private", "static": false, "params": ["$data"]}, "testWebhook": {"visibility": "public", "static": false, "params": ["$request"]}}, "properties": ["$paystackService", "$shipBubbleService", "$earningsService"]}}, "app/Http/Controllers/ReviewController.php": {"App\\Http\\Controllers\\ReviewController": {"type": "class", "methods": {"store": {"visibility": "public", "static": false, "params": ["$request", "$product"]}}, "properties": []}}, "app/Http/Controllers/ShippingController.php": {"App\\Http\\Controllers\\ShippingController": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$shipBubbleService"]}, "getRates": {"visibility": "public", "static": false, "params": ["$request"]}, "generateShippingDescription": {"visibility": "private", "static": false, "params": ["$courier"]}, "formatDeliveryTime": {"visibility": "private", "static": false, "params": ["$courier"]}, "getShippingFeatures": {"visibility": "private", "static": false, "params": ["$courier"]}}, "properties": ["$shipBubbleService"]}}, "app/Http/Controllers/Vendor/ProductController.php": {"App\\Http\\Controllers\\Vendor\\ProductController": {"type": "class", "methods": {"index": {"visibility": "public", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "store": {"visibility": "public", "static": false, "params": ["$request"]}, "edit": {"visibility": "public", "static": false, "params": ["$product"]}, "update": {"visibility": "public", "static": false, "params": ["$request", "$product"]}, "destroy": {"visibility": "public", "static": false, "params": ["$product"]}}, "properties": []}}, "app/Http/Controllers/Vendor/SubscriptionController.php": {"App\\Http\\Controllers\\Vendor\\SubscriptionController": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$paystackService"]}, "handleCallback": {"visibility": "public", "static": false, "params": ["$request"]}, "handleWebhook": {"visibility": "public", "static": false, "params": ["$request"]}}, "properties": ["$paystackService"]}}, "app/Http/Controllers/VendorController.php": {"App\\Http\\Controllers\\VendorController": {"type": "class", "methods": {"show": {"visibility": "public", "static": false, "params": ["$slug"]}}, "properties": []}}, "app/Http/Middleware/AdminMiddleware.php": {"App\\Http\\Middleware\\AdminMiddleware": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": ["$request", "$next"]}}, "properties": []}}, "app/Http/Middleware/ApprovedVendorMiddleware.php": {"App\\Http\\Middleware\\ApprovedVendorMiddleware": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": ["$request", "$next"]}}, "properties": []}}, "app/Http/Middleware/CheckVendorSubscription.php": {"App\\Http\\Middleware\\CheckVendorSubscription": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": ["$request", "$next"]}}, "properties": []}}, "app/Http/Middleware/TrackProductViews.php": {"App\\Http\\Middleware\\TrackProductViews": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$viewedProductsService"]}, "handle": {"visibility": "public", "static": false, "params": ["$request", "$next"]}}, "properties": ["$viewedProductsService"]}}, "app/Http/Middleware/VendorMiddleware.php": {"App\\Http\\Middleware\\VendorMiddleware": {"type": "class", "methods": {"handle": {"visibility": "public", "static": false, "params": ["$request", "$next"]}}, "properties": []}}, "app/Livewire/Actions/Logout.php": {"App\\Livewire\\Actions\\Logout": {"type": "class", "methods": {"__invoke": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/AddToCartButton.php": {"App\\Livewire\\AddToCartButton": {"type": "class", "methods": {"addToCart": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$wasAdded"]}}, "app/Livewire/Admin/Brands/Index.php": {"App\\Livewire\\Admin\\Brands\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "protected", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$brand"]}, "save": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$brand"]}, "toggleFeatured": {"visibility": "public", "static": false, "params": ["$brand"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$brand", "$logo", "$showModal", "$name", "$description", "$is_active", "$is_featured"]}}, "app/Livewire/Admin/Categories/Index.php": {"App\\Livewire\\Admin\\Categories\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "protected", "static": false, "params": []}, "updatedName": {"visibility": "public", "static": false, "params": ["$value"]}, "create": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$category"]}, "save": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$category"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$category", "$showModal", "$name", "$slug", "$parent_id"]}}, "app/Livewire/Admin/Colors/Index.php": {"App\\Livewire\\Admin\\Colors\\Index": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "store": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$colorId"]}, "update": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$colorId"]}, "toggleStatus": {"visibility": "public", "static": false, "params": ["$colorId"]}, "resetForm": {"visibility": "private", "static": false, "params": []}, "closeModal": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$showCreateModal", "$showEditModal", "$editingColor", "$name", "$hex_code", "$is_active", "$rules", "$messages"]}}, "app/Livewire/Admin/Commissions/Index.php": {"App\\Livewire\\Admin\\Commissions\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updateStatus": {"visibility": "public", "static": false, "params": ["$commissionId", "$status"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Admin/Orders/Index.php": {"App\\Livewire\\Admin\\Orders\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "updatingStatus": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}, "sanitizeSearchInput": {"visibility": "private", "static": false, "params": ["$input"]}, "sanitizeStatusInput": {"visibility": "private", "static": false, "params": ["$status"]}}, "properties": ["$search", "$status"]}}, "app/Livewire/Admin/Orders/Show.php": {"App\\Livewire\\Admin\\Orders\\Show": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$order"]}, "updateStatus": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$order", "$status"]}}, "app/Livewire/Admin/Payments/Index.php": {"App\\Livewire\\Admin\\Payments\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "getStatusClass": {"visibility": "public", "static": false, "params": ["$status"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Admin/Products/BestSellerManager.php": {"App\\Livewire\\Admin\\Products\\BestSellerManager": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "toggleBestSeller": {"visibility": "public", "static": false, "params": ["$product"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$queryString"]}}, "app/Livewire/Admin/Products/CreateProduct.php": {"App\\Livewire\\Admin\\Products\\CreateProduct": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "public", "static": false, "params": []}, "updatedProductName": {"visibility": "public", "static": false, "params": ["$value"]}, "updatedVendorId": {"visibility": "public", "static": false, "params": ["$vendorId"]}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$slug", "$vendor_id", "$is_featured", "$brand_id", "$sku", "$vendors", "$categories", "$brands"]}}, "app/Livewire/Admin/Products/EditProduct.php": {"App\\Livewire\\Admin\\Products\\EditProduct": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "ensureProductPropertiesAccessible": {"visibility": "private", "static": false, "params": []}, "hydrate": {"visibility": "public", "static": false, "params": []}, "getProductRules": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "deleteMedia": {"visibility": "public", "static": false, "params": ["$mediaId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendors", "$categories", "$brands"]}}, "app/Livewire/Admin/Products/Index.php": {"App\\Livewire\\Admin\\Products\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$product"]}, "sortBy": {"visibility": "public", "static": false, "params": ["$field"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$sortBy", "$sortDirection"]}}, "app/Livewire/Admin/Products/ProductForm.php": {"App\\Livewire\\Admin\\Products\\ProductForm": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "rules": {"visibility": "protected", "static": false, "params": []}, "updatedName": {"visibility": "public", "static": false, "params": ["$value"]}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$name", "$slug", "$vendor_id", "$category_id", "$description", "$price", "$discount_price", "$image_url", "$stock", "$is_active", "$vendors", "$categories"]}}, "app/Livewire/Admin/Products/Show.php": {"App\\Livewire\\Admin\\Products\\Show": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "toggleFeatured": {"visibility": "public", "static": false, "params": []}, "toggleBestSeller": {"visibility": "public", "static": false, "params": []}, "toggleActive": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product"]}}, "app/Livewire/Admin/Settings/Index.php": {"App\\Livewire\\Admin\\Settings\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$site_name", "$contact_email", "$support_phone"]}}, "app/Livewire/Admin/Sizes/Index.php": {"App\\Livewire\\Admin\\Sizes\\Index": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "store": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$sizeId"]}, "update": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$sizeId"]}, "toggleStatus": {"visibility": "public", "static": false, "params": ["$sizeId"]}, "resetForm": {"visibility": "private", "static": false, "params": []}, "closeModal": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$showCreateModal", "$showEditModal", "$editingSize", "$name", "$code", "$sort_order", "$is_active", "$rules"]}}, "app/Livewire/Admin/SubscriptionPlans/Index.php": {"App\\Livewire\\Admin\\SubscriptionPlans\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": ["$paystackService"]}, "rules": {"visibility": "protected", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$plan"]}, "save": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$plan"]}, "closeModal": {"visibility": "public", "static": false, "params": []}, "resetForm": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$editing", "$showModal", "$name", "$description", "$price", "$interval", "$duration_days", "$features", "$is_active", "$product_limit", "$commission_rate", "$paystackService"]}}, "app/Livewire/Admin/SubscriptionPlans/SubscriptionPlanForm.php": {"App\\Livewire\\Admin\\SubscriptionPlans\\SubscriptionPlanForm": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$subscriptionPlan"]}, "addFeature": {"visibility": "public", "static": false, "params": []}, "removeFeature": {"visibility": "public", "static": false, "params": ["$index"]}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$subscriptionPlan", "$name", "$description", "$price", "$interval", "$product_limit", "$order_limit", "$storage_limit", "$status", "$features", "$newFeature", "$rules", "$messages"]}}, "app/Livewire/Admin/Subscriptions/Index.php": {"App\\Livewire\\Admin\\Subscriptions\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$subscription"]}, "save": {"visibility": "public", "static": false, "params": []}, "saveSubscription": {"visibility": "public", "static": false, "params": []}, "cancel": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$editing", "$plans", "$plan_id", "$end_date", "$status", "$showCreateModal", "$new_plan_id", "$new_end_date", "$new_status"]}}, "app/Livewire/Admin/Users/<USER>": {"App\\Livewire\\Admin\\Users\\UserForm": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$user"]}, "rules": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$user", "$name", "$email", "$password", "$password_confirmation", "$selectedRole", "$allRoles"]}}, "app/Livewire/Admin/Vendors/Index.php": {"App\\Livewire\\Admin\\Vendors\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "approve": {"visibility": "public", "static": false, "params": ["$vendor"]}, "reject": {"visibility": "public", "static": false, "params": ["$vendor"]}, "toggleFeatured": {"visibility": "public", "static": false, "params": ["$vendor"]}, "sortBy": {"visibility": "public", "static": false, "params": ["$field"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$sortBy", "$sortDirection"]}}, "app/Livewire/Admin/Vendors/Show.php": {"App\\Livewire\\Admin\\Vendors\\Show": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$vendor"]}, "toggleStatus": {"visibility": "public", "static": false, "params": []}, "toggleFeatured": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor"]}}, "app/Livewire/Admin/Vendors/VendorForm.php": {"App\\Livewire\\Admin\\Vendors\\VendorForm": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$vendor"]}, "rules": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor", "$user", "$name", "$email", "$password", "$password_confirmation", "$shop_name", "$business_name", "$business_description", "$phone", "$business_address", "$city", "$state", "$country", "$is_featured", "$is_approved", "$banner", "$about", "$facebook_url", "$twitter_url", "$instagram_url"]}}, "app/Livewire/Admin/Withdrawals/Index.php": {"App\\Livewire\\Admin\\Withdrawals\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": ["$earningsService", "$paystackService"]}, "updateStatus": {"visibility": "public", "static": false, "params": ["$withdrawalId", "$status"]}, "resolveAccountName": {"visibility": "public", "static": false, "params": ["$withdrawalId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$earningsService", "$paystackService", "$resolvedAccountNames"]}}, "app/Livewire/Auth/ConfirmPassword.php": {"App\\Livewire\\Auth\\ConfirmPassword": {"type": "class", "methods": {"confirmPassword": {"visibility": "public", "static": false, "params": []}}, "properties": ["$password"]}}, "app/Livewire/Auth/ForgotPassword.php": {"App\\Livewire\\Auth\\ForgotPassword": {"type": "class", "methods": {"sendPasswordResetLink": {"visibility": "public", "static": false, "params": []}}, "properties": ["$email"]}}, "app/Livewire/Auth/Login.php": {"App\\Livewire\\Auth\\Login": {"type": "class", "methods": {"login": {"visibility": "public", "static": false, "params": []}, "ensureIsNotRateLimited": {"visibility": "protected", "static": false, "params": []}, "throttleKey": {"visibility": "protected", "static": false, "params": []}}, "properties": ["$email", "$password", "$remember"]}}, "app/Livewire/Auth/Register.php": {"App\\Livewire\\Auth\\Register": {"type": "class", "methods": {"register": {"visibility": "public", "static": false, "params": []}}, "properties": ["$name", "$email", "$password", "$password_confirmation", "$registering"]}}, "app/Livewire/Auth/ResetPassword.php": {"App\\Livewire\\Auth\\ResetPassword": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$token"]}, "resetPassword": {"visibility": "public", "static": false, "params": []}}, "properties": ["$token", "$email", "$password", "$password_confirmation"]}}, "app/Livewire/Auth/Vendor/Register.php": {"App\\Livewire\\Auth\\Vendor\\Register": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$request"]}, "register": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$name", "$email", "$password", "$password_confirmation", "$planId", "$plan"]}}, "app/Livewire/Auth/VerifyEmail.php": {"App\\Livewire\\Auth\\VerifyEmail": {"type": "class", "methods": {"sendVerification": {"visibility": "public", "static": false, "params": []}, "logout": {"visibility": "public", "static": false, "params": ["$logout"]}}, "properties": []}}, "app/Livewire/Cart/Index.php": {"App\\Livewire\\Cart\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updateCart": {"visibility": "public", "static": false, "params": []}, "updateQuantity": {"visibility": "public", "static": false, "params": ["$cartKey", "$quantity"]}, "incrementQuantity": {"visibility": "public", "static": false, "params": ["$cartKey"]}, "decrementQuantity": {"visibility": "public", "static": false, "params": ["$cartKey"]}, "removeItem": {"visibility": "public", "static": false, "params": ["$cartKey"]}, "clearCart": {"visibility": "public", "static": false, "params": []}, "calculateTotals": {"visibility": "private", "static": false, "params": []}, "loadRecommendedProducts": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$cartItems", "$subtotal", "$total", "$recommendedProducts", "$listeners"]}}, "app/Livewire/CartCounter.php": {"App\\Livewire\\CartCounter": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updateCount": {"visibility": "public", "static": false, "params": ["$newCount"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$count"]}}, "app/Livewire/Checkout/Index.php": {"App\\Livewire\\Checkout\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatedShippingAddressState": {"visibility": "public", "static": false, "params": ["$state"]}, "updatedShippingAddressLga": {"visibility": "public", "static": false, "params": ["$lga"]}, "getShippingRates": {"visibility": "public", "static": false, "params": []}, "selectShippingRate": {"visibility": "public", "static": false, "params": ["$rateKey"]}, "calculateSubtotal": {"visibility": "private", "static": false, "params": []}, "calculateTotal": {"visibility": "private", "static": false, "params": []}, "validateCheckoutProcess": {"visibility": "private", "static": false, "params": []}, "updated": {"visibility": "public", "static": false, "params": ["$propertyName"]}, "resetShipping": {"visibility": "private", "static": false, "params": []}, "isAddressComplete": {"visibility": "public", "static": false, "params": []}, "getMissingAddressFields": {"visibility": "public", "static": false, "params": []}, "refreshCart": {"visibility": "public", "static": false, "params": []}, "proceedToPayment": {"visibility": "public", "static": false, "params": []}, "getShippingErrorMessage": {"visibility": "private", "static": false, "params": ["$e"]}, "offerShippingFallback": {"visibility": "private", "static": false, "params": []}, "generateShippingDescription": {"visibility": "private", "static": false, "params": ["$courier"]}, "formatDeliveryTime": {"visibility": "private", "static": false, "params": ["$courier"]}, "getShippingFeatures": {"visibility": "private", "static": false, "params": ["$courier"]}, "validateProductAvailability": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$cartItems", "$subtotal", "$shippingAddress", "$states", "$lgas", "$shippingRates", "$selectedShippingRate", "$shippingCost", "$total", "$loadingShippingRates", "$processingPayment", "$loadingLgas", "$rules", "$messages"]}}, "app/Livewire/Checkout/Success.php": {"App\\Livewire\\Checkout\\Success": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$transaction_id"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$orders", "$transaction_id"]}}, "app/Livewire/ContactForm.php": {"App\\Livewire\\ContactForm": {"type": "class", "methods": {"rules": {"visibility": "protected", "static": false, "params": []}, "updated": {"visibility": "public", "static": false, "params": ["$propertyName"]}, "submit": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$name", "$email", "$subject", "$message", "$success", "$error"]}}, "app/Livewire/Dashboard/Index.php": {"App\\Livewire\\Dashboard\\Index": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Dashboard/MapWidget.php": {"App\\Livewire\\Dashboard\\MapWidget": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "loadOrderData": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$orderData"]}}, "app/Livewire/DebugCheckout.php": {"App\\Livewire\\DebugCheckout": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "testArrayOperations": {"visibility": "public", "static": false, "params": []}, "testCheckoutProcess": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$testData", "$cartItems", "$debugInfo"]}}, "app/Livewire/Orders/Index.php": {"App\\Livewire\\Orders\\Index": {"type": "class", "methods": {"updatingSearch": {"visibility": "public", "static": false, "params": []}, "updatingStatus": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$status", "$queryString"]}}, "app/Livewire/Pricing/Index.php": {"App\\Livewire\\Pricing\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "selectPlan": {"visibility": "public", "static": false, "params": ["$planId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$plans"]}}, "app/Livewire/Product/CategoryProducts.php": {"App\\Livewire\\Product\\CategoryProducts": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$category"]}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "updatingSortBy": {"visibility": "public", "static": false, "params": []}, "updatingPriceRange": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$category", "$search", "$sortBy", "$priceRange", "$queryString"]}}, "app/Livewire/Product/Gallery.php": {"App\\Livewire\\Product\\Gallery": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "selectImage": {"visibility": "public", "static": false, "params": ["$imageData"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$images", "$selectedImage"]}}, "app/Livewire/Product/Index.php": {"App\\Livewire\\Product\\Index": {"type": "class", "methods": {"updating": {"visibility": "public", "static": false, "params": ["$key"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$category", "$brand", "$min_price", "$max_price", "$sort_by"]}}, "app/Livewire/Product/Options.php": {"App\\Livewire\\Product\\Options": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "variantSelected": {"visibility": "public", "static": false, "params": ["$variantId"]}, "selectColor": {"visibility": "public", "static": false, "params": ["$colorId"]}, "selectSize": {"visibility": "public", "static": false, "params": ["$sizeId"]}, "updateSelectedVariant": {"visibility": "public", "static": false, "params": []}, "getSelectedVariantProperty": {"visibility": "public", "static": false, "params": []}, "checkWishlistStatus": {"visibility": "public", "static": false, "params": []}, "toggleWishlist": {"visibility": "public", "static": false, "params": []}, "addToCart": {"visibility": "public", "static": false, "params": []}, "incrementQuantity": {"visibility": "public", "static": false, "params": []}, "decrementQuantity": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$variants", "$selectedVariantId", "$selectedVariant", "$selectedColorId", "$selectedSizeId", "$quantity", "$inWishlist", "$listeners"]}}, "app/Livewire/Product/Search.php": {"App\\Livewire\\Product\\Search": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}, "sanitizeSearchQuery": {"visibility": "private", "static": false, "params": ["$query"]}}, "properties": ["$query"]}}, "app/Livewire/Product/Show.php": {"App\\Livewire\\Product\\Show": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product", "$viewedProductsService"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$relatedProducts", "$recentlyViewedProducts"]}}, "app/Livewire/Product/Tabs.php": {"App\\Livewire\\Product\\Tabs": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "selectTab": {"visibility": "public", "static": false, "params": ["$tab"]}, "submitReview": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$activeTab", "$rating", "$comment", "$rules"]}}, "app/Livewire/ProductCard.php": {"App\\Livewire\\ProductCard": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "addToCart": {"visibility": "public", "static": false, "params": []}, "toggleWishlist": {"visibility": "public", "static": false, "params": []}, "addToWishlist": {"visibility": "public", "static": false, "params": []}, "checkWishlist": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$loadingCart", "$loadingWishlist", "$inWishlist"]}}, "app/Livewire/Products/Index.php": {"App\\Livewire\\Products\\Index": {"type": "class", "methods": {"updating": {"visibility": "public", "static": false, "params": ["$key"]}, "render": {"visibility": "public", "static": false, "params": []}, "clearFilters": {"visibility": "public", "static": false, "params": []}}, "properties": ["$paginationTheme", "$search", "$category", "$min_price", "$max_price", "$sort_by"]}}, "app/Livewire/SafeComponent.php": {"App\\Livewire\\SafeComponent": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$args"]}, "render": {"visibility": "public", "static": false, "params": []}, "safeArrayGet": {"visibility": "protected", "static": false, "params": ["$array", "$key", "$default"]}, "safeArrayKeyExists": {"visibility": "protected", "static": false, "params": ["$key", "$array"]}}, "properties": []}}, "app/Livewire/Search.php": {"App\\Livewire\\Search": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatedQuery": {"visibility": "public", "static": false, "params": []}, "performSearch": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$query", "$results"]}}, "app/Livewire/Settings/Appearance.php": {"App\\Livewire\\Settings\\Appearance": {"type": "class", "methods": [], "properties": []}}, "app/Livewire/Settings/DeleteUserForm.php": {"App\\Livewire\\Settings\\DeleteUserForm": {"type": "class", "methods": {"deleteUser": {"visibility": "public", "static": false, "params": ["$logout"]}}, "properties": ["$password"]}}, "app/Livewire/Settings/Password.php": {"App\\Livewire\\Settings\\Password": {"type": "class", "methods": {"updatePassword": {"visibility": "public", "static": false, "params": []}}, "properties": ["$current_password", "$password", "$password_confirmation"]}}, "app/Livewire/Settings/Profile.php": {"App\\Livewire\\Settings\\Profile": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updateProfileInformation": {"visibility": "public", "static": false, "params": []}, "resendVerificationNotification": {"visibility": "public", "static": false, "params": []}}, "properties": ["$name", "$email"]}}, "app/Livewire/Shared/ProductForm.php": {"App\\Livewire\\Shared\\ProductForm": {"type": "class", "methods": {"initializeProductForm": {"visibility": "protected", "static": false, "params": []}, "getProductRules": {"visibility": "protected", "static": false, "params": []}, "getMessages": {"visibility": "protected", "static": false, "params": []}, "saveProduct": {"visibility": "protected", "static": false, "params": []}, "cleanupMedia": {"visibility": "protected", "static": false, "params": []}, "uploadVariantImage": {"visibility": "public", "static": false, "params": ["$index"]}, "removeVariantImage": {"visibility": "public", "static": false, "params": ["$index"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$image", "$gallery", "$variants", "$specifications", "$mediaToDelete"]}}, "app/Livewire/Vendor/Dashboard.php": {"App\\Livewire\\Vendor\\Dashboard": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}, "getAnalyticsData": {"visibility": "public", "static": false, "params": []}, "getMapData": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Vendor/Dashboard/FinancialDashboard.php": {"App\\Livewire\\Vendor\\Dashboard\\FinancialDashboard": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "balance": {"visibility": "public", "static": false, "params": []}, "totalEarnings": {"visibility": "public", "static": false, "params": []}, "totalWithdrawals": {"visibility": "public", "static": false, "params": []}, "recentTransactions": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor"]}}, "app/Livewire/Vendor/Dashboard/MapWidget.php": {"App\\Livewire\\Vendor\\Dashboard\\MapWidget": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "loadOrderData": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$orderData"]}}, "app/Livewire/Vendor/Earnings/Index.php": {"App\\Livewire\\Vendor\\Earnings\\Index": {"type": "class", "methods": {"boot": {"visibility": "public", "static": false, "params": ["$paystackService", "$shipBubbleService"]}, "mount": {"visibility": "public", "static": false, "params": []}, "loadEarningsData": {"visibility": "public", "static": false, "params": []}, "loadBanks": {"visibility": "public", "static": false, "params": []}, "withdraw": {"visibility": "public", "static": false, "params": []}, "updatedAccountNumber": {"visibility": "public", "static": false, "params": []}, "updatedBankCode": {"visibility": "public", "static": false, "params": []}, "verifyAccount": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$balance", "$withdrawableBalance", "$pendingBalance", "$totalEarnings", "$totalWithdrawn", "$banks", "$amount", "$bank_code", "$account_name", "$account_number", "$verifying_account", "$account_verified", "$paystackService", "$shipBubbleService"]}}, "app/Livewire/Vendor/Onboarding/Index.php": {"App\\Livewire\\Vendor\\Onboarding\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "saveBusinessInfo": {"visibility": "public", "static": false, "params": []}, "saveDocuments": {"visibility": "public", "static": false, "params": []}, "savePayoutInfo": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor", "$step", "$business_name", "$phone", "$business_address", "$business_description", "$id_document", "$business_document", "$bank_name", "$bank_account_name", "$bank_account_number"]}}, "app/Livewire/Vendor/Orders/Index.php": {"App\\Livewire\\Vendor\\Orders\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatingSearch": {"visibility": "public", "static": false, "params": []}, "updatingStatus": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$status", "$search", "$queryString"]}}, "app/Livewire/Vendor/Orders/Show.php": {"App\\Livewire\\Vendor\\Orders\\Show": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$order"]}, "calculateTotals": {"visibility": "public", "static": false, "params": []}, "updateStatus": {"visibility": "public", "static": false, "params": []}, "checkShipBubbleDeliveryStatus": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$order", "$vendorItems", "$subtotal", "$commission", "$netAmount", "$newStatus"]}}, "app/Livewire/Vendor/Pending/Index.php": {"App\\Livewire\\Vendor\\Pending\\Index": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Vendor/Products/CreateProduct.php": {"App\\Livewire\\Vendor\\Products\\CreateProduct": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "protected", "static": false, "params": []}, "getProductRules": {"visibility": "protected", "static": false, "params": []}, "messages": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$colors", "$sizes", "$vendor", "$vendorBrand", "$categories", "$brands", "$isLoading"]}}, "app/Livewire/Vendor/Products/EditProduct.php": {"App\\Livewire\\Vendor\\Products\\EditProduct": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "ensureProductPropertiesAccessible": {"visibility": "private", "static": false, "params": []}, "hydrate": {"visibility": "public", "static": false, "params": []}, "rules": {"visibility": "protected", "static": false, "params": []}, "getProductRules": {"visibility": "protected", "static": false, "params": []}, "messages": {"visibility": "protected", "static": false, "params": []}, "getCustomMessages": {"visibility": "protected", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$colors", "$sizes", "$vendor", "$vendorBrand", "$categories", "$brands", "$isLoading"]}}, "app/Livewire/Vendor/Products/Index.php": {"App\\Livewire\\Vendor\\Products\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updatedSearch": {"visibility": "public", "static": false, "params": []}, "updatedStatus": {"visibility": "public", "static": false, "params": []}, "updatedCategory": {"visibility": "public", "static": false, "params": []}, "updatedSelectAll": {"visibility": "public", "static": false, "params": []}, "deleteProduct": {"visibility": "public", "static": false, "params": ["$productId"]}, "toggleProductStatus": {"visibility": "public", "static": false, "params": ["$productId"]}, "bulkDelete": {"visibility": "public", "static": false, "params": []}, "bulkToggleStatus": {"visibility": "public", "static": false, "params": ["$status"]}, "getProducts": {"visibility": "private", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$search", "$status", "$category", "$sortBy", "$sortDirection", "$selectedProducts", "$selectAll", "$queryString"]}}, "app/Livewire/Vendor/Products/ProductForm.php": {"App\\Livewire\\Vendor\\Products\\ProductForm": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "rules": {"visibility": "protected", "static": false, "params": []}, "addVariant": {"visibility": "public", "static": false, "params": []}, "removeVariant": {"visibility": "public", "static": false, "params": ["$index"]}, "addSpecification": {"visibility": "public", "static": false, "params": []}, "removeSpecification": {"visibility": "public", "static": false, "params": ["$index"]}, "save": {"visibility": "public", "static": false, "params": []}, "deleteMedia": {"visibility": "public", "static": false, "params": ["$mediaId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$image", "$gallery", "$variants", "$specifications", "$mediaToDelete"]}}, "app/Livewire/Vendor/Products/Variants.php": {"App\\Livewire\\Vendor\\Products\\Variants": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$product"]}, "render": {"visibility": "public", "static": false, "params": []}, "create": {"visibility": "public", "static": false, "params": []}, "store": {"visibility": "public", "static": false, "params": []}, "edit": {"visibility": "public", "static": false, "params": ["$variantId"]}, "update": {"visibility": "public", "static": false, "params": []}, "delete": {"visibility": "public", "static": false, "params": ["$variantId"]}, "toggleStatus": {"visibility": "public", "static": false, "params": ["$variantId"]}, "updatedColorId": {"visibility": "public", "static": false, "params": []}, "updatedSizeId": {"visibility": "public", "static": false, "params": []}, "generateSku": {"visibility": "private", "static": false, "params": []}, "resetForm": {"visibility": "private", "static": false, "params": []}, "closeModal": {"visibility": "public", "static": false, "params": []}}, "properties": ["$product", "$showCreateModal", "$showEditModal", "$editingVariant", "$color_id", "$size_id", "$sku", "$price_adjustment", "$stock_quantity", "$is_active", "$rules"]}}, "app/Livewire/Vendor/Profile/Index.php": {"App\\Livewire\\Vendor\\Profile\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor", "$user", "$name", "$email", "$business_name", "$business_address", "$business_description", "$phone", "$logoUrl", "$logo", "$current_password", "$password", "$password_confirmation"]}}, "app/Livewire/Vendor/Reviews/Index.php": {"App\\Livewire\\Vendor\\Reviews\\Index": {"type": "class", "methods": {"render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Livewire/Vendor/Settings/Index.php": {"App\\Livewire\\Vendor\\Settings\\Index": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "updateSettings": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor", "$shop_name", "$business_name", "$business_address", "$city", "$state", "$country", "$phone", "$logo"]}}, "app/Livewire/Vendor/Settings/Profile.php": {"App\\Livewire\\Vendor\\Settings\\Profile": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": []}, "save": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor", "$banner", "$about", "$facebook_url", "$twitter_url", "$instagram_url", "$rules"]}}, "app/Livewire/Vendor/Shipping/Index.php": {"App\\Livewire\\Vendor\\Shipping\\Index": {"type": "class", "methods": {"boot": {"visibility": "public", "static": false, "params": ["$shipBubbleService"]}, "mount": {"visibility": "public", "static": false, "params": []}, "loadRecentShipments": {"visibility": "public", "static": false, "params": []}, "refreshTrackingStatus": {"visibility": "public", "static": false, "params": ["$orderId"]}, "markAsShipped": {"visibility": "public", "static": false, "params": ["$orderId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$recentShipments", "$defaultPackages", "$defaultAddress", "$shipBubbleService"]}}, "app/Livewire/Vendor/Storefront.php": {"App\\Livewire\\Vendor\\Storefront": {"type": "class", "methods": {"mount": {"visibility": "public", "static": false, "params": ["$vendor"]}, "products": {"visibility": "public", "static": false, "params": []}, "categories": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$vendor"]}}, "app/Livewire/Vendor/Subscription/Index.php": {"App\\Livewire\\Vendor\\Subscription\\Index": {"type": "class", "methods": {"boot": {"visibility": "public", "static": false, "params": ["$paystackService", "$subscriptionService"]}, "mount": {"visibility": "public", "static": false, "params": []}, "subscribeToStandard": {"visibility": "public", "static": false, "params": []}, "cancel": {"visibility": "public", "static": false, "params": []}, "subscribeToPlan": {"visibility": "public", "static": false, "params": ["$planId"]}, "isCurrentPlan": {"visibility": "public", "static": false, "params": ["$planId"]}, "cancelSubscription": {"visibility": "public", "static": false, "params": []}, "upgradePlan": {"visibility": "public", "static": false, "params": ["$planId"]}, "reactivateSubscription": {"visibility": "public", "static": false, "params": []}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": ["$subscription", "$subscriptionStatus", "$standardPlan", "$allPlans", "$paystackService", "$subscriptionService"]}}, "app/Livewire/Wishlist/Index.php": {"App\\Livewire\\Wishlist\\Index": {"type": "class", "methods": {"addToWishlist": {"visibility": "public", "static": false, "params": ["$productId"]}, "toggleWishlist": {"visibility": "public", "static": false, "params": ["$productId"]}, "clearWishlist": {"visibility": "public", "static": false, "params": []}, "removeFromWishlist": {"visibility": "public", "static": false, "params": ["$productId"]}, "render": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Mail/ContactFormMail.php": {"App\\Mail\\ContactFormMail": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$name", "$email", "$subject", "$message"]}, "envelope": {"visibility": "public", "static": false, "params": []}, "content": {"visibility": "public", "static": false, "params": []}, "attachments": {"visibility": "public", "static": false, "params": []}}, "properties": ["$name", "$email", "$mailSubject", "$messageBody"]}}, "app/Models/Brand.php": {"App\\Models\\Brand": {"type": "class", "methods": {"boot": {"visibility": "public", "static": true, "params": []}, "scopeActive": {"visibility": "public", "static": false, "params": ["$query"]}, "scopeFeatured": {"visibility": "public", "static": false, "params": ["$query"]}, "vendor": {"visibility": "public", "static": false, "params": []}, "products": {"visibility": "public", "static": false, "params": []}, "registerMediaCollections": {"visibility": "public", "static": false, "params": []}, "registerMediaConversions": {"visibility": "public", "static": false, "params": ["$media"]}, "getLogoUrlAttribute": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Category.php": {"App\\Models\\Category": {"type": "class", "methods": {"parent": {"visibility": "public", "static": false, "params": []}, "children": {"visibility": "public", "static": false, "params": []}, "products": {"visibility": "public", "static": false, "params": []}, "getRouteKeyName": {"visibility": "public", "static": false, "params": []}, "getAllProducts": {"visibility": "public", "static": false, "params": []}, "getAllCategoryIds": {"visibility": "private", "static": false, "params": []}, "getImageUrlAttribute": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/CheckoutSession.php": {"App\\Models\\CheckoutSession": {"type": "class", "methods": {"user": {"visibility": "public", "static": false, "params": []}, "isExpired": {"visibility": "public", "static": false, "params": []}, "markAsExpired": {"visibility": "public", "static": false, "params": []}, "markAsProcessing": {"visibility": "public", "static": false, "params": []}, "markAsCompleted": {"visibility": "public", "static": false, "params": []}, "markAsFailed": {"visibility": "public", "static": false, "params": []}, "scopeActive": {"visibility": "public", "static": false, "params": ["$query"]}, "scopeExpired": {"visibility": "public", "static": false, "params": ["$query"]}, "createSession": {"visibility": "public", "static": true, "params": ["$data"]}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Color.php": {"App\\Models\\Color": {"type": "class", "methods": {"productVariants": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable"]}}, "app/Models/Commission.php": {"App\\Models\\Commission": {"type": "class", "methods": {"order": {"visibility": "public", "static": false, "params": []}, "vendor": {"visibility": "public", "static": false, "params": []}, "calculateCommissionAmount": {"visibility": "public", "static": true, "params": ["$orderTotal"]}, "calculateVendorEarnings": {"visibility": "public", "static": true, "params": ["$orderTotal"]}, "getCommissionRate": {"visibility": "public", "static": true, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Order.php": {"App\\Models\\Order": {"type": "class", "methods": {"boot": {"visibility": "protected", "static": true, "params": []}, "user": {"visibility": "public", "static": false, "params": []}, "vendor": {"visibility": "public", "static": false, "params": []}, "items": {"visibility": "public", "static": false, "params": []}, "getStatusClassAttribute": {"visibility": "public", "static": false, "params": []}, "payment": {"visibility": "public", "static": false, "params": []}, "isPending": {"visibility": "public", "static": false, "params": []}, "isProcessing": {"visibility": "public", "static": false, "params": []}, "isCompleted": {"visibility": "public", "static": false, "params": []}, "isCancelled": {"visibility": "public", "static": false, "params": []}, "isPaid": {"visibility": "public", "static": false, "params": []}, "getAddressAttribute": {"visibility": "public", "static": false, "params": []}, "getCityAttribute": {"visibility": "public", "static": false, "params": []}, "getLgaAttribute": {"visibility": "public", "static": false, "params": []}, "getStateAttribute": {"visibility": "public", "static": false, "params": []}, "getPostalCodeAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedShippingAddressAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedBillingAddressAttribute": {"visibility": "public", "static": false, "params": []}, "isJsonString": {"visibility": "private", "static": false, "params": ["$string"]}, "getShippingAddressLineAttribute": {"visibility": "public", "static": false, "params": []}, "getBillingAddressLineAttribute": {"visibility": "public", "static": false, "params": []}, "getStatusColorAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedTotalAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedShippingCostAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedSubtotalAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedDiscountAttribute": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$guarded", "$casts"]}}, "app/Models/OrderItem.php": {"App\\Models\\OrderItem": {"type": "class", "methods": {"boot": {"visibility": "protected", "static": true, "params": []}, "order": {"visibility": "public", "static": false, "params": []}, "product": {"visibility": "public", "static": false, "params": []}, "vendor": {"visibility": "public", "static": false, "params": []}, "getFormattedPriceAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedUnitPriceAttribute": {"visibility": "public", "static": false, "params": []}, "getFormattedSubtotalAttribute": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Payment.php": {"App\\Models\\Payment": {"type": "class", "methods": {"order": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Product.php": {"App\\Models\\Product": {"type": "class", "methods": {"getSlugOptions": {"visibility": "public", "static": false, "params": []}, "vendor": {"visibility": "public", "static": false, "params": []}, "category": {"visibility": "public", "static": false, "params": []}, "brand": {"visibility": "public", "static": false, "params": []}, "orderItems": {"visibility": "public", "static": false, "params": []}, "reviews": {"visibility": "public", "static": false, "params": []}, "wishlists": {"visibility": "public", "static": false, "params": []}, "wishlistedBy": {"visibility": "public", "static": false, "params": []}, "specifications": {"visibility": "public", "static": false, "params": []}, "activeVariants": {"visibility": "public", "static": false, "params": []}, "availableVariants": {"visibility": "public", "static": false, "params": []}, "getCurrentPrice": {"visibility": "public", "static": false, "params": []}, "getDiscountPercentage": {"visibility": "public", "static": false, "params": []}, "getRouteKeyName": {"visibility": "public", "static": false, "params": []}, "isOnSale": {"visibility": "public", "static": false, "params": []}, "averageRating": {"visibility": "public", "static": false, "params": []}, "reviewCount": {"visibility": "public", "static": false, "params": []}, "variants": {"visibility": "public", "static": false, "params": []}, "getHasVariantsAttribute": {"visibility": "public", "static": false, "params": []}, "getAvailableColorsAttribute": {"visibility": "public", "static": false, "params": []}, "getAvailableSizesAttribute": {"visibility": "public", "static": false, "params": []}, "getTotalVariantStockAttribute": {"visibility": "public", "static": false, "params": []}, "getCheapestVariantPriceAttribute": {"visibility": "public", "static": false, "params": []}, "getVariantByColorAndSize": {"visibility": "public", "static": false, "params": ["$colorId", "$sizeId"]}, "registerMediaCollections": {"visibility": "public", "static": false, "params": []}, "registerMediaConversions": {"visibility": "public", "static": false, "params": ["$media"]}, "getImageUrlAttribute": {"visibility": "public", "static": false, "params": []}, "getThumbUrlAttribute": {"visibility": "public", "static": false, "params": []}, "getGalleryImagesAttribute": {"visibility": "public", "static": false, "params": []}, "isWishlisted": {"visibility": "public", "static": false, "params": []}, "setFeatured": {"visibility": "public", "static": false, "params": ["$featured"]}, "setBestSeller": {"visibility": "public", "static": false, "params": ["$bestSeller"]}, "hasSufficientStock": {"visibility": "public", "static": false, "params": ["$requestedQuantity"]}, "getStockLevel": {"visibility": "public", "static": false, "params": []}, "reduceStock": {"visibility": "public", "static": false, "params": ["$quantity"]}, "increaseStock": {"visibility": "public", "static": false, "params": ["$quantity"]}, "reserveStock": {"visibility": "public", "static": false, "params": ["$quantity", "$reservationId"]}}, "properties": ["$fillable", "$guarded", "$casts"]}}, "app/Models/ProductSpecification.php": {"App\\Models\\ProductSpecification": {"type": "class", "methods": {"product": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable"]}}, "app/Models/ProductVariant.php": {"App\\Models\\ProductVariant": {"type": "class", "methods": {"product": {"visibility": "public", "static": false, "params": []}, "color": {"visibility": "public", "static": false, "params": []}, "size": {"visibility": "public", "static": false, "params": []}, "getPriceAttribute": {"visibility": "public", "static": false, "params": []}, "registerMediaCollections": {"visibility": "public", "static": false, "params": []}, "getImageUrlAttribute": {"visibility": "public", "static": false, "params": []}, "getDisplayNameAttribute": {"visibility": "public", "static": false, "params": []}, "getFullNameAttribute": {"visibility": "public", "static": false, "params": []}, "isInStock": {"visibility": "public", "static": false, "params": []}, "isAvailable": {"visibility": "public", "static": false, "params": []}, "getFormattedPriceAttribute": {"visibility": "public", "static": false, "params": []}, "scopeActive": {"visibility": "public", "static": false, "params": ["$query"]}, "scopeInStock": {"visibility": "public", "static": false, "params": ["$query"]}, "scopeAvailable": {"visibility": "public", "static": false, "params": ["$query"]}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Review.php": {"App\\Models\\Review": {"type": "class", "methods": {"user": {"visibility": "public", "static": false, "params": []}, "product": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Role.php": {"App\\Models\\Role": {"type": "class", "methods": [], "properties": ["$fillable"]}}, "app/Models/Size.php": {"App\\Models\\Size": {"type": "class", "methods": {"productVariants": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable"]}}, "app/Models/Subscription.php": {"App\\Models\\Subscription": {"type": "class", "methods": {"user": {"visibility": "public", "static": false, "params": []}, "plan": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/SubscriptionPlan.php": {"App\\Models\\SubscriptionPlan": {"type": "class", "methods": [], "properties": ["$fillable", "$casts"]}}, "app/Models/User.php": {"App\\Models\\User": {"type": "class", "methods": {"role": {"visibility": "public", "static": false, "params": []}, "isAdmin": {"visibility": "public", "static": false, "params": []}, "isVendor": {"visibility": "public", "static": false, "params": []}, "vendor": {"visibility": "public", "static": false, "params": []}, "isApprovedVendor": {"visibility": "public", "static": false, "params": []}, "hasRole": {"visibility": "public", "static": false, "params": ["$roleName"]}, "wishlist": {"visibility": "public", "static": false, "params": []}, "wishlistProducts": {"visibility": "public", "static": false, "params": []}, "orders": {"visibility": "public", "static": false, "params": []}, "reviews": {"visibility": "public", "static": false, "params": []}, "shippingAddress": {"visibility": "public", "static": false, "params": []}, "initials": {"visibility": "public", "static": false, "params": []}, "canAccessAdmin": {"visibility": "public", "static": false, "params": []}, "canAccessVendor": {"visibility": "public", "static": false, "params": []}, "hasElevatedPrivileges": {"visibility": "public", "static": false, "params": []}, "getRoleName": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$guarded", "$hidden", "$casts"]}}, "app/Models/Vendor.php": {"App\\Models\\Vendor": {"type": "class", "methods": {"boot": {"visibility": "protected", "static": true, "params": []}, "user": {"visibility": "public", "static": false, "params": []}, "products": {"visibility": "public", "static": false, "params": []}, "orders": {"visibility": "public", "static": false, "params": []}, "transactions": {"visibility": "public", "static": false, "params": []}, "commissions": {"visibility": "public", "static": false, "params": []}, "subscription": {"visibility": "public", "static": false, "params": []}, "subscriptions": {"visibility": "public", "static": false, "params": []}, "brand": {"visibility": "public", "static": false, "params": []}, "getRouteKeyName": {"visibility": "public", "static": false, "params": []}, "getTotalSalesAttribute": {"visibility": "public", "static": false, "params": []}, "registerMediaCollections": {"visibility": "public", "static": false, "params": []}, "registerMediaConversions": {"visibility": "public", "static": false, "params": ["$media"]}, "getLogoUrlAttribute": {"visibility": "public", "static": false, "params": []}, "getBannerUrlAttribute": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/VendorSubscription.php": {"App\\Models\\VendorSubscription": {"type": "class", "methods": {"vendor": {"visibility": "public", "static": false, "params": []}, "subscriptionPlan": {"visibility": "public", "static": false, "params": []}, "plan": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/VendorTransaction.php": {"App\\Models\\VendorTransaction": {"type": "class", "methods": {"vendor": {"visibility": "public", "static": false, "params": []}, "reference": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Models/Wishlist.php": {"App\\Models\\Wishlist": {"type": "class", "methods": {"user": {"visibility": "public", "static": false, "params": []}, "product": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable"]}}, "app/Models/WishlistItem.php": {"App\\Models\\WishlistItem": {"type": "class", "methods": {"wishlist": {"visibility": "public", "static": false, "params": []}, "product": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable"]}}, "app/Models/Withdrawal.php": {"App\\Models\\Withdrawal": {"type": "class", "methods": {"vendor": {"visibility": "public", "static": false, "params": []}}, "properties": ["$fillable", "$casts"]}}, "app/Notifications/VendorSubscriptionReminder.php": {"App\\Notifications\\VendorSubscriptionReminder": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": ["$subscription"]}, "via": {"visibility": "public", "static": false, "params": ["$notifiable"]}, "toMail": {"visibility": "public", "static": false, "params": ["$notifiable"]}, "toArray": {"visibility": "public", "static": false, "params": ["$notifiable"]}}, "properties": ["$subscription"]}}, "app/Policies/ProductPolicy.php": {"App\\Policies\\ProductPolicy": {"type": "class", "methods": {"before": {"visibility": "public", "static": false, "params": ["$user", "$ability"]}, "viewAny": {"visibility": "public", "static": false, "params": ["$user"]}, "view": {"visibility": "public", "static": false, "params": ["$user", "$product"]}, "create": {"visibility": "public", "static": false, "params": ["$user"]}, "update": {"visibility": "public", "static": false, "params": ["$user", "$product"]}, "delete": {"visibility": "public", "static": false, "params": ["$user", "$product"]}, "restore": {"visibility": "public", "static": false, "params": ["$user", "$product"]}, "forceDelete": {"visibility": "public", "static": false, "params": ["$user", "$product"]}}, "properties": []}}, "app/Providers/AppServiceProvider.php": {"App\\Providers\\AppServiceProvider": {"type": "class", "methods": {"register": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Providers/AuthServiceProvider.php": {"App\\Providers\\AuthServiceProvider": {"type": "class", "methods": {"register": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": []}}, "properties": ["$policies"]}}, "app/Providers/CartServiceProvider.php": {"App\\Providers\\CartServiceProvider": {"type": "class", "methods": {"register": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Providers/ViewedProductsServiceProvider.php": {"App\\Providers\\ViewedProductsServiceProvider": {"type": "class", "methods": {"register": {"visibility": "public", "static": false, "params": []}, "boot": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Services/Cart/CartService.php": {"App\\Services\\Cart\\CartService": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": []}, "add": {"visibility": "public", "static": false, "params": ["$product", "$quantity", "$variant"]}, "getCartKey": {"visibility": "private", "static": false, "params": ["$productId", "$variantId"]}, "update": {"visibility": "public", "static": false, "params": ["$cartKey", "$quantity"]}, "remove": {"visibility": "public", "static": false, "params": ["$cartKey"]}, "updateByIds": {"visibility": "public", "static": false, "params": ["$productId", "$quantity", "$variantId"]}, "removeByIds": {"visibility": "public", "static": false, "params": ["$productId", "$variantId"]}, "clear": {"visibility": "public", "static": false, "params": []}, "content": {"visibility": "public", "static": false, "params": []}, "count": {"visibility": "public", "static": false, "params": []}, "total": {"visibility": "public", "static": false, "params": []}, "subtotal": {"visibility": "public", "static": false, "params": []}, "tax": {"visibility": "public", "static": false, "params": ["$taxRate"]}, "validatePrices": {"visibility": "public", "static": false, "params": []}, "updatePrices": {"visibility": "public", "static": false, "params": []}}, "properties": ["$session", "$cart"]}}, "app/Services/FileUploadService.php": {"App\\Services\\FileUploadService": {"type": "class", "methods": {"uploadImage": {"visibility": "public", "static": false, "params": ["$file", "$directory", "$options"]}, "deleteFile": {"visibility": "public", "static": false, "params": ["$path", "$disk"]}, "getFileUrl": {"visibility": "public", "static": false, "params": ["$path", "$disk", "$default"]}, "processImage": {"visibility": "private", "static": false, "params": ["$file", "$options"]}, "generateUniqueFilename": {"visibility": "private", "static": false, "params": ["$file", "$format"]}, "extractPathFromUrl": {"visibility": "private", "static": false, "params": ["$url", "$disk"]}, "migrateFile": {"visibility": "public", "static": false, "params": ["$path", "$fromDisk", "$toDisk", "$newDirectory"]}, "getFileSize": {"visibility": "public", "static": false, "params": ["$path", "$disk"]}}, "properties": []}}, "app/Services/PaystackService.php": {"App\\Services\\PaystackService": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": []}, "initializeTransaction": {"visibility": "public", "static": false, "params": ["$data"]}, "verifyTransaction": {"visibility": "public", "static": false, "params": ["$reference"]}, "createPlan": {"visibility": "public", "static": false, "params": ["$data"]}, "createSubscription": {"visibility": "public", "static": false, "params": ["$data"]}, "cancelSubscription": {"visibility": "public", "static": false, "params": ["$subscriptionCode"]}, "getSubscription": {"visibility": "public", "static": false, "params": ["$subscriptionCode"]}, "createCustomer": {"visibility": "public", "static": false, "params": ["$data"]}, "updatePlan": {"visibility": "public", "static": false, "params": ["$planCode", "$data"]}, "createTransferRecipient": {"visibility": "public", "static": false, "params": ["$data"]}, "initiateTransfer": {"visibility": "public", "static": false, "params": ["$data"]}, "getBankList": {"visibility": "public", "static": false, "params": []}, "createSubaccount": {"visibility": "public", "static": false, "params": ["$data"]}, "updateSubaccount": {"visibility": "public", "static": false, "params": ["$subaccountCode", "$data"]}, "getSubaccount": {"visibility": "public", "static": false, "params": ["$subaccountCode"]}, "initializeTransactionWithSplit": {"visibility": "public", "static": false, "params": ["$data"]}, "resolveAccountName": {"visibility": "public", "static": false, "params": ["$accountNumber", "$bankCode"]}, "calculateVendorSplits": {"visibility": "public", "static": false, "params": ["$vendorTotals", "$totalAmount"]}, "initializeTransactionWithInternalSplit": {"visibility": "public", "static": false, "params": ["$data"]}, "validateTransactionData": {"visibility": "private", "static": false, "params": ["$data"]}, "sanitizeMetadata": {"visibility": "private", "static": false, "params": ["$metadata"]}, "sanitizeMetadataArray": {"visibility": "private", "static": false, "params": ["$array"]}}, "properties": ["$secretKey", "$baseUrl"]}}, "app/Services/ShipBubbleService.php": {"App\\Services\\ShipBubbleService": {"type": "class", "methods": {"__construct": {"visibility": "public", "static": false, "params": []}, "getRates": {"visibility": "public", "static": false, "params": ["$data"]}, "createShipment": {"visibility": "public", "static": false, "params": ["$data"]}, "validateAddress": {"visibility": "public", "static": false, "params": ["$data"]}, "trackShipment": {"visibility": "public", "static": false, "params": ["$trackingCode"]}, "getShippingRates": {"visibility": "public", "static": false, "params": ["$cartItems", "$shippingAddress", "$vendor"]}, "getOrCreateSenderAddress": {"visibility": "protected", "static": false, "params": ["$vendor"]}, "buildVendorAddress": {"visibility": "protected", "static": false, "params": ["$vendor"]}, "getOrCreateReceiverAddress": {"visibility": "protected", "static": false, "params": ["$shippingAddress"]}, "sendRequest": {"visibility": "protected", "static": false, "params": ["$method", "$endpoint", "$data"]}, "parseErrorMessage": {"visibility": "protected", "static": false, "params": ["$responseData"]}, "formatUserFriendlyErrorMessage": {"visibility": "protected", "static": false, "params": ["$rawMessage"]}, "categorizeError": {"visibility": "protected", "static": false, "params": ["$errorMessage"]}, "sanitizeLogData": {"visibility": "protected", "static": false, "params": ["$data"]}, "formatNigerianPhoneNumber": {"visibility": "protected", "static": false, "params": ["$phone"]}, "isValidNigerianPhoneNumber": {"visibility": "protected", "static": false, "params": ["$phone"]}, "preValidateAddressData": {"visibility": "protected", "static": false, "params": ["$addressData"]}, "isCircuitOpen": {"visibility": "protected", "static": false, "params": []}, "recordFailure": {"visibility": "protected", "static": false, "params": []}, "recordSuccess": {"visibility": "protected", "static": false, "params": []}}, "properties": ["$apiKey", "$baseUrl", "$maxRetries", "$retryDelay", "$circuitBreakerKey", "$failureThreshold", "$recoveryTimeout"]}}, "app/Services/SubscriptionService.php": {"App\\Services\\SubscriptionService": {"type": "class", "methods": {"canReceiveOrders": {"visibility": "public", "static": false, "params": ["$vendor"]}, "getSubscriptionStatus": {"visibility": "public", "static": false, "params": ["$vendor"]}, "ensureStandardPlan": {"visibility": "public", "static": false, "params": []}, "ensureAllPlans": {"visibility": "public", "static": false, "params": []}, "blockVendorOrders": {"visibility": "public", "static": false, "params": ["$vendor"]}, "changePlan": {"visibility": "public", "static": false, "params": ["$vendor", "$newPlan"]}, "cancelSubscription": {"visibility": "public", "static": false, "params": ["$vendor"]}, "handleFailedPayment": {"visibility": "public", "static": false, "params": ["$vendor"]}, "reactivateSubscription": {"visibility": "public", "static": false, "params": ["$vendor", "$plan"]}, "sendSubscriptionReminders": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Services/VendorEarningsService.php": {"App\\Services\\VendorEarningsService": {"type": "class", "methods": {"creditSale": {"visibility": "public", "static": false, "params": ["$vendor", "$order", "$amount", "$description"]}, "debitCommission": {"visibility": "public", "static": false, "params": ["$vendor", "$order", "$saleAmount", "$description"]}, "processWithdrawal": {"visibility": "public", "static": false, "params": ["$vendor", "$amount", "$description", "$withdrawal"]}, "processOrderEarnings": {"visibility": "public", "static": false, "params": ["$order"]}, "createTransaction": {"visibility": "protected", "static": false, "params": ["$vendor", "$type", "$amount", "$description", "$reference"]}, "calculateCommission": {"visibility": "public", "static": false, "params": ["$amount", "$rate"]}, "creditVendor": {"visibility": "public", "static": false, "params": ["$vendor", "$amount", "$description", "$reference"]}, "processPendingBalances": {"visibility": "public", "static": false, "params": []}, "reconcileVendorBalance": {"visibility": "public", "static": false, "params": ["$vendor"]}}, "properties": []}}, "app/Services/ViewedProductsService.php": {"App\\Services\\ViewedProductsService": {"type": "class", "methods": {"addProduct": {"visibility": "public", "static": false, "params": ["$product"]}, "getViewedProductIds": {"visibility": "public", "static": false, "params": []}, "getRecentlyViewedProducts": {"visibility": "public", "static": false, "params": ["$limit"]}, "clearViewedProducts": {"visibility": "public", "static": false, "params": []}}, "properties": []}}, "app/Traits/HandlesProductImages.php": [], "app/Traits/ManagesSpecifications.php": [], "app/Traits/ManagesVariants.php": [], "config/app.php": [], "config/auth.php": [], "config/brandify.php": [], "config/cache.php": [], "config/database.php": [], "config/filesystems.php": [], "config/livewire.php": [], "config/locations.php": [], "config/logging.php": [], "config/mail.php": [], "config/media-library.php": [], "config/providers.php": [], "config/queue.php": [], "config/services.php": [], "config/session.php": [], "routes/api.php": {"routes": [{"method": "GET", "uri": "/orders/by-state"}]}, "routes/auth.php": {"routes": [{"method": "GET", "uri": "login"}, {"method": "GET", "uri": "register"}, {"method": "GET", "uri": "forgot-password"}, {"method": "GET", "uri": "reset-password/{token}"}, {"method": "GET", "uri": "verify-email"}, {"method": "GET", "uri": "verify-email/{id}/{hash}"}, {"method": "GET", "uri": "confirm-password"}, {"method": "POST", "uri": "logout"}]}, "routes/console.php": {"routes": []}, "routes/web.php": {"routes": [{"method": "GET", "uri": "/"}, {"method": "VIEW", "uri": "/about"}, {"method": "VIEW", "uri": "/contact"}, {"method": "POST", "uri": "/contact/submit"}, {"method": "GET", "uri": "/shop"}, {"method": "GET", "uri": "/category/{category:slug}"}, {"method": "GET", "uri": "/products/{product:slug}"}, {"method": "GET", "uri": "/search"}, {"method": "POST", "uri": "/product/{product:slug}/reviews"}, {"method": "GET", "uri": "/vendors/{vendor:slug}"}, {"method": "GET", "uri": "/cart"}, {"method": "POST", "uri": "/cart/add/{product}"}, {"method": "GET", "uri": "/pricing"}, {"method": "GET", "uri": "/become-a-vendor"}, {"method": "GET", "uri": "/store/{slug}"}, {"method": "GET", "uri": "/dashboard"}, {"method": "REDIRECT", "uri": "settings"}, {"method": "GET", "uri": "settings/profile"}, {"method": "GET", "uri": "settings/password"}, {"method": "GET", "uri": "settings/appearance"}, {"method": "GET", "uri": "/wishlist"}, {"method": "POST", "uri": "/wishlist/toggle/{product}"}, {"method": "POST", "uri": "/wishlist/add/{product}"}, {"method": "DELETE", "uri": "/wishlist/remove/{product}"}, {"method": "GET", "uri": "/orders"}, {"method": "GET", "uri": "/orders/{order}"}, {"method": "POST", "uri": "/orders/{order}/return"}, {"method": "GET", "uri": "/checkout"}, {"method": "GET", "uri": "/debug-checkout"}, {"method": "GET", "uri": "/test-checkout-minimal"}, {"method": "GET", "uri": "/debug-livewire-checkout"}, {"method": "POST", "uri": "/shipping/rates"}, {"method": "GET", "uri": "/payment/initialize"}, {"method": "POST", "uri": "/payment/paystack/initialize"}, {"method": "GET", "uri": "/payment/paystack/callback"}, {"method": "GET", "uri": "/checkout/success/{transaction_id?}"}, {"method": "GET", "uri": "/dashboard"}, {"method": "GET", "uri": "/onboarding"}, {"method": "GET", "uri": "/products"}, {"method": "GET", "uri": "/products/create"}, {"method": "GET", "uri": "/products/{product:slug}/edit"}, {"method": "GET", "uri": "/products/{product:slug}/variants"}, {"method": "DELETE", "uri": "/products/{product}"}, {"method": "GET", "uri": "/orders"}, {"method": "GET", "uri": "/orders/{order}"}, {"method": "GET", "uri": "/shipping"}, {"method": "GET", "uri": "/earnings"}, {"method": "GET", "uri": "/financials"}, {"method": "GET", "uri": "/profile"}, {"method": "GET", "uri": "/subscription"}, {"method": "GET", "uri": "/subscription/callback"}, {"method": "GET", "uri": "/reviews"}, {"method": "GET", "uri": "/settings"}, {"method": "GET", "uri": "/settings/profile"}, {"method": "GET", "uri": "/pending"}, {"method": "GET", "uri": "/"}, {"method": "GET", "uri": "/dashboard"}, {"method": "GET", "uri": "users"}, {"method": "GET", "uri": "users/create"}, {"method": "GET", "uri": "users/{user}"}, {"method": "GET", "uri": "users/{user}/edit"}, {"method": "DELETE", "uri": "users/{user}"}, {"method": "GET", "uri": "vendors"}, {"method": "GET", "uri": "vendors/create"}, {"method": "GET", "uri": "vendors/{vendor}"}, {"method": "GET", "uri": "vendors/{vendor}/edit"}, {"method": "DELETE", "uri": "vendors/{vendor}"}, {"method": "GET", "uri": "/products"}, {"method": "GET", "uri": "/products/create"}, {"method": "GET", "uri": "/products/{product:slug}"}, {"method": "GET", "uri": "/products/{product:slug}/edit"}, {"method": "GET", "uri": "/products/best-sellers"}, {"method": "GET", "uri": "brands"}, {"method": "GET", "uri": "categories"}, {"method": "GET", "uri": "colors"}, {"method": "GET", "uri": "sizes"}, {"method": "GET", "uri": "orders"}, {"method": "GET", "uri": "orders/{order}"}, {"method": "GET", "uri": "commissions"}, {"method": "GET", "uri": "subscriptions"}, {"method": "GET", "uri": "subscription-plans"}, {"method": "GET", "uri": "subscription-plans/create"}, {"method": "GET", "uri": "subscription-plans/{subscriptionPlan}/edit"}, {"method": "DELETE", "uri": "subscription-plans/{subscriptionPlan}"}, {"method": "GET", "uri": "payments"}, {"method": "GET", "uri": "withdrawals"}, {"method": "GET", "uri": "/settings"}, {"method": "GET", "uri": "/profile"}, {"method": "POST", "uri": "/paystack/webhook"}, {"method": "POST", "uri": "/paystack/subscription/webhook"}]}}