<?php

namespace App\Livewire;

use Livewire\Component;

use App\Models\Product;

class Search extends Component
{
    public string $query = '';

    public $results;

    public function mount()
    {
        $this->results = collect();
    }

    public function updatedQuery()
    {
        if (strlen($this->query) >= 2) {
            $this->results = Product::where('name', 'like', '%' . $this->query . '%')
                ->orWhere('description', 'like', '%' . $this->query . '%')
                ->take(5)
                ->get();
        } else {
            $this->results = collect();
        }
    }
    
    public function performSearch()
    {
        // This redirects to a full search results page when the user hits enter.
        return redirect()->route('products.search', ['query' => $this->query]);
    }

    public function render()
    {
        return view('livewire.search');
    }
}
