<?php
require __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Error;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ParserFactory;
use <PERSON>p<PERSON><PERSON><PERSON>\Node;
use Php<PERSON><PERSON>er\NodeVisitorAbstract;

class StructureVisitor extends NodeVisitorAbstract {
    public $structure = [];
    private $currentClass = null;
    private $currentNamespace = null;

    public function enterNode(Node $node) {
        if ($node instanceof Node\Stmt\Namespace_) {
            $this->currentNamespace = $node->name ? $node->name->toString() : null;
        } elseif ($node instanceof Node\Stmt\Class_) {
            $className = $node->name ? $node->name->name : 'anonymous@' . $node->getLine();
            $fullClassName = $this->currentNamespace ? $this->currentNamespace . '\\' . $className : $className;
            $this->currentClass = $fullClassName;
            $this->structure[$fullClassName] = [
                'type' => 'class',
                'methods' => [],
                'properties' => [],
            ];
        } elseif ($node instanceof Node\Stmt\ClassMethod && $this->currentClass) {
            $methodName = $node->name->name;
            $this->structure[$this->currentClass]['methods'][$methodName] = [
                'visibility' => $node->isPublic() ? 'public' : ($node->isProtected() ? 'protected' : 'private'),
                'static' => $node->isStatic(),
                'params' => array_map(fn($p) => '$' . ($p->var->name ?? 'unknown'), $node->params)
            ];
        } elseif ($node instanceof Node\Stmt\Property && $this->currentClass) {
            foreach ($node->props as $prop) {
                $this->structure[$this->currentClass]['properties'][] = '$' . $prop->name->name;
            }
        }
    }

    public function leaveNode(Node $node) {
        if ($node instanceof Node\Stmt\Class_ && $this->currentClass) {
            $this->currentClass = null;
        }
        if ($node instanceof Node\Stmt\Namespace_) {
            $this->currentNamespace = null;
        }
    }
}

function parse_blade_file($content) {
    $directives = [];
    preg_match_all('/@([a-zA-Z0-9_.-]+)(?:\s*\((.*?)\))?/s', $content, $matches, PREG_SET_ORDER);
    foreach ($matches as $match) {
        $directives[] = [
            'directive' => $match[1],
            'args' => isset($match[2]) ? trim($match[2], " \t\n\r\0\x0B'\"") : '',
        ];
    }
    return ['blade_directives' => $directives];
}

function parse_routes_from_ast($ast) {
    $routes = [];
    $visitor = new class extends NodeVisitorAbstract {
        public $routes = [];
        public function enterNode(Node $node) {
            if ($node instanceof Node\Expr\StaticCall && count($node->getArgs()) >= 2) {
                if ($node->class instanceof Node\Name && $node->class->toString() === 'Route') {
                    $method = $node->name->name;
                    $uri = $node->getArgs()[0]->value;
                    if ($uri instanceof Node\Scalar\String_) {
                        $this->routes[] = [
                            'method' => strtoupper($method),
                            'uri' => $uri->value,
                        ];
                    }
                }
            }
        }
    };
    $traverser = new PhpParser\NodeTraverser();
    $traverser->addVisitor($visitor);
    $traverser->traverse($ast);
    return $visitor->routes;
}

$structureMap = [];
$fileManifestPath = __DIR__ . '/file_manifest.json';

if (!file_exists($fileManifestPath)) {
    die("file_manifest.json not found!");
}

$fileManifest = json_decode(file_get_contents($fileManifestPath), true);
$files = array_keys($fileManifest);

$parser = (new ParserFactory)->createForNewestSupportedVersion();

foreach ($files as $file) {
    $filePath = __DIR__ . '/' . $file;
    if (!file_exists($filePath) || is_dir($filePath)) {
        continue;
    }

    $fileContent = file_get_contents($filePath);

    if (str_ends_with($file, '.blade.php')) {
        $structureMap[$file] = parse_blade_file($fileContent);
    } elseif (str_ends_with($file, '.php')) {
        try {
            $ast = $parser->parse($fileContent);
            $structureVisitor = new StructureVisitor();
            $traverser = new PhpParser\NodeTraverser();
            $traverser->addVisitor($structureVisitor);
            $traverser->traverse($ast);
            $fileStructure = $structureVisitor->structure;

            if (str_starts_with($file, 'routes/')) {
                $fileStructure['routes'] = parse_routes_from_ast($ast);
            }

            $structureMap[$file] = $fileStructure;
        } catch (Error $error) {
            $structureMap[$file] = ['error' => "Parse error: {$error->getMessage()}"];
        }
    }
}

file_put_contents(__DIR__ . '/structure_map.json', json_encode($structureMap, JSON_PRETTY_PRINT));

echo "structure_map.json generated successfully.";
