<?php

namespace App\Livewire\Auth\Vendor;

use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Register extends Component
{
    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';

    public $planId;
    public $plan;

    public function mount(Request $request)
    {
        $this->planId = $request->query('plan');
        if ($this->planId) {
            $this->plan = SubscriptionPlan::find($this->planId);
        } 

        if(!$this->plan){
            // CRITICAL FIX: Use correct Livewire redirect method
            $this->redirectRoute('pricing');
            return;
        }
    }

    public function register()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $vendorRole = Role::where('name', 'vendor')->first();
        if (!$vendorRole) {
            session()->flash('error', 'An internal error occurred. Please contact support.');
            return;
        }

        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
            'role_id' => $vendorRole->id,
        ]);

        // CRITICAL FIX SM2/AL3: Use is_approved as single source of truth
        $vendor = Vendor::create([
            'user_id' => $user->id,
            'business_name' => $this->name . "'s Store",
            'slug' => Str::slug($this->name . "'s Store"),
            'is_approved' => false, // Primary field for vendor approval
            'status' => 'pending', // Keep in sync for backward compatibility
            'has_completed_onboarding' => false,
        ]);



        Auth::login($user);

        // CRITICAL FIX AL4: Use correct Livewire redirect methods
        // Vendors should go to onboarding if not completed, otherwise to dashboard
        if (!$vendor->has_completed_onboarding) {
            $this->redirectRoute('vendor.onboarding');
        } else {
            $this->redirectRoute('vendor.dashboard');
        }
    }

    public function render()
    {
        return view('livewire.auth.vendor.register');
    }
}
